Mu Yuan
Website: https://yuanmu97.github.io/
Email: <EMAIL>
GitHub: github.com/yuanmu97


Education
  University of Science and Technology of China                                                    Hefei, China
  Ph.D. in Computer Science and Technology                                                   Sep. 2019–Jun. 2024
     – Advisor: Prof<PERSON> (ACM/IEEE Fellow) and Prof. <PERSON><PERSON>
     – Dissertation: Heterogeneous Collaborative Model Inference
     – USTC Doctoral Dissertation Award
  University of Science and Technology of China                                                    Hefei, China
  B.S. in Computer Science and Technology (Hua-Xia Talent Class)                             Sep. 2015–Jun. 2019
     – Thesis: Comprehensive and Eﬀicient Data Labelling via Adaptive Model Scheduling
     – USTC Outstanding Undergraduate Thesis Award


Experience
  The Chinese University of Hong Kong                                                        Hong Kong, China
  AIoT Lab, Postdoctoral Fellow, Prof<PERSON> (ACM/IEEE Fellow)                         Jul. 2024-Current

  SenseTime                                                                                      Beijing, China
  Intern Researcher, Deep Learning-Based Video Action Recognition Project                         Mar.-Jul. 2019

  University of Washington                                                                         Seattle, U.S.
  Summer Research Program                                                                          Jul.-Sep. 2017


Awards and Grants
• National Natural Science Foundation of China, Grant No.623B2093, RMB 300,000                         2024-2025
• ACM SenSys 2025, Best Paper Honorable Mention Award                                                       2025
• CCF Doctoral Dissertation Award (10 nationwide)                                                           2024
• CCF TCIoT Doctoral Dissertation Award (4 nationwide)                                                      2024
• ACM SenSys 2024, Best Demo Runner-up Award                                                                2024
• CAS President Award                                                                                       2024
• ByteDance Scholars (13 nationwide)                                                                        2023
• National Scholarship                                                                           2020/2022/2023
• SenseTime Scholarship (22 nationwide)                                                                     2018
• Grand Price (1 out of 1530 teams) of the 4th National University Cloud Computing Contest                  2018


Academic Services
• General co-chair of ACM ANAI Workshop 2025 (co-located with ACM MobiCom 2025)
• Reviewer of ACM IMWUT, IEEE INFOCOM, IEEE TMC, IEEE IoTJ, AAAI, NeurIPS


                                                                                                     Page 1 of 3
Publications
1. Mu Yuan, Lan Zhang, Yihang Cheng, Miao-Hui Song, Guoliang Xing, Xiang-Yang Li. STIP: Three-Party
    Privacy-Preserving and Lossless Inference for Large Transformers in Production. In The Network and Distributed
    System Security (NDSS) Symposium. 2026.

 2. Mu Yuan, Lan Zhang, Liekang Zeng, Siyang Jiang, Bufang Yang, Di Duan, Guoliang Xing. SCX: Stateless
    KV-Cache Encoding for Cloud-Scale Confidential Transformer Serving. In ACM SIGCOMM Conference. 2025.

 3. Liekang Zeng, Yunchao Liu, Shengyuan Ye, Mu Yuan, Di Duan, Xu Chen, Guoliang Xing. Grape: Eﬀicient
    Spatiotemporal Prediction Services with Stale Sensing Streams. In The IEEE Real-Time Systems Symposium
    (RTSS). 2025.

 4. Siyang Jiang, Bufang Yang, Lilin Xu, Mu Yuan, Yeerzhati Abudunuer, Kaiwei Liu, Liekang Zeng, Hongkai Chen,
    Xiaofan Jiang, Zhenyu Yan, Guoliang Xing. LLM-Driven Low-Resolution Vision System for On-Device Human
    Behavior Understanding. In ACM MobiCom. 2025.

 5. Yuting He, Xinyan Wang, Mu Yuan, Bufang Yang, Siyang Jiang, Yihua Huang, Doris S. F. Yu, Guoliang Xing,
    Hongkai Chen. Myo-Trainer: A Vision-based Muscle-Aware Motion Feedback System for In-Home Resistance
    Training. In ACM MobiCom. 2025.

 6. Mu Yuan, Lan Zhang, Di Duan, Liekang Zeng, Miao-Hui Song, Zichong Li, Guoliang Xing, Xiang-Yang Li.
    Mitigating Tail Latency for on-Device Inference with Load-Balanced Heterogeneous Models. In IEEE Transactions on
    Mobile Computing. 2025.

 7. Yihang Cheng, Lan Zhang, Junyang Wang, Mu Yuan, Yunhao Yao. RemoteRAG: A Privacy-Preserving LLM Cloud
    RAG Service. In Findings of the Association for Computational Linguistics (ACL). 2025.

 8. Puhan Luo, Jiahui Hou, Haisheng Tan, Mu Yuan, Guangyu Wu, Kaiwen Guo, Zhiqiang Wang, XiangYang Li.
    ChannelZip: SLO-aware channel compression for task-adaptive model serving on IoT devices. In ACM Transactions
    on Sensor Networks. 2025.

 9. Di Duan, Shengzhe Lyu, Mu Yuan, Hongfei Xue, Tianxing Li, Weitao Xu, Kaishun Wu, Guoliang Xing. Argus:
    Multi-view egocentric human mesh reconstruction based on stripped-down wearable mmwave add-on. In Proceedings
    of the 23rd ACM Conference on Embedded Networked Sensor Systems. 2025.

10. Junyang Zhang, Mu Yuan, Ruiguang Zhong, Puhan Luo, Huiyou Zhan, Ningkang Zhang, Chengchen Hu,
    Xiang-Yang Li. A-VL: Adaptive Attention for Large Vision-Language Models. In Proceedings of the AAAI
    Conference on Artificial Intelligence. 2025.

11. Yunhao Yao, Jiahui Hou, Mu Yuan, Haiyue Zhang, Zhengyuan Xu, Xiang-Yang Li. TraﬀicDiary: User Attribute
    Inference Based on Smart Home Traﬀic Traces. In ACM Transactions on Internet Technology. 2025.

12. Mu Yuan, Lan Zhang, Yunhao Yao, Junyang Zhang, Puhan Luo, Xiang-Yang Li. Resource-Eﬀicient Model Inference
    for AIoT: A Survey. In the Chinese Journal of Computers. 2024.

13. Yunhao Yao, Jiahui Hou, Guangyu Wu, Yihang Cheng, Mu Yuan, Puhan Luo, Zhiqiang Wang, Xiang-Yang Li.
    SecoInfer: Secure DNN End-Edge Collaborative Inference Framework Optimizing Privacy and Latency. In ACM
    Transactions on Sensor Networks. 2024.

14. Puhan Luo, Jiahui Hou, Mu Yuan, Guangyu Wu, Yunhao Yao, Xiang-Yang Li. F2Zip: Finetuning-free model
    compression for scenario-adaptive embedded vision. In Proceedings of the 22nd ACM Conference on Embedded
    Networked Sensor Systems. 2024.

15. Yuting He, Xinyan Wang, Mu Yuan, Di Duan, Doris SF Yu, Guoliang Xing, Hongkai Chen. Demo: Myotrainer:
    Muscle-Aware Motion Analysis and Feedback System for In-Home Resistance Training. In Proceedings of the 22nd
    ACM Conference on Embedded Networked Sensor Systems. 2024.

16. Ningkang Zhang, Guangyu Wu, Chao Gu, Mu Yuan, Xiang-Yang Li. FusionFlow: Neural Fusion and Compression
    for Communication-Eﬀicient Edge-Cloud Collaborative Computing. In International Conference on Wireless Artificial
    Intelligent Computing Systems and Applications. 2024.


                                                                                                         Page 2 of 3
17. Junyang Wang, Lan Zhang, Junhao Wang, Mu Yuan, Yihang Cheng, Qian Xu, Bo Yu. GraphProxy:
    Communication-eﬀicient federated graph learning with adaptive proxy. In IEEE INFOCOM. 2024.

18. Mu Yuan, Lan Zhang, Xuanke You, and Xiang-Yang Li. PacketGame: Multi-Stream Packet Gating for Concurrent
    Video Inference at Scale. In ACM SIGCOMM Conference. 2023.

19. Mu Yuan, Lan Zhang, Fengxiang He, Xueting Tong, Zhenyuan Xu, and Xiang-Yang Li. InFi: End-to-End Learning
    to Filter Input for Resource-Eﬀiciency in Mobile-Centric Inference. In IEEE Transactions on Mobile Computing
    (TMC). 2023.

20. Mu Yuan, Lan Zhang, Zimu Zheng, Yi-Nan Zhang, and Xiang-Yang Li. MLink: Linking Black-Box Models from
    Multiple Domains for Collaborative Inference. In IEEE Transactions on Pattern Analysis and Machine Intelligence
    (TPAMI). 2023.

21. Miao-Hui Song, Lan Zhang, Mu Yuan, Zichong Li, Qi Song, Yijun Liu, Guidong Zheng. Cotel: Ontology-neural
    co-enhanced text labeling. In Proceedings of the ACM Web Conference. 2023.

22. Zichong Li, Lan Zhang, Mu Yuan, Miao-Hui Song, and Qi Song. Eﬀicient Deep Ensemble Inference via Query
    Diﬀiculty-dependent Task Scheduling. In IEEE ICDE Conference. 2023.

23. Lan Zhang, Daren Zheng, Mu Yuan, Feng Han, Zhengtao Wu, Mengjing Liu, and Xiang-Yang Li. MultiSense:
    Cross-labelling and Learning Human Activities Using Multimodal Sensing Data. In ACM Transactions on Sensor
    Networks (TOSN). 2023.

24. Mu Yuan, Lan Zhang, Fengxiang He, Xueting Tong, and Xiang-Yang Li. InFi: End-to-End Learnable Input Filter
    for Resource-Eﬀicient Mobile-Centric Inference. In ACM MobiCom Conference. 2022.

25. Mu Yuan, Lan Zhang, Xiang-Yang Li, Lin-Zhuo Yang, and Hui Xiong. Adaptive Model Scheduling for
    Resource-eﬀicient Data Labeling. In ACM Transactions on Knowledge Discovery from Data (TKDD). 2022.

26. Mu Yuan, Lan Zhang, and Xiang-Yang Li. MLink: Linking Black-Box Models for Collaborative Multi-Model
    Inference. In AAAI Conference. 2022. (Oral Presentation 4.5%)

27. Xuanke You, Lan Zhang, Haikuo Yu, Mu Yuan, and Xiang-Yang Li. KATN: Key activity detection via inexact
    supervised learning. In ACM Ubicomp Conference. 2021.

28. Lan Zhang, Mu Yuan, Daren Zheng, Xiang-Yang Li. M&M: Recognizing Multiple Co-evolving Activities from
    Multi-Source Videos. In 17th International Conference on Distributed Computing in Sensor Systems (DCOSS). 2021.

29. Mu Yuan, Lan Zhang, Zhengtao Wu, and Daren Zheng. High-quality Activity-Level Video Advertising. In
    IEEE/ACM IWQoS Conference. 2020.

30. Mu Yuan, Lan Zhang, Xiang-Yang Li, and Hui Xiong. Comprehensive and Eﬀicient Data Labeling via Adaptive
    Model Scheduling. In IEEE ICDE Conference. 2020.




                                                                                                         Page 3 of 3
Project Goal
The overarching goal of this project is to design and develop a next-generation edge–cloud
collaborative infrastructure for memory-augmented LLM serving, enabling intelligent,
secure, and personalized edge agents. By addressing the fundamental limitations of current
edge-only [1,2,3] and cloud-only [4,5,6] approaches, the project aims to deliver low-latency,
privacy-preserving, and adaptive LLM services in real-world edge environments, with a focus
on long-term autonomy and societal applications such as healthcare and wellness [7,8].
This goal is both ambitious—pioneering an unexplored direction that unifies memory, secure
serving, and edge–cloud collaboration—and realistic, given my prior contributions on secure
LLM inference (SCX, SIGCOMM 2025; STIP, NDSS 2026) and successful industrial deployment
of STIP in Huawei’s MindSpore framework.

Specific Objectives
1. Collaborative Memory Management
    Design mechanisms to partition, synchronize, and optimize short-term and long-term LLM
     memory across edge and cloud resources.
    Enable real-time recall and personalization at the edge while leveraging cloud-scale memory
     for knowledge retention.
2. Secure and Efficient Serving Infrastructure
    Develop privacy-preserving serving mechanisms tailored to edge agents such as personal
     health assistants, where data involve highly sensitive personal and medical information.
    Design fine-grained privacy controls that distinguish between local (edge) personal data and
     shared (cloud) knowledge, ensuring that health records, biosignals, or conversational history
     remain confidential while still enabling global model improvement.
    Optimize bandwidth and computational costs under these constraints, allowing secure yet
     efficient model–agent interactions in resource-limited edge environments.
3. Agent-Centric Adaptation
    Build system support for edge agents that require persistent personalization and context
     continuity.
    Explore lightweight fine-tuning and retrieval mechanisms to tailor agent behavior to
     individual users while maintaining global consistency through cloud coordination.
4. Prototype Deployment and Validation
    Implement a working prototype on real edge–cloud platforms, with healthcare-inspired agent
     scenarios as representative testbeds.
    Advance the project from proof-of-concept (TRL3) to prototype demonstration in relevant
     environments (TRL6).

Expected Change and Impact
The project will create a paradigm shift from cloud-centric AI services to edge–cloud
collaborative, memory-augmented LLM serving for edge agentic applications. This change will
enable trustworthy, real-time, and personalized intelligence at the edge, unlocking new
possibilities for health, wellness, and beyond. It will also establish a general-purpose infrastructure
that can serve as the foundation for future research and industry adoption.
Background
Large Language Models (LLMs) have rapidly progressed from text generators to autonomous
agents [9] capable of reasoning, planning, and interacting with users in natural language. At the
same time, the edge computing paradigm has gained momentum, especially in health and wellness
applications [10] where responsiveness, personalization, and privacy are critical. The convergence
of these two trends points to a new frontier: edge-based LLM agents that can operate close to
users, providing long-term assistance while safeguarding sensitive data.
Despite the growing potential, current infrastructures [1-6] are not designed for this emerging
paradigm. Today’s LLM services are primarily cloud-centric, optimized for general-purpose text
generation rather than persistent memory, personalized adaptation, and secure serving for
edge agents. Health-related data, such as biosignals, medical records, and conversational history,
raise particularly strong privacy concerns [11] that existing cloud-only approaches fail to address.
Without a new infrastructure, the deployment of trustworthy edge agents will remain infeasible.

The timeliness of this project is underpinned by three converging factors:
1. Technology readiness: Foundational work on secure inference (e.g., STIP [12], SCX [13]) has
proven feasible at scale and has even been adopted by industry platforms such as Huawei’s
MindSpore. This creates a unique opportunity to build the next layer of infrastructure specifically
for edge agents.
2. Societal demand: The rise of AI assistants in healthcare, wellness, and daily life highlights an
urgent need for privacy-preserving, real-time, and adaptive edge intelligence. Recent global
discussions on AI regulation [14] further stress the importance of trustworthy design.
3. Research momentum: Memory-augmented LLMs and agent architectures [18,19] are rapidly
evolving, yet system support for edge–cloud collaborative memory and serving is still missing.
Advancing this direction now ensures leadership in a fast-emerging area.

Novelty
Most existing research on LLM infrastructure focuses on cloud-only serving, targeting efficient
inference (e.g., quantization [15], KV cache optimization [4,5], speculative decoding [16]) or
fine-tuning [17] at scale. These approaches largely treat each query in isolation, overlooking the
memory continuity, personalization, and data privacy that are indispensable for agent-style
applications. On the industrial side, companies are exploring agent frameworks [20,21], but these
rely almost entirely on cloud-based APIs, with limited consideration of edge deployment,
collaborative memory, or sensitive data handling.
This project introduces a paradigm shift from cloud-only LLM services to edge–cloud
collaborative, memory-augmented serving tailored for edge agents. Its novelty lies in:
    Collaborative memory management: Partitioning and synchronizing short-term and
     long-term memory across edge and cloud.
    Privacy-aware serving for edge agents: Designing secure serving infrastructure specifically
     for sensitive personal and medical data, beyond generic confidentiality solutions.
    Agent-centric adaptation: Enabling continuity and personalization for edge agents through
     lightweight fine-tuning and retrieval, rather than treating each request in isolation.
By combining these innovations, the project addresses a timely research gap and lays the
foundation for the next generation of trustworthy, real-time, and adaptive edge agents.
The proposed project will design and implement an edge–cloud collaborative infrastructure for
memory-augmented LLM serving, focusing on enabling secure, efficient, and personalized edge
agents. The methodology is organized around three core research thrusts: collaborative memory
management, secure and efficient LLMs serving for edge agents, and agent-centric adaptation.
Each thrust contains specific research tasks, methodological approaches, and milestones to
measure progress.

1. Collaborative Memory Management
Objective
Enable edge agents to maintain short-term personalization while leveraging cloud-scale memory
for long-term knowledge, through a systematic partitioning and synchronization mechanism.
Methodology
The first research thrust focuses on the design of collaborative memory management for
edge–cloud LLM agents. Unlike conventional question-answering services that treat every new
request session as stateless, an edge agent must preserve both short-term and long-term memory to
support context continuity and personalization [22]. To achieve this, the project will design a
hierarchical LLM memory architecture in which recent interactions, user-specific preferences,
and transient states are maintained on the edge, while the cloud stores aggregated long-term
knowledge and performs large-scale indexing. The central challenge lies in determining how to
partition memory across these two tiers and how to synchronize them efficiently under relatively
limited edge resource constraints.
This research will begin with the development of a memory abstraction layer [23] that decouples
local and global memory functions. On top of this abstraction, an adaptive memory
synchronization mechanisms will be designed to control when and how edge states are shared
with the cloud. For example, frequently used personal context will remain at the edge to reduce
latency and avoid exposure of sensitive data, while compressed or anonymized summaries may be
uploaded to the cloud for long-term retention. Hybrid memory retrieval strategies will also be
investigated, enabling the agent to respond quickly using cached local knowledge while querying
the cloud only for complex or rarely used information. The design will be extensively validated
through simulations and prototype deployments on edge–cloud testbeds, measuring latency,
memory recall accuracy, and scalability as the number of concurrent agents grows. By the end of
this thrust, the project will deliver a validated mechanism for collaborative memory that ensures
both efficiency and usability for real-world edge agents.
Milestones
Prototype memory abstraction layer with basic partitioning strategy.
Adaptive synchronization algorithms validated in laboratory environments.
Demonstration of collaborative memory across >100 simulated agents with latency <100ms.

2. Secure and Efficient Serving for Edge Agents
Objective
Design privacy-preserving serving mechanisms tailored to sensitive edge agents, such as personal
health assistants, where data confidentiality is paramount.
Methodology
The second thrust addresses the pressing need for secure and efficient serving [12,13,14] in edge
agents, with particular relevance to applications in healthcare and personal wellness [8,10]. Unlike
generic secure inference frameworks that only protect model weights or user inputs, edge agents
must deal with highly heterogeneous and sensitive personal data [11], including biosignals,
medical records, and long-term dialogue histories. This creates new privacy risks because memory
synchronization and continuous serving expose far richer context than one-off queries. A central
methodological task in this research is to define a threat model that captures these unique
vulnerabilities and to design serving mechanisms that mitigate them.
Building on our prior work in secure inference, such as SCX [13] and STIP [12], this project will
design an edge–cloud serving architecture that incorporates layered privacy controls. Sensitive
health data processed at the edge will never leave the device in raw form; instead, encoding or
lightweight encryption schemes will be applied before synchronization. The project will explore
block-based cryptographic encoding [24] and bandwidth-aware transmission strategies [25]
to ensure that the system remains feasible even on resource-constrained devices and intermittent
networks. These mechanisms will be coupled with adaptive scheduling that balances
confidentiality with response time, allowing the system to dynamically adjust offloading decisions
depending on network conditions and user-defined privacy preferences.
The methodology will culminate in a prototype that demonstrates privacy-preserving serving in
health-inspired scenarios. For example, a personal health agent will be able to process biosignals
locally, selectively share secure embeddings with the cloud, and still provide accurate and timely
responses. This thrust will be evaluated against cloud-only serving baselines [4-6], showing that
strong privacy guarantees can be achieved without unacceptable losses in latency or accuracy.
Milestones
Defined privacy requirements and threat model for edge agents.
Prototype encoding mechanisms validated on small-scale LLM serving workloads.
Demonstration of secure serving in a health-inspired agent scenario, with end-to-end latency
comparable to baseline cloud-only systems.

3. Agent-Centric Adaptation
Objective
Enable edge agents to adapt to individual users while benefiting from global improvements,
supporting long-term continuity and personalization.
Methodology
The third thrust focuses on enabling edge agents to adapt to users over time, maintaining
personalization and continuity across interactions [9]. Today’s cloud-based question answering
services operate in a stateless manner, treating each request session independently and discarding
user-specific context. For edge agents to be truly useful, they must incorporate mechanisms for
incremental personalization and long-term continuity, while respecting privacy constraints. The
project will therefore explore lightweight LLM personalization methods that can be deployed
on edge devices without retraining full models. Parameter-efficient approaches such as adapters
[26] or low-rank fine-tuning [27] will be extended to the edge setting, enabling agents to adjust
behavior to individual users with limited computation.
In addition to parameter adaptation, the research will address continuity of context [28]. Rather
than sending personal histories to the cloud, edge devices will maintain secure local embeddings
that summarize prior interactions. These embeddings may be selectively shared with the cloud
under user-controlled policies, enabling the agent to preserve context across sessions without
compromising sensitive data. Edge-cloud collaborative retrieval-augmented generation [29]
will be adapted to this hybrid design: local stores will contain personal documents and histories,
while the cloud provides large-scale general knowledge. A key challenge lies in integrating these
two sources into a seamless context pipeline that supports efficient and accurate inference.
Evaluation of this thrust will involve controlled experiments measuring personalization accuracy,
response latency, and resistance to privacy leakage. Ultimately, the project will deliver a fully
integrated prototype edge agent capable of persistent personalization and memory continuity. This
prototype will serve as a proof that agent-centric adaptation, when combined with collaborative
memory and secure serving, can significantly enhance the usability, trustworthiness, and
long-term engagement of edge LLM agents.
Milestones
Initial design of lightweight fine-tuning for edge devices.
Prototype retrieval-based personalization system validated in lab setting.
Fully integrated agent with persistent memory continuity demonstrated in operational edge–cloud
environment.

International Context and Differentiation
Similar Research
Recent international efforts on LLM systems can be broadly grouped into three categories.
1. Cloud-only LLM optimization has focused on improving inference efficiency through
techniques such as quantization [15], KV-cache optimization [4,5], and speculative decoding [16].
These approaches have achieved significant throughput gains, but inherently overlooking the
personalization, long-term memory, and privacy requirements critical to agent applications.
2. Federated and privacy-preserving ML frameworks, such as federated learning [31], secure
multi-party computation [30], and homomorphic encryption [32], demonstrate how sensitive data
can be protected during model training or inference. However, these systems are not designed for
edge agents: their secure inference mechanisms often operate at extremely low throughput
(minutes per token) and provide no protection for agent memory, making them impractical for
long-context, real-time applications.
3. Agent frameworks from industry, including AutoGLM [20] and MobileAgent [21], highlight
the growing interest in orchestration of LLM agents. Yet, these systems rely almost exclusively on
cloud APIs and lack the supporting infrastructure for edge deployment, collaborative memory
management, or privacy-aware serving.
Differentiation
This project pioneers a new direction by targeting memory-augmented serving for edge agents
with a privacy-aware, edge–cloud collaborative design. In contrast to existing work, it:
    Explicitly integrates short-term memory at the edge with long-term memory in the cloud,
     achieving a balance between latency, scalability, and personalization.
    Provides agent application-specific secure serving mechanisms tailored to sensitive domains
     such as personal health assistants, rather than relying on generic confidentiality solutions.
    Enables persistent personalization and continuity across sessions, giving edge agents the
     ability to maintain user-specific context over time—capabilities entirely absent from today’s
     stateless cloud-based APIs.
        Gantt Chart:
                                                           Year-1                              Year-2                               Year-3
        Activity                                Q1       Q2      Q3        Q4       Q1       Q2      Q3         Q4       Q1       Q2      Q3        Q4
       Collaborative Memory Management
       M1. Prototype abstraction
       M2. Adaptive sync validated
       M3. Demo across >100 agents
       Secure & Efficient Serving
       M1. Threat model defined
       M2. Prototype encoding validated
       M3. Health agent demo
       Agent-Centric Adaptation
       M1. Initial fine-tuning design
       M2. Retrieval-based personalization
       M3. Full integration demo
       System Integration
       M1. Final integrated system demo
       M2. Operational edge-cloud system
       Annual Reports

Timeline Description
The project will be carried out over three years in four major thrusts: collaborative memory management, secure and ef cient serving, agent-centric adaptation,
and system integration.
In Year 1, the emphasis will be on laying the foundation for collaborative memory management and secure serving. The rst milestones include building a prototype
memory abstraction layer, validating adaptive synchronization algorithms, and demonstrating collaborative memory across more than 100 simulated agents (Y1 Q1–
Q2). In parallel, privacy requirements and the threat model for edge agents will be de ned, prototype encoding mechanisms will be validated, and a health-inspired
secure serving demo will be delivered (Y1 Q1–Q3).
In Year 2, the focus shifts to personalization and adaptation of edge agents. The initial design of lightweight ne-tuning for edge devices will be completed in the rst
half of the year (Y2 Q1–Q2), followed by a retrieval-based personalization prototype in the second half (Y2 Q3–Q4). These developments will build directly on the
secure and memory-enabled serving infrastructure established in Year 1.
In Year 3, all research thrusts will converge into a nal integrated system. Early in the year, a full integration demo of agent-centric adaptation will be delivered (Y3
Q1). This will be followed by the demonstration of the nal integrated system (Y3 Q2) and the deployment of an operational edge–cloud prototype system (Y3 Q3–
Q4), showcasing persistent memory continuity, secure serving, and personalization in realistic environments.
Annual reports will be submitted at the end of each project year (by 31 October), summarizing progress and outlining plans for the following stage.
Figure 1. Overview of the proposed edge-cloud collaborative infrastructure for memory-augmented edge LLM
agents. The framework is structured around three key research thrusts: (I) Collaborative Agent Memory
Management, which partitions and synchronizes short-term memory at the edge with long-term memory in the
cloud; (II) Collaborative Secure and Efficient Agent Serving, which enables privacy-preserving and low-latency
inference across heterogeneous environments; and (III) Collaborative Agent-Centric Adaptation, which supports
lightweight personalization, and retrieval-augmented generation. Together, these components form a scalable,
trustworthy infrastructure enabling real-time, personalized, and secure edge agents.




Figure 2. A region-scale edge AI testbed for Alzheimer’s disease research that the applicant is currently
participating in and will have access to in the future. The testbed is designed to analyze multiple digital biomarkers
of dementia, including activities of daily living, behavioral and psychological symptoms, and motor functions. It
consists of 97 sensor nodes deployed in the homes of elderly participants, connected to a central server in the
laboratory. This deployment provides a large-scale, real-world environment for developing and validating edge AI
techniques, and serves as a representative application scenario for the proposed edge agent infrastructure.
Huawei Hong Kong Research Center
For industry collaboration, I will continue to build on our established relationship with Huawei
Hong Kong Research Center, a leading technology research hub in the region. My past
engagement with Huawei has already facilitated the translation of cutting-edge research into
practice—for example, our joint work on privacy-preserving LLM serving has been integrated
into Huawei’s open-source MindSpore MindArmour framework and recognized in international
venues. Such collaboration demonstrates a proven pathway for research outcomes to influence
industry solutions and reach wider adoption.

During the Fellowship period, I will maintain close interaction with Huawei Hong Kong through
regular technical discussions, joint workshops, and research exchanges. These engagements will
ensure that the project remains aligned with emerging industry needs, while at the same time
allowing academic insights to inform real-world system development. Importantly, the
collaboration will serve as a channel for testing new ideas against practical constraints, thereby
strengthening the translational value of the research.

This partnership is designed not only to maximize the practical impact of the Fellowship outcomes
but also to explore new opportunities for knowledge transfer, product demonstrations, and
technology adoption. With structured collaboration and clear alignment between academic and
industrial objectives, Huawei Hong Kong will play a critical role in actualizing the pledged impact
of this project, bridging the gap between fundamental research and real-world applications.
Translational Impact
The proposed project will deliver edge–cloud collaborative infrastructure for memory-augmented
LLM serving, enabling secure, efficient, and personalized edge agents. While the academic
contributions lie in advancing system design and algorithms, the broader translational impact
extends across multiple dimensions of society, economy, and public services.

1. Impact on Health and Quality of Life
One of the most direct beneficiaries of this research will be healthcare and wellness services. Edge
agents, such as personal health assistants, can analyze biosignals, medical records, and
conversational history to provide real-time guidance for patients, elderly individuals, or people
with chronic diseases. By ensuring that sensitive information is processed securely at the edge, the
project will empower users to receive personalized and trustworthy health advice without
exposing private data to untrusted cloud platforms.
In the context of an aging society and rising healthcare costs, such agents can reduce the burden
on medical staff by enabling continuous remote monitoring, early anomaly detection, and
proactive interventions. For elderly care, edge agents can serve as daily companions, offering
reminders, monitoring conditions, and alerting caregivers when necessary. By combining LLM
capabilities with privacy-preserving infrastructure, the project directly contributes to improving
individual well-being and the quality of life.

2. Impact on Economy and Industry
The project addresses one of the most pressing industrial challenges of deploying LLMs: high cost,
inefficiency, and lack of trust. By developing collaborative memory and secure serving techniques,
the research lowers the barrier for enterprises to integrate LLM-powered agents into their services.
Telecommunications companies, healthcare providers, and IoT manufacturers stand to benefit
from a scalable infrastructure that allows local personalization with global intelligence, reducing
bandwidth usage and operational expenses.
In particular, the collaboration with Huawei and the successful integration of STIP into
MindSpore MindArmour demonstrate the project’s potential for rapid industrial adoption. Future
commercialization pathways may involve integrating the proposed infrastructure into open-source
frameworks, licensing components for healthcare and wellness providers, or enabling start-ups to
build specialized edge agent services. The resulting ecosystem has the potential to generate new
markets for edge AI agents, stimulate economic growth, and foster industrial innovation in Hong
Kong and beyond.

3. Impact on Public Services and Policy
Beyond healthcare and industry, the project has implications for public policy and trust in AI.
Current debates on AI governance emphasize the need for privacy, transparency, and
accountability. By embedding privacy-preserving design principles into the infrastructure, the
project provides a concrete demonstration of how large-scale AI systems can comply with
emerging regulations such as GDPR or China’s Personal Information Protection Law (PIPL).
Furthermore, edge–cloud collaborative agents can be deployed in public service domains such as
education, smart cities, and transportation. For example, personalized learning assistants can
operate on student devices without leaking sensitive academic data, while city-scale agents can
provide real-time environmental or mobility insights with privacy safeguards. These applications
showcase how research outcomes can directly support policy objectives for responsible and
citizen-centric AI deployment.

4. Societal and Cultural Impact
LLM-based edge agents will influence not only the economy and policy but also the way
individuals interact with technology. The shift from “stateless query answering” to persistent,
memory-augmented agents represents a cultural transformation: users will no longer see AI as a
detached tool, but as a trustworthy assistant embedded in daily life. Ensuring privacy and security
is crucial to building this trust. By addressing these concerns from the ground up, the project helps
cultivate public confidence in AI technologies, which is essential for their long-term acceptance
and responsible use.
This cultural shift is particularly relevant in healthcare, where patients are often reluctant to adopt
digital tools due to fear of surveillance or data misuse. Demonstrating a secure, edge-based
alternative empowers individuals to embrace AI while maintaining control over their personal data,
aligning with global trends toward human-centric AI design.

5. Environmental Impact
By optimizing the distribution of computation and memory between edge and cloud, the project
will also contribute to reducing the environmental footprint of large-scale AI services. Edge-side
processing minimizes unnecessary data transmission and reduces reliance on energy-intensive
cloud data centers. This aligns with global sustainability goals and supports the development of
green AI infrastructure.

6. Overall Expected Benefits
The translational impact of this project can be summarized as:
    Health and Quality of Life: Real-time, privacy-preserving personal health agents that
     improve well-being and reduce healthcare costs.
    Economy and Industry: New infrastructure for LLM agents, enabling commercialization
     opportunities and lowering costs for enterprises.
    Public Policy and Services: Concrete frameworks supporting AI governance, regulatory
     compliance, and public trust.
    Society and Culture: A paradigm shift in human–AI interaction, from stateless services to
     persistent, trustworthy agents.
    Environment: Energy-efficient edge–cloud collaboration reducing the carbon footprint of AI.

By bridging fundamental research with industrial deployment and societal needs, this project will
demonstrate a clear pathway from academic innovation to real-world impact, ensuring that the
benefits of LLM agents extend well beyond the research community.
[1] Sheng, Ying, Lianmin Zheng, Binhang Yuan, Zhuohan Li, Max Ryabinin, Beidi Chen, Percy Liang,
Christopher Ré, Ion Stoica, and Ce Zhang. "Flexgen: High-throughput generative inference of large language
models with a single gpu." In International Conference on Machine Learning, pp. 31094-31116. PMLR, 2023.
[2] Song, Yixin, Zeyu Mi, Haotong Xie, and Haibo Chen. "Powerinfer: Fast large language model serving with a
consumer-grade gpu." In Proceedings of the ACM SIGOPS 30th Symposium on Operating Systems Principles, pp.
590-606. 2024.
[3] Liu, Zechun, Changsheng Zhao, Forrest Iandola, Chen Lai, Yuandong Tian, Igor Fedorov, Yunyang Xiong et
al. "Mobilellm: Optimizing sub-billion parameter language models for on-device use cases." In Forty-first
International Conference on Machine Learning. 2024.
[4] Kwon, Woosuk, Zhuohan Li, Siyuan Zhuang, Ying Sheng, Lianmin Zheng, Cody Hao Yu, Joseph Gonzalez,
Hao Zhang, and Ion Stoica. "Efficient memory management for large language model serving with
pagedattention." In Proceedings of the 29th symposium on operating systems principles, pp. 611-626. 2023.
[5] Qin, Ruoyu, Zheming Li, Weiran He, Jialei Cui, Feng Ren, Mingxing Zhang, Yongwei Wu, Weimin Zheng,
and Xinran Xu. "Mooncake: Trading more storage for less computation—a {KVCache-centric} architecture for
serving {LLM} chatbot." In 23rd USENIX Conference on File and Storage Technologies (FAST 25), pp. 155-170.
2025.
[6] Zhong, Yinmin, Shengyu Liu, Junda Chen, Jianbo Hu, Yibo Zhu, Xuanzhe Liu, Xin Jin, and Hao Zhang.
"{DistServe}: Disaggregating prefill and decoding for goodput-optimized large language model serving." In 18th
USENIX Symposium on Operating Systems Design and Implementation (OSDI 24), pp. 193-210. 2024.
[7] Friha, Othmane, Mohamed Amine Ferrag, Burak Kantarci, Burak Cakmak, Arda Ozgun, and Nassira
Ghoualmi-Zine. "Llm-based edge intelligence: A comprehensive survey on architectures, applications, security
and trustworthiness." IEEE Open Journal of the Communications Society (2024).
[8] Ouyang, Xiaomin, Xian Shuai, Yang Li, Li Pan, Xifan Zhang, Heming Fu, Sitong Cheng et al. "ADMarker: A
Multi-Modal Federated Learning System for Monitoring Digital Biomarkers of Alzheimer's Disease." In
Proceedings of the 30th Annual International Conference on Mobile Computing and Networking, pp. 404-419.
2024.
[9] Li, Yuanchun, Hao Wen, Weijun Wang, Xiangyu Li, Yizhen Yuan, Guohong Liu, Jiacheng Liu et al.
"Personal llm agents: Insights and survey about the capability, efficiency and security." arXiv preprint
arXiv:2401.05459 (2024).
[10] Amin, Syed Umar, and M. Shamim Hossain. "Edge intelligence and Internet of Things in healthcare: A
survey." IEEE access 9 (2020): 45-59.
[11] Bommareddy, Sahithi, Javed Ahmad Khan, and Rohit Anand. "A review on healthcare data privacy and
security." Networking technologies in smart healthcare (2022): 165-187.
[12] Mu Yuan, Lan Zhang, Yihang Cheng, Miao-Hui Song, Guoliang Xing, Xiang-Yang Li. STIP: Three-Party
Privacy-Preserving and Lossless Inference for Large Transformers in Production. In The Network and Distributed
System Security (NDSS) Symposium. 2026.
[13] Mu Yuan, Lan Zhang, Liekang Zeng, Siyang Jiang, Bufang Yang, Di Duan, Guoliang Xing. SCX: Stateless
KV-Cache Encoding for Cloud-Scale Confidential Transformer Serving. In ACM SIGCOMM Conference. 2025.
[14] Díaz-Rodríguez, Natalia, Javier Del Ser, Mark Coeckelbergh, Marcos López De Prado, Enrique
Herrera-Viedma, and Francisco Herrera. "Connecting the dots in trustworthy Artificial Intelligence: From AI
principles, ethics, and key requirements to responsible AI systems and regulation." Information Fusion 99 (2023):
101896.
[15] Lin, Ji, Jiaming Tang, Haotian Tang, Shang Yang, Wei-Ming Chen, Wei-Chen Wang, Guangxuan Xiao,
Xingyu Dang, Chuang Gan, and Song Han. "Awq: Activation-aware weight quantization for on-device llm
compression and acceleration." Proceedings of machine learning and systems 6 (2024): 87-100.
[16] Leviathan, Yaniv, Matan Kalman, and Yossi Matias. "Fast inference from transformers via speculative
decoding." In International Conference on Machine Learning, pp. 19274-19286. PMLR, 2023.
[17] Han, Zeyu, Chao Gao, Jinyang Liu, Jeff Zhang, and Sai Qian Zhang. "Parameter-efficient fine-tuning for
large models: A comprehensive survey." arXiv preprint arXiv:2403.14608 (2024).
[18] Wang, Weizhi, Li Dong, Hao Cheng, Xiaodong Liu, Xifeng Yan, Jianfeng Gao, and Furu Wei. "Augmenting
language models with long-term memory." Advances in Neural Information Processing Systems 36 (2023):
74530-74543.
[19] Xu, Wujiang, Kai Mei, Hang Gao, Juntao Tan, Zujie Liang, and Yongfeng Zhang. "A-mem: Agentic memory
for llm agents." arXiv preprint arXiv:2502.12110 (2025).
[20] Liu, Xiao, Bo Qin, Dongzhu Liang, Guang Dong, Hanyu Lai, Hanchen Zhang, Hanlin Zhao et al. "Autoglm:
Autonomous foundation agents for guis." arXiv preprint arXiv:2411.00820 (2024).
[21] Wang, Junyang, Haiyang Xu, Haitao Jia, Xi Zhang, Ming Yan, Weizhou Shen, Ji Zhang, Fei Huang, and
Jitao Sang. "Mobile-agent-v2: Mobile device operation assistant with effective navigation via multi-agent
collaboration." Advances in Neural Information Processing Systems 37 (2024): 2686-2710.
[22] Zhong, Wanjun, Lianghong Guo, Qiqi Gao, He Ye, and Yanlin Wang. "Memorybank: Enhancing large
language models with long-term memory." In Proceedings of the AAAI Conference on Artificial Intelligence, vol.
38, no. 17, pp. 19724-19731. 2024.
[23] Li, Zhiyu, Shichao Song, Chenyang Xi, Hanyu Wang, Chen Tang, Simin Niu, Ding Chen et al. "Memos: A
memory os for ai system." arXiv preprint arXiv:2507.03724 (2025).
[24] Rana, Muhammad, Quazi Mamun, and Rafiqul Islam. "A block cipher for resource-constrained IoT devices."
World Academy of Science, Engineering and Technology 17, no. 3 (2023): 266-271.
[25] Liu, Guangyuan, Yinqiu Liu, Ruichen Zhang, Hongyang Du, Dusit Niyato, Zehui Xiong, Sumei Sun, and
Abbas Jamalipour. "Wireless agentic ai with retrieval-augmented multimodal semantic perception." arXiv preprint
arXiv:2505.23275 (2025).
[26] Houlsby, Neil, Andrei Giurgiu, Stanislaw Jastrzebski, Bruna Morrone, Quentin De Laroussilhe, Andrea
Gesmundo, Mona Attariyan, and Sylvain Gelly. "Parameter-efficient transfer learning for NLP." In International
conference on machine learning, pp. 2790-2799. PMLR, 2019.
[27] Hu, Edward J., Yelong Shen, Phillip Wallis, Zeyuan Allen-Zhu, Yuanzhi Li, Shean Wang, Lu Wang, and
Weizhu Chen. "Lora: Low-rank adaptation of large language models." ICLR 1, no. 2 (2022): 3.
[28] Maharana, Adyasha, Dong-Ho Lee, Sergey Tulyakov, Mohit Bansal, Francesco Barbieri, and Yuwei Fang.
"Evaluating Very Long-Term Conversational Memory of LLM Agents." In Proceedings of the 62nd Annual
Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pp. 13851-13870. 2024.
[29] Gao, Yunfan, Yun Xiong, Xinyu Gao, Kangxiang Jia, Jinliu Pan, Yuxi Bi, Yixin Dai, Jiawei Sun, Haofen
Wang, and Haofen Wang. "Retrieval-augmented generation for large language models: A survey." arXiv preprint
arXiv:2312.10997 2, no. 1 (2023).
[30] Knott, Brian, Shobha Venkataraman, Awni Hannun, Shubho Sengupta, Mark Ibrahim, and Laurens van der
Maaten. "Crypten: Secure multi-party computation meets machine learning." Advances in Neural Information
Processing Systems 34 (2021): 4961-4973.
[31] Fan, Tao, Yan Kang, Guoqiang Ma, Weijing Chen, Wenbin Wei, Lixin Fan, and Qiang Yang. "Fate-llm: A
industrial grade federated learning framework for large language models." arXiv preprint (2023).
[32] Pang, Qi, Jinhao Zhu, Helen Möllering, Wenting Zheng, and Thomas Schneider. "Bolt: Privacy-preserving,
accurate and efficient inference for transformers." In 2024 IEEE Symposium on Security and Privacy (SP), pp.
4753-4771. IEEE, 2024.
