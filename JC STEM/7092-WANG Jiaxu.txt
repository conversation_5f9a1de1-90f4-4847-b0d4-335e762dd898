<PERSON><PERSON><PERSON>
PhD Student in HKUST GZ | <EMAIL> | tel:+852-9713-4631 | born: 1996 | male | Google Scholar

Research Interests

   • Physical Intelligence: Learning dynamics laws and physical principles from real-world perception and interaction.
   • Spatial Intelligence: Reconstructing 3D geometry and building efficient representations for reasoning and action.
   • Embodied AI: Integrating physical and spatial intelligence into Humanoid robots and robotic manipulation.

Research Publications

First Author
   • <PERSON> , <PERSON> , <PERSON>, <PERSON>, <PERSON>. UniRGM: Structured Triplet Coupling of Gaussians, Meshes, and Rigs for
     Dynamic Articulated Object Modeling. (AAAI 2025). Under Review.

   • <PERSON> , <PERSON> , <PERSON> , <PERSON>, <PERSON>, <PERSON>. Latent Sparse Point Fields for Scalable 3D Representation in
     Embodied Manipulation. Advances in Neural Information Processing Systems. (ICRA2026). Submitted

   • <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. MoNeuSC: Learning Anisotropic-augmented Dynamics with Neural Stress
     Corrector from Real World Interactions. International Conference on Computer Vision. (AAAI 2025). Under Review

   • <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> <PERSON>. Query-based semantic gaussian field for scene representation in
     reinforcement learning. International Conference on Intelligent Robots and Systems. (ICRA2026). Submitted Page

   • Humanoid Occupancy: Enabling A Generalized Multimodal Occupancy Perception System on Humanoid Robots.
     (Technical Report) Published by Beijing X-Humanoid Company in 2025. Page

   • J Wang, J Sun, J He, Q Zhang, M Sun, R Xu. DEL: Discrete Element Learner for Learning 3D Particle Dynamics
     with Neural Rendering. Advances in Neural Information Processing Systems (NeurIPS 2024) Page

   • J Wang, J He, Z Zhang, M Sun, J Sun, R Xu EvGGS: A Collaborative Learning Framework for Event-based General-
     izable Gaussian Splatting. International Conference on Machine Learning. (ICML2024) Page

   • J Wang, Q Zhang, J Sun, J Cao, G Han, W Zhao, W Zhang, Y Shao, Y Guo, R Xu. Reinforcement learning with
     generalizable gaussian splatting. International Conference on Intelligent Robots and Systems. (IROS 2024) Page

   • J He∗ , J Wang∗ , J Li, Q Zhang, J Cao, R Xu. DEGS: Temporally-Aware Deformable 3D Gaussian Splatting from
     RGB and Event Data. Transactions on Visualization and Computer Graphics (TVCG). in Minor Revision.

   • J Wang, Z Zhang, J He, R Xu. PFGS: High Fidelity Point Cloud Rendering via Feature Splatting. European Conference
     on Computer Vision. (ECCV 2024) Page

   • J Wang, Z Zhang, R Xu. Learning Robust Generalizable Radiance Field with Visibility and Feature Augmented Point
     Representation. International Conference on Learning Representations. (ICLR2024) Page

   • J Wang, J He, Z Zhang, R Xu. Physical priors augmented event-based 3d reconstruction. International Conference on
     Robotics and Automation (ICRA2024). Page

   • J Wang, B Xu, H Cheng, R Xu. DONE: Dynamic Neural Representation Via Hyperplane Neural ODE. International
     Conference on Acoustics, Speech and Signal Processing. (ICASSP2024) Page

   • J Wang, Z Zhang, R Xu. Learning to generate and manipulate 3d radiance field by a hierarchical diffusion framework
     with clip latent. Pacific Conference on Computer Graphics and Applications. (PG2023) Page
Contributing Author
   • Y Zhang, Q Zhang, X Ju, Z Liu, J Mao, J Sun, J Wang, S Gao, S Cai, Z Qin, S Cai, Z Qin, L Liang, J Wang, R Xu, J
     Tang. EmbodiedVSR: Dynamic Scene Graph-guided Chain-of-thought Reasoning for Visual Spatial Tasks. International
     Conference on Computer Vision. (AAAI 2025). Under Review Page

   • Z Zhang, Q Zhang, W Cui, S Shi, Y Guo, G Han, J Sun, J Cao, J Wang, H Cheng, X Ju, Z Che, R Xu, J Tang.
     Occupancy World Model for Robots. (Robotics Automation Letters) Under Review Page

   • Q Zhang, G Han, J Sun, W Zhao, J Wang, J Cao, H Cheng, L Zhang, Y Guo, R Xu. Distillation-ppo: A novel two-stage
     reinforcement learning framework for humanoid robot perceptive locomotion. (IROS 2025). Page
   • J Cao, Q Zhang, J Sun, J Wang, H Cheng, Y Li, J Ma, Y Shao, W Zhao. Mamba Policy: Towards Efficient 3D
     Diffusion Policy with Hybrid Selective State Models. Conference on Robot Learning. (CoRL2025). Page

   • M Sun, Z Fang, J Wang, J Jiang, D Kong, C Hu, Y Fang, R Xu. Optimal Brain Apoptosis. International Conference
     on Learning Representations. (ICLR2025) Page

   • J Cao, X Zheng, Y Lyu, J Wang, R Xu, L Wang. Chasing day and night: Towards robust and efficient all-day object
     detection guided by an event camera. International Conference on Robotics and Automation. (ICRA2024). Page

   • M Sun, D Zhang, Z Ge, J Wang, J Li, Z Fang, R Xu. Eventrpg: Event data augmentation with relevance propagation
     guidance. International Conference on Learning Representations. (ICLR2024) Page

Education

Hong Kong University of Science and Technology, Hong Kong                                      Nov. 2024 — Sept. 2025
Hong Kong University of Science and Technology (GZ), Guangzhou                                 Sept. 2022 — Nov. 2024
Doctor of Microelectronics, supervised by
Prof. Renjing Xu (Primary in GZ) and Prof. Qiming Shao (Host in HK)

The University of Hong Kong, Hong Kong (Quit)                                                  Aug. 2021 — Feb. 2022
Doctor of Geophysics, supervised by Prof. Clarence Choi

Dalian University of Technology, Dalian                                                        Aug. 2018 — June 2021
Master of Architectural and Civil Engineering (GeoTechnical Branch)
supervised by Prof. Shibin Tang
GPA: 3.77/4.0 (1/120)

Shandong Jianzhu University, Jinan                                                             Sept. 2014 — June 2018
Bachelor of Urban Underground Space Engineering (Engineering Mechanics Branch)
GPA: 3.6/4.0 (1/79)

Academic Experience

Beijing Humanoid Robot Innovation Center Web Page                       Remote Intern              June 2024 — Present

Fok Ying Tung Research Institute of HKUST Web Page                    Research Associate       May 2022 — Sept. 2022

Award

   • 2021 Outstanding Graduates of Liaoning Province
   • 2021 Dalian Science and Technology Progress Second Prize
   • 2020 National Scholarship
   • 2019 First-class Award of Science and Technology of China Gold Association
   • 2018-2020 First-class scholarship of Dalian University of Technology
   • 2017 Second-Class Award of Shandong Electronic and Information Technology Application Competition
   • 2014-2018 First-class scholarship of Shandong Jianzhu University

Academic Service

   • Reviewer:
        – International Conference on Machine Learning (ICML), 2024,2025
        – International Conference on Learning Representations (ICLR), 2025
        – Conference on Neural Information Processing Systems (NeurIPS), 2024
        – Pacific Conference on Computer Graphics and Applications (Pacific Graphics), 2024,2025
        – IEEE International Conference on Robotics and Automation (ICRA), 2024,2025
        – IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS), 2024,2025
        – Robotics: Science and Systems Conference (RSS), 2024
        – IEEE Robotics and Automation Letters (RA-L)
        – International Conference on Artificial Intelligence and Statistics (AISTATS), 2025
   • Member:
        – IEEE Student Member



                                                           2
Skills

   • Programming and Software: C++, C, Python, MATLAB, OpenCV, OpenGL, Qt, VTK, PyTorch, TensorFlow,
     LaTeX, Blender, Unity, Houdini, Ansys, Abaqus, RFPA, COMSOL, MultiPhysics, Solidworks
   • Hardware Experience: Unitree G1 Robot, TienKung Robot 1.0, Franka Robot Arm, AgileX PiPER Robot Arm
   • Physical Simulation Methods: Finite Element Method (FEM, XFEM), Finite Volume Method (FVM), Discrete
     Element Method (DEM), Material Point Method (MPM, MLS-MPM), Smoothed Particle Hydrodynamics (SPH),
     Realistic Failure Process Analysis (RFPA), Numerical Manifold, Position-based Dynamics (XPBD)
   • Language: Chinese (Native), English (IELTS 7, CET-6 596)




                                                     3
2    Goals and Objectives (one Page)
Summary This project aims to establish a physics-aware, tactile-informed, multimodal learning
framework for 3D scene perception and policy learning in embodied AI. By integrating full-hand
tactile sensing with physics-aware multimodal data fusion, we target richer and more actionable
representations of geometry, physical material, interaction dynamics, and agent policy. The
overarching goal is to move from purely vision-based perception toward embodied perception
that jointly reasons about how the world looks, feels, and responds to interaction, thereby
enabling deeper 3D physical scene understanding and more adaptive policy learning.
(1) Design a full-hand tactile skin or glove to achieve real-time tactile perception and self-
pose tracking. We will design a whole-hand tactile glove capable of continuously capturing both
hand and finger pose and full-hand pressure distribution. The key objective is to fuse multimodal
data sensors, including IMU, visual trackers, with tactile sensor readings, and map them through
deformable skinning into a dynamically updated hand mesh. This will provide a physically
consistent, temporally aligned representation of contact between the hand and external objects,
forming the foundation for downstream geometric and physical reasoning tasks.
(2) Jointly perceive the physics and fine-grained geometry of objects in 3D scene by visual-
tactile multimodal exploration. We aim to reconstruct a structured and physics-informed digital
twin for each object in the scene that captures fine-grained geometry and physical properties
through interactive visuo-tactile exploration. Vision provides global structural cues, while
tactile input refines local details and reveals material attributes such as stiffness, friction, and
compliance. To achieve this, we will explore a multimodal data fusion module that relies on and
is enhanced by a differentiable physics learner.
(3) Physics-aware policy learning with multimodal inputs. We will design novel network
architectures to incorporate whole-hand tactile signals, reconstructed physical properties, and
visual and language inputs into the network input. By conditioning policies on those multimodal
representations, the agent can adapt its control strategies in manipulation and interaction tasks.
To make the policy understand real-world physics, we plan to distill physics knowledge through
a differentiable physics simulator with a self-looped, physics-consistent training strategy. We
will also explore how tactile-informed policies improve robustness, safety, and generalization
in complex tasks such as in-hand manipulation, grasp adaptation, and multi-step assembly,
highlighting the role of tactile sensing in bridging perception and action.
(4) Industrial deployment and iterative improvement. We will validate the framework through
real-world tests and on-site pilots with industry partners (e.g., production-line manipulation
and inspection). A closed feedback loop, including logs of vision/tactile signals, predicted vs.
realized forces, and failure cases, will drive dataset updates, simulator parameter tuning, and
model refinement. This translational pipeline both proves the method under industrial constraints
and surfaces new research directions, yielding more robust, safe policies and a clear tech-transfer
path.




                                                 2
3    Timeliness and Novelty (one Page)
Research Background For embodied intelligence, perceiving and physically reasoning about
3D scenes is fundamental. Existing work has largely focused on vision [1, 4, 7,15] and, more
recently, vision–language approaches [16,13]. While these methods can capture geometry and
semantics, they are inherently limited in modeling physical properties, e.g., stiffness, friction,
compliance, and force transmission. Humans, in contrast, rely on touch and an intuitive physics
model to anticipate outcomes before acting. Tactile sensing complements vision by revealing
material properties and contact forces that remain invisible to the eye. This is equally critical for
embodied agents [23, 21]. It is imagined that whole-foot tactile sensing can support gait adapta-
tion on uneven terrains, and whole-hand tactile sensing can provide the fine-grained feedback
required for in-hand manipulation. Without tactile perception and physical understanding, agents
remain reactive rather than predictive.

Why is the research important? As of 2025, both tactile perception and explicit physics
understanding in embodied AI remain nascent. Most studies are confined to controlled labs
and focus on fingertip or end-effector sensing using strain gauges or small patches [21, 22, 24].
Such signals are informative yet inherently limited: they neither scale to diverse, contact-rich
interactions nor provide sufficient coverage for reliable inference of material parameters and
contact mechanics. In parallel, another line of works [19 20] employs a dynamics module as an
auxiliary task to enhance the ability of policy learning. However, many dynamics modules are
implemented as visual next-frame prediction, lacking physical grounding in forces, compliance,
and frictional contact. In contrast, humans perceive with distributed, large-area tactile feedback
and reason with a physics prior. Hence, progress demands distributed whole-hand (and ultimately
full-body) tactile sensing coupled with physics-aware scene understanding that can estimate
physical properties, predict physics-aware dynamic outcomes, and inform robust control.

Why should we conduct the research now? Recent advances present a unique opportunity
to move beyond limited prototypes and vision-only prediction. Key developments include: 1.
flexible tactile materials and scalable sensor integration that enable dense whole-hand sensing;
2. improved on-hand pose tracking through IMUs and visual markers; 3. differentiable physics
simulators and rendering tools that support end-to-end training; and 4. multimodal learning
frameworks capable of fusing vision, touch, and language. Together, these advances make the
development of tactile-informed, physics-aware 3D scene perception especially timely. Such
integration bridges the gap between geometry and physics, supports anticipatory reasoning about
the consequences of actions, and opens new possibilities in locomotion, dexterous manipulation,
and multimodal policy learning. Advancing whole-hand tactile sensing in combination with
explicit physical modeling is, therefore, not only necessary but also transformative for the future
of embodied intelligence.




                                                 3
4     Methodology (Three Page)
In this study, we develop a physics-aware, tactile-informed, multimodal framework for
physical scene perception and policy learning. To the best of our knowledge, this is the first
project to jointly leverage whole-hand tactile sensing and differentiable physics to address both
perception and decision-making challenges in embodied AI.
    (1) First, we will design a whole-hand tactile glove that captures dense pressure signals across
the entire hand and tracks its own pose in real time, producing a mesh-based hand representation
suitable for contact-rich downstreaming tasks.
    (2) Second, we propose a physics-aware, multimodal perception framework. By jointly incor-
porating tactile and visual observations and enforcing their agreement through a differentiable
physics module, the framework recovers finer 3D geometry and physical properties of the scene,
moving beyond vision-only reconstruction.
    (3) Third, we will introduce a novel PT-4D paradigm, a dynamics-predictive, physics-
aware multimodal policy learning paradigm. Conditioned on vision, touch, language, and
proprioception, the policy jointly predicts an action and the resulting scene change, and is trained
with a self-looped physics consistency strategy, implemented by differentiable simulation. This
coupling substantially strengthens physical consistency and enables anticipatory, physically
grounded decision-making.
    (4) Finally, we will validate the framework in real robotic scenarios, including fine-grained
manipulation and locomotion over irregular or deformable terrains, using humanoid platforms
such as X-Humanoid (Beijing) and BYD humanoid systems (Hong Kong).
4.1     A Whole-hand Tactile Glove with Multimodal Self Pose Tracking
Background. Recent tactile embodied systems have largely relied on small fingertip strain
gauges [24, 22]. While such sensors provide useful contact cues, their coverage and bandwidth
are limited and differ markedly from the distributed, whole-hand tactile perception that humans
rely on. Building on this observation, our prior work developed a whole-hand tactile glove with
distributed flexible fiber sensors that captures dense contact forces across the palm and fingers.
Fig. 1 provides some examples of our previously designed product. However, the current version
of this device lacked the self-pose tracking functionality of the hand and fingers.
Method roadmap. As our team engage deeply in multimodal data fusion and learning [11], We
therefore propose a multimodal tracker that fuses IMU-based palm dynamics with vision-based
finger articulation: embedded IMUs supply high-frequency global motion zIMU              t   = [at , ωt ], while
visual markers on the dorsum and finger joints provide drift-free local constraints zvis           t = h(xt ).
The full hand state is xt = [pt , qt , vt , ωt , θt , θ̇t ], where (pt , qt ) denote palm pose and θt the
finger joint angles.
Modality fusion for Real-time pose self-tracking. We will adopt a learning–EKF (Extended
Kalman Filter) fusion that couples deep feature encoders with a principled Bayesian update.
Modality-specific networks produce embeddings eimu           t   = Eimu (zIMU
                                                                            t     ) and evis          vis
                                                                                         t = Evis (zt ). The
EKF style fusion then runs with these learned models: prediction uses xt|t−1 = fϕ (xt−1 , eimu             t );
correction uses xt|t = xt|t−1 +Kϕ(evis t −h  ϕ  (x t|t−1   ))  where K  ,
                                                                       ϕ ϕf   , hϕ are projection networks.   A
                              imu vis
learned gating αt = σ(gϕ (et , et )) softly controls update strength via xt|t =: (1 − αt )xt|t−1 +
αt xt|t , making the filter robust to occlusions and marker dropouts. The learned method yields
calibrated uncertainty and a drift-corrected, tactile-aligned hand–finger pose.
Dynamic mesh updating via tracked states. The recovered state xt is mapped to the MANO
hand model (Hand Model with Articulated and Non-rigid deformations) Φ to reconstruct a
hand mesh Mt = Φ(xt ). Tactile signals are then projected onto Mt to produce local surface
deformations ∆Mt at contact regions, yielding the refined mesh M∗t = Mt + ∆Mt . This


                                                       4
tactile-informed deformation furnishes a physically grounded mesh representation of fine-grained
interactions that goes beyond pose alone.
4.2    3D Physics-Geometry Joint Perception via VisuoTactile Multimodal
       Fusion with Differentiable Physics
Background. Learning 3D geometry from visual observations has long been studied: recent
implicit (NeRF [17]) and explicit (3D Gaussian Splatting [18, 3]) representations reconstruct
geometry from monocular or multi-view imagery with impressive fidelity. Yet vision alone
cannot reveal essential physical properties (e.g., stiffness, friction) and often misses fine-grained
surface textures, both crucial for embodied agents that must interact safely and successfully with
the real physical world. Our prior work [6] attempts to infer geometry and physics purely from
dynamic videos (e.g., falls, impacts), but they depend on large visible deformations, yield coarse
reconstructions, miss micro-structure, and cover only narrow material classes.
    To address these gaps, we introduce tactile signals as a complementary modality. Humans
use touch to perceive what vision cannot; likewise, tactile signals supply direct contact and force
cues absent from images. We therefore propose a physics-enhanced, multimodal perception
framework that fuses vision and whole-hand tactile signals and unifies them into a differentiable
physics learner, enabling the system to both acquire finer geometry and estimate physical
properties of the scene in a physically consistent manner.
Method roadmap. We first reconstruct a coarse object mesh Ĝ from images by using off-the-
shelf 3D reconstruction methods, such as [2,3], which serves as a geometric prior. The agent then
executes a controlled probing sequence (squeezing, stroking, sliding with the objects) to acquire
synchronized whole-hand tactile data: a pose-tracked hand mesh with surface deformations
{Mt }, which can be exported by our designed glove in Sec. 4.1, and distributed pressure maps
{Tt } across palm and fingers. A one-time calibration establishes a mapping between hand-mesh
surface points and tactile taxels; we denote the known projection by Π that can be used to
aggregate simulated contact stresses on Mt into the taxel grid.
Differentiable physics for aligning tactile sensing and geometries. Let G denotes the refined
object geometry and P = {E, ν, µ, c, . . . } denotes its physical parameters (Young’s modulus
E, Poisson ratio ν, friction coefficient µ, damping c, etc.). We propose to use a differentiable
physics simulator S, such as a particle-based MPM, a mesh-based FEM, or a neural-based
simulator [6], that takes (G, P ) as input, together with the hand boundary conditions provided
by the pose-tracked hand mesh Mt . Here, Mt encodes the measured motion and placement of
hand-surface points over time, effectively describing how the hand presses, slides, or squeezes
the object. Given these inputs, the simulator produces the corresponding contact stress and force
fields on the hand surface. The predicted whole-hand pressure map can then be expressed as

                                  T̃t (G, P ) = Π(S(G, P ; Mt )),                                 (1)

where Π projects simulated stresses onto the tactile sensor grid. To better refine the surface
geometries, such as those sub-visual textures, we parameterize a displacement/roughness field ψ
on the coarse visual prior Ĝ, yielding a refined surface with vertices v⋆ = v + ∆(v; ψ). We give
a visual aid of the entire pipeline in Fig. 2.
Optimization strategy and objectives. We jointly estimate (G, P, ψ) by aligning simulated and
observed tactile responses while remaining faithful to the visual and physical prior:

       (G∗ , P ∗ , ψ ∗ ) = arg min Ltac(T̃t (G, P ), Tt ) +λphys Rphys (P ) +λsurf Rsurf (ψ) .    (2)
                               G,P,ψ |       {z        }         | {z }            | {z }
                                       tactile consistency       physical reg       surface reg



                                                      5
Here Ltac compares predicted and measured pressure maps, Rphys encodes physical constraints
(e.g., E > 0, ν ∈ (−1, 0.5), µ ≥ 0). Rsurf constrains micro-geometry (e.g., TV/Laplacian
smoothness and bounded amplitude for ψ,) which helps stabilize the training stability.
4.3    Physics-aware, Tactile-informed Multimodal Policy Learning
Background. The prevailing setup learns a reactive policy that maps a language instruction,
a current 3D observation, and the agent state to an action [7, 12, 9] (we refer this as the
3D paradigm in Fig. 3(a)): at = π3D (L, o3D         t , st ). However, it lacks anticipation of future
outcomes. Humans, by contrast, always anticipate the consequences of an action before execution,
underscoring the importance of an explicit dynamics model as a marker of physical understanding.
Recent works in 2025 augment the policy to jointly output an action and a future frame [14, 19]
(the 4D paradigm in Fig. 3(b)): (at , o3D                       3D
                                             t+1 ) = π4D (L, ot , st ). However, these models remain
vision-centric and are not physically grounded (e.g., they do not reason about forces or contact
modes). At the same time, some works incorporate tactile input into the policy learning [22, 21,
24], at = πT-3D (L, o3D
                     t , Tt , st ), which we call the T-3D paradigm in Fig. 3(c). But they still omit
the explicit prediction of what will happen in the future. These point to a missing piece: a T-4D
formulation that fuses tactile-informed multimodal inputs with explicit dynamics prediction,
shown in Fig. 3(d). Yet even such extensions often consider “dynamics predictions” for next-
frame visual generation, rather than understanding true physical concepts. To address this, we
propose to distill physics into the policy learning via differentiable simulation to physically align
the predicted actions and anticipated dynamics. We name it as a novel PT-4D paradigm.
Method roadmap. We will design a multimodal policy that reasons about both ”what to do” and
”what will happen”. The policy receives (i) current object geometry Xt (ii) physical parameters
P estimated in Sec. 4.2, (iii) whole-hand tactile observations Tt with the self-tracked hand poses
Mt , (iv) proprioceptive/embodiment states st , and (v) a language instruction L. To facilitate
understanding, the whole pipeline of our novel policy learning framework can be seen in Fig. 4.
Multimodal feature fusion for a shared dynamic latent. Inspired by our prior work [10],
each modality is tokenized by a dedicated encoder: z geo = Egeo (Xt ), z tac = Etac (Tt , Mt ),
z prop = Eprop (st ), z lang = Elang (L). These embeddings are fused by a Transformer with
cross-attention to produce fused tokens ct = TF([z geo ; z tac ; z prop ; z lang ]). This representation
conditions a flow-matching module that outputs a compact dynamics latent yt = FMθ (ϵ; ct ).
From yt we decode two outputs with lightweight heads: (1) a dynamic head predicts the
geometric and tactile futures (Xt+1 , Tt+1 ) = Ddyn (yt ); and (2) a policy head predicts the action
at = Dact (yt ). This dual decoding ensures that the decision and its expected consequence arise
from the same dynamics-aware latent yt , aligning “how we act” with “what will change.”
Self-looped physics consistency with differentiable simulation. To move beyond a purely
generative paradigm and endow predictions with physical meaning, we distill physics knowledge
perceived by (Sec . 4.2) into policy learning via a physics-consistency learning strategy. Con-
cretely, we execute the predicted action at in the differentiable simulator S to obtain a rollout
geometric and tactile future X  et+1 , Tet+1 = S(Xt , at , P ). We then adopt these simulated results to
supervise the results predicted by the dynamic head.
                    Lphys-cons = λgeo Lgeo(X   et+1 , Xt+1 ) + λtac Ltac(Tet+1 , Tt+1 ).              (3)
Based on these constraints, the action at and the predicted future Xt+1 are required to tightly
cohere under physics, strengthening their mutual dependence.
    Rather than just predicting future frames, the policy is trained to predict the state that a
differentiable physics engine would produce for its own action in the current scene. This joint
constraint binds action selection and dynamic prediction to physics-consistent evolution, greatly
enhancing physical consistency throughout the pipeline.

                                                   6
Figure 1: Prototype whole-hand tactile glove and visualizations. (a) Pinching a cup with the
corresponding contact map over a hand model. (b) Raw hand pressure matrix from distributed
taxels. (c) Tactile response while snapping fingers. The current prototype delivers dense, whole-
hand pressure sensing and interactive visualization, but does not estimate real-time hand–finger
pose; in this project, we will upgrade it to a self-tracking version by fusing IMU and vision-based
markers to recover a pose-tracked hand mesh.




Figure 2: Physics-aware visuotactile reconstructions of both fine-grained geometries and
physical parameters. Visual inputs initialize coarse geometry. A surface displacement field
ψ(x) is used to augment and refine the coarse geometry. Tactile data with self-pose tracking
provides a hand mesh trajectory as the boundary conditions of the simulation. A differentiable
physics simulator predicts hand pressure distributions, which are matched to measured pressure
maps to form a loss; gradients update material parameters and micro-geometry. Red arrows are
the forward path; purple arrows refer to the backward propagation path.




                                                7
Figure 3: (a) The classic 3D paradigm relies on visual observations and instructions to directly
output an action. (b) The 4D paradigm extends this by also predicting how the visual scene
will change after the action. (c) The T-3D paradigm augments the 3D setup with tactile input,
improving perception of contact but still without prediction. (d) The T-4D paradigm combines
vision and touch to predict both the action and how the scene and tactile feedback will evolve.
Our work builds on (d) and goes a step further by introducing physics constraints, making the
learned policy more physically grounded and reliable.




Figure 4: Overview of our physics-aware, dynamic-predictive, tactile-informed multimodal
policy learning framework. The model takes multimodal inputs including language instructions,
robot state, 3D observations, and whole-hand tactile readings. These inputs are fused through
cross-attention Transformers and projected into a dynamics latent via a flow-matching module.
From this latent, three heads predict: (i) the next 3D scene state, (ii) the next tactile feedback,
and (iii) the action to execute. The predicted futures are compared against outcomes generated
by a differentiable physics simulator, enforcing physics consistency. This design, which we term
self-looped physics consistency constraints (red dash box), enables the policy to anticipate both
visual and tactile dynamics while grounding its decisions in physically plausible interactions.

                                                8
Project Timeline
Project duration: Dec 2025 – Nov 2028 (36-month Fellowship). Annual reports: report period
ends 30 Sep; submission due on or before 31 Oct.

• 2025-12 → 2026-01 — Thrust 1A: Full-hand tactile glove (design/fabrication; flexible-fibre
  layout; electronics). Milestones: M1.0 Design freeze (2026-01); M1.1 Glove v1 prototype
  (2026-02).

• 2026-02 → 2026-03 — Thrust 1B: Self-pose tracking (IMU + visual markers; MANO
  mapping; minimal dataset bring-up). Milestones: M1B.1 IMU–camera synchronisation &
  calibration validated (2026-03); M1B.2 MANO-aligned mesh streaming at 100 Hz (2026-03).

• 2026-10-31 — Annual Report Y1 (due): covers 2025-12 → 2026-09 (report period ends
  2026-09-30).

• 2026-04 → 2027-02 — Thrust 2A: Visuotactile inverse optimisation with differentiable
  physics (co-estimate geometry & material parameters; contact modelling). Milestones: M2A.1
  Differentiable-physics stack integrated (2026-08); M2A.2 Inverse-optimisation baseline ready
  (2026-12); M2A.3 Parameter identifiability study completed (2027-02).

• 2027-03 → 2027-09 — Thrust 2B: Forward multimodal model (vision + whole-hand touch)
  with surface micro-geometry refinement. Milestones: M2B.1 Micro-geometry refinement
  demo on benchmarks (2027-06); M2B.2 Forward model validated with cross-dataset generali-
  sation (2027-09).

• 2027-01 → 2027-07 — Thrust 3A (simulation): PT-4D policy learning in simulation (physics-
  consistency training; visual + tactile dynamics forecasting). Milestones: M3A.1 Physics-
  consistency training pipeline operational (2027-04); M3A.2 PT-4D demo in simulation (2027-
  07).

• 2027-07 → 2027-11 — Thrust 3B (integration): Physics-aware policy with reconstructed
  geometry/physics; safety-aware tactile constraints; benchmark consolidation. Milestones:
  M3B.1 Safety-aware policy variant complete (2027-09); M3B.2 State-of-the-art on ≥2 tasks
  (2027-11).

• 2027-10-31 — Annual Report Y2 (due): covers 2026-10 → 2027-09 (report period ends
  2027-09-30).

• 2027-12 → 2028-08 — Thrust 4A (industrial deployment): Hardware-in-the-loop & on-site
  pilots (X-Humanoid, Beijing; BYD, Hong Kong); cloud–edge deployment; latency/reliability
  targets; safety case. Milestones: H1 HIL demo on a contact-rich task (2028-03); H2 On-site
  pilot meets latency & reliability targets (2028-08).

• 2028-04 → 2028-11 — Thrust 4B (extensions & scaling): Extensions to additional domains
  (e.g., inspection, warehousing); dataset/tooling release; documentation & tech-transfer package.
  Milestones: E1 Extension evaluation plan approved (2028-05); E2 Domain-transfer pilot
  completed (2028-09); E3 Final release (2028-11).

• 2028-10-31 — Final Report (due): covers 2027-10 → 2028-09 (report period ends 2028-09-
  30).
Potential Collaborator(s) in Hong Kong
   During the Fellowship, the project will establish and maintain close collaborations with
academic and industry partners who provide complementary expertise and translational pathways.
These collaborations are critical to ensuring that the proposed framework is both scientifically
novel and industrially impactful.
Project Principle Investigator:
    The Fellowship will be conducted under the close guidance of Prof. Xiangyu Yue at the
Chinese University of Hong Kong. As the direct supervisor, Prof. Yue brings extensive ex-
pertise in multimodal learning and 3D representation, areas in which he has made significant
contributions. This expertise is highly complementary to the Fellow’s background in 3D physics
simulation and robot learning. Prof. Yue will play a central role in advising on the core innova-
tions of the project, particularly the integration of tactile sensing with visual and physical scene
understanding. His supervision ensures that the research remains at the forefront of embodied
AI and that the methodological advances are rigorous and impactful.
External Collaborators:
Prof. Qiming Shao (HKUST, Computer Science and Engineering) In parallel, the project is
expected to collaborate with Prof. Qiming Shao at the Hong Kong University of Science and
Technology, who will serve as host supervisor. Prof. Shao specialises in high-performance and
edge computing, which is critical for deploying advanced algorithms onto real robotic platforms.
While the proposed methods may perform well in simulation, practical deployment requires
careful consideration of computational efficiency, communication with cloud resources, and
real-time processing on local devices. Prof. Shao’s expertise directly addresses these challenges.
His involvement will ensure that the methods developed in this project can be translated from
simulation to real-world robotic systems, thereby securing a vital link between theoretical
innovation and practical feasibility.
HKUST–BYD Von Neumann Institute for Embodied Intelligence The Fellowship will also
engage with the HKUST–BYD Von Neumann Institute for Embodied Intelligence, jointly
established by HKUST and BYD’s Embodied Intelligence Division. Backed by BYD’s industrial
expertise, the Institute provides a unique translational platform connecting academic research to
industrial applications. This collaboration creates opportunities to apply the proposed methods
in real production environments, such as robotic arms performing complex material assembly,
automated inspection, and manufacturing processes. Through access to industrial testbeds and
pilot production lines, the Institute and BYD will play a critical role in validating the framework
beyond laboratory settings. They may also serve as hosts for the Fellow’s translational research
industry attachment, enabling direct engagement with industrial workflows and accelerating the
realisation of pledged impact.
Summary
    Together, these collaborators provide an integrated support structure: (i) Prof. Yue ensures
scientific novelty and rigorous methodological guidance; (ii) Prof. Shao addresses the compu-
tational and deployment challenges critical for bringing the methods onto real robots; and (iii)
the Von Neumann Institute and BYD provide the industrial environment necessary to validate
and apply the methods at scale. This collaborative ecosystem guarantees that the Fellowship
outcomes will not only advance the state of the art in embodied intelligence but also deliver
tangible impact in real-world manufacturing and robotic applications.
Translational Impact
Impact Beyond Academia. The proposed research on physics-aware, tactile-informed, mul-
timodal perception and policy learning will deliver tangible contributions and advantages that
extend well beyond the academic community.

Economic and Industrial Impact. By enabling embodied agents to perceive not only geometry
but also physical properties such as stiffness, friction, and compliance, this project opens the
way to safer and more robust robotic systems in manufacturing and logistics. In collaboration
with BYD and the HKUST–BYD Von Neumann Institute, the framework will be piloted on
production lines, where improved tactile-informed control can reduce product damage, lower
failure and rework rates, and increase automation efficiency. These advances directly contribute
to industrial competitiveness and cost savings in high-value manufacturing sectors.

Societal and Quality-of-Life Benefits. Embodied agents that can interact with the physical
world in a physically consistent manner will be better suited for applications in healthcare,
eldercare, and service robotics. For example, robots equipped with whole-hand tactile perception
can handle delicate objects such as medical tools or assist elderly individuals in daily tasks with
higher safety and reliability. This enhances quality of life, especially for aging populations, by
expanding access to safe robotic assistance in domestic and care settings.

Contributions to Public Policy and Services. The development of tactile-informed and
physics-aware robots aligns with global policy goals in digital transformation and smart manu-
facturing. By demonstrating scalable deployment of safe and reliable embodied AI in industrial
and service contexts, the project will provide valuable evidence for shaping policy frameworks
on human–robot collaboration, workplace safety standards, and guidelines for responsible AI
deployment in society.

Environmental and Sustainability Impact. More accurate physical perception reduces energy
waste and material damage in automated assembly and handling. Robots capable of anticipating
forces and adapting contacts can perform with greater efficiency, lowering scrap rates and
resource consumption. These improvements contribute to more sustainable manufacturing
practices, consistent with broader environmental and climate goals.

Cultural and Educational Value. The project will also contribute to the cultural understanding
of embodied intelligence by disseminating open-source datasets and demonstrators, fostering
public engagement with cutting-edge robotics. This helps inspire the next generation of engineers
and strengthens the talent pipeline in AI and robotics.
    In sum, the project will generate valuable changes across economy, society, and environment:
enhancing industrial productivity, improving safety and quality of life, informing responsible
policy, supporting sustainability, and engaging the public with embodied AI technologies.
