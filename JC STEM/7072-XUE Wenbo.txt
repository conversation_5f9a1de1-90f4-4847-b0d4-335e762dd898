<PERSON><PERSON>e
                                                                              Southern University of Science
Ph.D. Candidate in Intelligent                                              and Technology, Shenzhen, China

Manufacturing & Robotics                                                                                     

Founder & CEO, LiAn Robotics Co., Ltd.                                      # <EMAIL>



                 Education
2019–2025 (exp.) Ph.D. in Intelligent Manufacturing and Robotics, Southern University of Science
                 and Technology, Shenzhen, China
                 Dissertation: Origami-inspired soft robotics, rigid-flexible coupled systems, and multi-material
                 3D printing.
      2015–2019 B.Eng. in Process Equipment and Control Engineering, Northwest University,
                Xi’an, China

                 Entrepreneurship Experience
   2021–Present Founder & CEO, Shenzhen LiAn Robotics Co., Ltd.

                 ○ Spearheaded the development of the ”Changqi Tongluo” TCM massage robot, securing
                   40M RMB in investment and achieving 0.5 N force control accuracy in collaboration with
                   Guangdong Provincial Hospital of Chinese Medicine.
                 ○ Engineered and commercialized advanced indoor-outdoor navigation algorithms for wheeled

                   and legged robots, generating over 1.5M RMB in sales.
                 ○ Co-developed a UAV-based façade inspection system for structural risk assessment as

                   part of a joint R&D initiative with China South City Group (HKEX: 1668).

                 Research Interests
                 ○ Origami-inspired soft robotics                 ○ Compliant mechanisms
                 ○ Multi-material additive manufacturing          ○ Rigid-flexible coupled robotic systems


                 ○   Force/impedance control for HRI              ○   Mobile robot navigation & motion plan-
                                                                      ning

                 Selected Publications (SCI)
             [1] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Origami Robots: Design, Actuation, and 3D Printing
                 Methods. Advanced Materials Technologies, 2024. (IF: 6.2)
             [2] Xue W., Jin L., Jian B., et al. Origami-Based Flexible Robotic Grippers via Hard-Soft
                 Coupled Multimaterial 3D Printing. Soft Robotics, 2024. (IF: 6.4)
             [3] Xue W., Sun Z., Ye H., et al. Rigid-flexible coupled origami robots via multimaterial
                 3D printing. Smart Materials and Structures, 2023. (IF: 3.7)
             [4] Jin L., Zhai X., Xue W., Liao W.-H., et al. Finite element analysis, machine learning,
                 and digital twins for soft robots. Smart Materials and Structures, 2024. (IF: 3.7)



                                                                                                             1/2
                Research Projects
 2022–Present Key Contributor, National Key R&D Program of China, Grant No. 2020YFB1312903
                Focused on origami-braiding fusion structures for industrial pipeline operation robots.
   2020–2021 Principal Investigator, Guangdong Province Climbing Plan Project
                Led research on a wearable variable-stiffness rehabilitation glove; project completed success-
                fully.

                Patents
CN108222600B    Expandable rotary lift stereo garage. (First inventor, granted).
CN215459847U    Flexible exoskeleton device. (First inventor, granted).
CN212601918U    Flexible pneumatic finger and gripping device. (First inventor, granted).
CN106768659B    Non-contact gas leakage sensor monitoring system. (Co-inventor, granted).

                Awards and Honors
         2021   Second Prize, “Internet+” Innovation and Entrepreneurship Competition, SUSTech
         2018   Honorable Mention, Mathematical Contest in Modeling (MCM/ICM)
         2018   Second Prize, National Chemical Engineering Design Competition
         2018   Third Prize, National Mechanical Design Competition – Shaanxi Province
   2016, 2018   National Endeavor Scholarship (Awarded twice)
         2017   Second Prize, “Challenge Cup” Competition, Northwest University
        2016 Second Prize, National College Mathematical Modeling Competition – Shaanxi
             Province

                Leadership & Service
   2020–2021 President, Graduate Student Union, SUSTech
                Directed academic exchange events and research-sharing forums for the graduate community.
   2016–2017 Minister of Secretariat, Youth Volunteer Association, Northwest University
                Managed a 14-day rural education support program for underprivileged students in Weinan,
                Shaanxi.




                                                                                                          2/2
G OALS AND O BJECTIVES
The overarching goal of this project is to design, develop, and validate a Vector-Ducted UAV Platform
through to Technology Readiness Level 8 (TRL 8), establishing a market-ready system for safe, precise,
and autonomous high-altitude facade operations. The project aims to create a transformative shift in the
facade maintenance industry by introducing a flying robotic system that integrates thrust vectoring, inherent
safety, and advanced force-control strategies to perform tasks currently dominated by high-risk manual
labour.
The physical prototype is a compact platform measuring 580 mm (L) × 580 mm (W) × 480 mm (H) with
a take-off weight of 9.0 kg. It can carry a maximum payload of 5.0 kg for up to 40 minutes of hover.
Rated IP55 and operable in temperatures from -20°C to 50°C, the system is built for demanding urban
environments.


Specific Objectives

   1. Develop a TRL 8 Prototype: Build and qualify a robust, ducted-fan UAV with thrust-vectoring capa-
      bility, designed as shown in Image 2 . The platform will be optimized for close-proximity operations,
      featuring a 12.5-inch propeller system and achieving hover accuracies of ±0.1m (RTK/Vision) to
      ±0.5m (GNSS).

   2. Innovate Drift-Free, All-Weather Navigation: Engineer and integrate a multi-modal navigation
      module that fuses IMU, vision, and a novel visual-bubble-level sensor. This system will provide an
      absolute gravity reference, eliminating drift in GPS-denied urban canyons and ensuring operational
      stability and safety during long-duration missions. Target metrics: positional drift < 2 cm/min (val-
      idated against Vicon), orientation accuracy within ±0.5◦ over 40+ min missions, robustness under
      illumination variations (100–10,000 lux) and wind disturbances up to 6 m/s, and redundant sensor
      failover within 50 ms with automated return-to-home capability.

   3. Master Force-Controlled Physical Interaction: Pioneer the application of admittance force con-
      trol for aerial facade interaction. By integrating a six-axis force/torque sensor at the payload inter-
      face, the UAV will intelligently modulate its position and thrust vectors to maintain a constant, gentle
      pressure on surfaces. This allows the system to ”feel” the building facade, smoothly following its
      contours and applying consistent force with inspection probes or cleaning brushes. Target metrics:
      contact force regulation within ±10% of a 5–15 N target range, surface-following accuracy within
      ±5 mm RMS on facades, disturbance response time < 150 ms, and stable continuous contact over
      facade paths exceeding 5 m.

   4. Demonstrate End-to-End Application Viability: Conduct comprehensive field trials on real build-
      ing facades in Hong Kong, validating the system’s performance in inspection and cleaning. Target
      metrics: AI-powered crack detection with >95% accuracy and <5% false positives for cracks ≥ 0.3
      mm; cleaning payload achieves >90% dust/stain removal efficiency over 0.5 m × 0.5 m patches; safe
      and uninterrupted facade operations up to 30 m height with continuous contact missions exceeding
      20 min; TRL 8 validation through joint field trials with at least one industry partner.




                                                      1
IMELINESS AND N OVELTY

Background and Market Urgency

The demand for high-rise facade inspection and maintenance is reaching a critical point in Hong Kong
and other global megacities. Regulatory mandates such as Mandatory Building Inspection Scheme require
thousands of buildings to undergo regular assessments. Current methods, mainly rope-access workers and
scaffolding, remain dangerous, expensive, and inefficient, creating an urgent demand for safer, automated
alternatives.
While UAV-based inspection has emerged as a promising solution, most existing approaches remain limited
to visual data collection without physical interaction capabilities [1, 2]. Navigation in GPS-denied urban
environments also suffers from drift and robustness issues, despite advances in visual-inertial odometry
frameworks such as VINS-Mono and ORB-SLAM3 [3, 4].
At the same time, aerial physical interaction research has shown the potential of impedance and admittance
control [5, 6], yet these methods are rarely applied to ducted or thrust-vectoring UAVs. Recent progress on
ducted-fan UAVs highlights their superior safety in close-proximity tasks [7, 8, 9, 10], but their application
in facade operations remains at an early TRL stage and lacks real-world adoption.


The Evolution from Aerial Vehicle to Aerial Robot

As illustrated in Image 1, UAV technology is undergoing a paradigm shift. The first wave transformed
UAVs from military assets into consumer aerial photography tools. The second wave established them as
logistics platforms for payload delivery. We are now at the cusp of a third wave: the rise of the aerial
robot—a system capable of intelligent, precise, and safe physical interaction with the environment.
This project positions itself at the forefront of this transition, moving beyond the UAV as a passive “eye
in the sky” or delivery vehicle. By integrating advanced force-control strategies and robust navigation,
the UAV is redefined as an active robot, capable of executing complex facade tasks that neither fixed-base
manipulators nor conventional drones can accomplish.


Novelty of the Proposed Research

The novelty of this research lies in the integration of three synergistic innovations:
    • Vector-Ducted Airframe for Safe Interaction: Ducted fans inherently enhance safety, but our ad-
      dition of thrust vectoring is transformative. It decouples flight dynamics from interaction dynamics,
      allowing stable contact operations without attitude compromises, even under wind disturbances.
    • Admittance Control for Aerial Platforms: While impedance/admittance control is well established
      in ground manipulators [5], this project pioneers its deployment in aerial facade interaction. The
      UAV exhibits compliant, spring-like behavior, maintaining delicate yet consistent contact forces that
      prevent structural or platform damage.
    • Absolute Gravity Reference for Drift-Free Navigation: The integration of a visual-bubble-level
      sensor provides an absolute gravity reference, mitigating long-term IMU drift and ensuring reliable
      navigation in GPS-denied urban canyons [11].
Together, these innovations advance the UAV from a passive vehicle into a fully capable aerial robot,
uniquely suited for high-rise facade maintenance in dense urban settings.

                                                       1
M ETHODOLOGY
The methodology of this project follows a structured, milestone-driven approach that systematically ad-
vances the technology from proof-of-concept to an operationally validated system. This progression is
guided by the OKR (Objectives and Key Results) framework, which ensures that each phase has measurable
outcomes while building coherently toward higher Technology Readiness Levels (TRLs). The three-year
plan has been designed to ensure feasibility, ambition, and a clear pathway to translational impact.


Rationale for the Phased Approach

The staged methodology mirrors the natural evolution of robotics systems: (1) establishing hardware reli-
ability and low-level control, (2) integrating high-level autonomy and interaction capabilities, and (3) vali-
dating in real operational environments. This stepwise process minimizes technical risk by validating each
subsystem before integration, while ensuring that the overall system reaches TRL 8 within the fellowship
duration.


International Research Landscape

Globally, UAV-based facade inspection and cleaning has attracted attention, but most existing approaches
fall into two categories:
1. Climbing or tethered robots – which demonstrate good force-control but are limited in mobility, require
permanent installations, or lack adaptability to complex urban facades[12].
2. Conventional multi-rotor UAVs – which provide high mobility but suffer from instability in physical
interaction, as exposed propellers create safety risks and aerodynamic downwash leads to poor contact
stability[13, 14].
Several research groups have investigated vision-based facade inspection with UAVs[15, 16, 17]. Others
have explored impedance and nonlinear force-control strategies for aerial manipulators[13, 18, 19]. How-
ever, most of these studies stop at laboratory demonstrations (TRL 4–5) and rarely address the safety and
stability challenges required for deployment on real high-rise buildings.


Distinctiveness of the Proposed Approach

This project is distinctive in three aspects:

    • Vector-Ducted Propulsion System: Unlike conventional UAVs, the ducted rotor with thrust vec-
      toring allows attitude and payload control to be decoupled. While ducted fans forsafety and thrust
      vectoring for control have been explored separately [7, 8], their integrationfor sustained, forceful con-
      tact represents a new paradigm in aerial contact robotics, enabling safer and more stable operations
      than add-on thruster systems[10].

    • Admittance Control for UAV–Surface Interaction: While admittance/impedance control is a well-
      established theory in industrial manipulators [5], its application to aerial robots for sustained contact
      force regulation is a challenging and emerging research area[13, 20]. Our approach directly adapts
      this methodology, enabling precise facade cleaning and inspection tasks that were previously imprac-
      tical with UAVs.

                                                      1
    • Drift-Free Navigation with Visual-Bubble-Level Sensing: To overcome the well-documented lim-
      itations of vision-only navigation in cluttered, GPS-denied environments[21], such as long-term drift
      in Visual-Inertial Odometry (VIO)[3], we introduce an absolute gravity reference. This novel inte-
      gration addresses a known weakness in state-of-the-art SLAM systems [22], ensuring long-duration
      stability.


Justification of the Timeline

The proposed timeline reflects the technical dependencies: hardware reliability (Year 1) is foundational
before integrating advanced autonomy (Year 2), which must be proven in controlled testbeds before large-
scale field trials (Year 3). The facilities at CUHK, including wind tunnels, motion capture systems, and real
facade testing sites via industry partners, provide the ideal ecosystem to realize this timeline. Unlike many
international projects that remain in the prototype stage, our translational focus and local industrial support
enable us to realistically target TRL 8 within three years.


Objective 1: Develop and Validate the Core UAV Platform (Year 1, TRL 1-4)

This phase focuses on creating the fundamental hardware and low-level control systems.

Key Result 1.1: Airframe and Propulsion System Finalized. Design and fabricate the carbon-fiber air-
     frame, ducted fan assemblies, and power distribution system. Perform static thrust and efficiency
     tests.

Key Result 1.2: Thrust Vectoring Mechanism Integrated. Develop and test servo-actuated nozzle mech-
     anisms with reliable thrust vectoring of ±30◦ .

Key Result 1.3: Low-Level Flight Controller Implemented. Develop inner-loop attitude and angular rate
     controllers using thrust vectoring actuators, based on established geometric control principles for
     aerial vehicles [23]. Achieve stable hover and manual control. As shown in Figure 3, the block
     diagram illustrates the intentional interaction force control framework.

Key Result 1.4: Basic Validation in Lab. Demonstrate basic flight maneuvers (hover, forward, lateral)
     and validate controllability, achieving TRL 4.

Target Metrics: static thrust efficiency within ±5% of design specification; thrust-vectoring response time
<100 ms; flight stability maintained for 10 min hover tests with RMS attitude error < 2◦ .


Objective 2: Integrate Advanced Navigation and Force Control (Year 2, TRL 5–6)

This phase transforms the flying platform into an intelligent robot capable of perception and interaction.

Key Result 2.1: Multi-Modal Sensor Suite Integrated. IMU, stereo vision, RTK-GPS, and visual-bubble-
     level sensor with factor-graph SLAM fusion, leveraging modern optimization back-ends and testbeds
     [24, 25].

Key Result 2.2: Drift-Free Navigation Validated. Long-duration (>40 min) GPS-denied indoor tests with
     drift < 2 cm/min.

                                                      2
Key Result 2.3: Admittance Control Algorithm Developed. Outer-loop control law generates position/velocity
     corrections from force/torque inputs. As shown in Figure 4, an overview of the seven subsystems and
     their interactions is presented.

Key Result 2.4: Force Control Demonstrated in a Relevant Environment. UAV maintains constant con-
     tact force (±10%) on mock facade while tracking predefined paths, achieving TRL 6. As shown in
     Figure 5, the system architecture of the ground control station and the flight system is depicted.

Target Metrics: orientation accuracy ±0.5◦ roll/pitch over 40+ min; robustness under 100–10,000 lux illu-
mination and winds up to 6 m/s; contact force regulation within ±10% of 5–15 N range; surface-following
accuracy ±5 mm RMS; dynamic force response time <150 ms.


Objective 3: Demonstrate System in an Operational Environment (Year 3, TRL 7–8)

This phase focuses on end-to-end application testing and system qualification in real-world scenarios.

Key Result 3.1: Application Payloads Developed. Modular inspection payload (RGB/thermal + AI de-
     fect detection) and cleaning payload (roller brush).

Key Result 3.2: Technology Prototype Demonstration (TRL 7). Field trials on low-rise building facade
     with both inspection and cleaning.

Key Result 3.3: Full System Qualification and Demonstration (TRL 8). High-rise facade maintenance
     operations in Hong Kong under supervised autonomy.

Key Result 3.4: Final Report and Commercialization Plan. Documentation, operational manual, and roll-
     out plan with industry partners.

Target Metrics: defect detection accuracy >95% with false positives <5% for cracks ≥ 0.3 mm; cleaning
efficiency >90% on 0.5 m × 0.5 m facade patches; safe continuous contact operations >20 min up to 30 m
height; TRL 8 validated through field trials with at least one Hong Kong industry partner.




                                                    3
 1




 2      Ducted                                         6-DoF Force
                                                                            Pitch axis               Roll axis
        Propeller                                       Sensor
                                               LiDAR




                                                                             Vision Calibration Module

                                                                           Holder
  Vectoring Servo
                                                                           Camera
               Visual Obstacle                                                                        Circular
                                                                           Bubble
               Avoidance                          Roller Drum                                         Spirit Level


 3         6-DoF Force
                                                                                                  Thrust-Vectoring
             sensor                                   Intentional             Actuator
                                                                                                   Quad-ducted
                                                  interaction control         allocation
         Distance and                                                                               Fan Vehicle
            normal
           estimator
                                Force/state
                                 trajectory
     Vision                      generator
   Calibration                                     External wrench                                   VI sensor
                                                                            State estimator
                                                      estimator                                       VICON
               Time of filght     LiDAR
  IMU
                 camera            map


 4                                                           5                                    Data Link
                                                                    VICON         MATLAB
     Manual      Communication                   Sensors                                          RFD900x
     control                          Flight                                                                  BLDC
                                        +                      IMU                                            Motors
                                                                               Class          Class
                                    Admittance                BNO080                       Communication
                                                                              Sensors
                     Power          Controller
                   management                                    TOF
                                                 Actuators
                                                                 L515        Measure           Main Loop
                    Regulator                                                -ments           Timing state-
     Ground                                                    LIDAR                            machine          ESC
     station         Battery                                  MID 360

                                                               Vision        Estimator          Class            Servo
                                                             Calibration                        Control          Motos
  Thrust-Vectoring Quad-ducted Fan Vehicle

1. The four evolutionary stages of UAVs; 2. Design and layout of the Vector-Ducted
UAV with the vectored-thrust mechanism and the visual-bubble-level sensor; 3.
Block diagram of the intentional interaction control framework; 4. Overview of the
seven subsystem and their interacting; 5. System architecture of ground control
station and flight system.
P ROJECT T IMELINE
The project is structured as a three-year fellowship, with clear milestones designed to achieve TRL 8.
Annual reports are due on or before 31 October for the period ending 30 September.

                            Table 1: Gantt Chart of Project Activities and Milestones
 Activity / Milestone                          Year 1                  Year 2                 Year 3
                                       Q1    Q2    Q3       Q4   Q1   Q2   Q3    Q4     Q1   Q2   Q3   Q4
 Phase 1: Platform Dev. (TRL 1-4)
 Design & Fabrication
 Propulsion & Vectoring Tests
 Low-Level Controller Dev.
 Milestone: TRL 4 Achieved
 Phase 2: Adv. Control (TRL 5-6)
 Sensor Suite Integration
 Navigation Algorithm Dev.
 Admittance Control Dev.
 Milestone: TRL 6 Achieved
 Phase 3: Op. Demo (TRL 7-8)
 Application Payload Dev.
 Field Trials (TRL 7)
 Full Qualification (TRL 8)
 Milestone: TRL 8 Achieved
 Reporting
 Annual Report 1
 Annual Report 2
 Final Report




                                                        1
P OTENTIAL C OLLABORATORS IN H ONG KONG
This project will actively engage a multi-disciplinary network of collaborators to ensure its academic rigor,
regulatory compliance, and translational success.


Academic Collaborators
    • Primary Supervisor: Prof. Ben M. Chen, CUHK. As a world-renowned expert in unmanned
      systems and control theory, an IEEE Fellow, and Editor-in-Chief of Unanned Systems, Prof. Chen’s
      guidance will be invaluable. His lab at CUHK provides state-of-the-art facilities and a wealth of
      institutional knowledge in UAV development.

    • Secondary Collaborator: HKUST Robotics Institute. Collaboration with experts at HKUST will
      provide complementary expertise in AI-based computer vision for defect detection and advanced
      motion planning algorithms.


Regulatory and Governmental Bodies
    • Civil Aviation Department (CAD) Hong Kong: Early and continuous engagement with the CAD
      is critical to ensure the UAV platform and its operational procedures comply with all local SUAS
      (Small Unmanned Aircraft System) regulations, particularly for operations in urban environments.
      This collaboration will help shape a pathway for the legal operation of contact-based aerial robots.

    • Buildings Department & Urban Renewal Authority: Partnering with these bodies will ensure the
      inspection methodologies meet the standards of the Mandatory Building Inspection Scheme. Their
      involvement provides a direct channel for large-scale governmental adoption and validation of the
      technology’s effectiveness.


Industrial and Application Partners
    • Property Developers and Managers: Companies like Swire Properties and Kai Shing Manage-
      ment Services own and manage vast portfolios of high-rise buildings. They will serve as crucial
      end-users, providing real facade sites for field trials (TRL 7-8) and invaluable feedback on opera-
      tional requirements and business models.

    • Infrastructure Operators: Owners of major infrastructure, such as the operators of the Tsing Ma
      Bridge, can benefit from this technology for structural health monitoring, providing another key
      application area beyond residential buildings.

    • Facade Maintenance Service Providers: Existing companies that provide rope-access or scaffold-
      ing services are potential adopters of this technology. We will engage them to understand workflow
      integration and to develop a ”robot-as-a-service” business model.


Innovation and Technology Ecosystem
    • Hong Kong Science and Technology Parks Corporation (HKSTP): Engaging with HKSTP will
      provide access to a broader network of tech startups, potential investors, and manufacturing partners.
      It offers a clear pathway for spinning off the technology into a commercial venture post-fellowship.


                                                     1
T RANSLATIONAL I MPACT
The translational impact of this research extends far beyond academia, delivering tangible benefits to Hong
Kong’s economy, society, and environment by fundamentally reshaping high-risk industries. The core of
this impact lies in the paradigm shift from UAV-as-a-vehicle to UAV-as-a-robot, unlocking new capabilities
and markets.


Economic Impact

    • Cost Reduction: The platform drastically reduces operational costs by eliminating the need for
      expensive and time-consuming scaffolding or gondolas. Maintenance cycles can be shortened from
      weeks to days, leading to significant savings for building owners.

    • Creation of New High-Tech Service Industry: This project will seed a new market for robotic
      facade maintenance services in Hong Kong. This creates high-skilled jobs for robot operators, data
      analysts, and maintenance technicians, boosting the local tech economy.

    • Global Market Potential: The technology is directly applicable to any megacity with a high con-
      centration of skyscrapers. Successful deployment in Hong Kong will serve as a launchpad for inter-
      national expansion, creating a valuable export product.


Societal and Safety Impact

    • Elimination of High-Risk Jobs: The most significant societal impact is the potential to eliminate the
      need for ”spidermen” (rope-access workers) to perform dangerous facade work. This project doesn’t
      aim to replace workers, but to transform their jobs. The former rope-access technician becomes a
      highly skilled robot operator, controlling a fleet of UAVs safely from the ground. This represents a
      monumental leap forward in occupational safety.

    • Enhanced Public Safety: By making facade inspections faster and cheaper, building owners can
      conduct them more frequently. This proactive approach allows for the early detection of defects like
      loose tiles or cracks, preventing catastrophic facade failures and enhancing public safety in dense
      urban areas.

    • Broadened Application Scope: The core technology is applicable to a vast range of high-risk inspec-
      tion and maintenance tasks beyond buildings, including bridges, wind turbines, dams, and offshore
      oil rigs, vastly improving safety and efficiency across multiple critical infrastructure sectors.


Environmental Impact

The precision of admittance force control allows the cleaning module to apply the exact pressure needed.
This optimizes the use of water and cleaning agents, minimizing runoff and waste compared to traditional
high-pressure washing methods. This contributes to a more sustainable and environmentally friendly ap-
proach to building maintenance.




                                                    1
Policy and Industrial Adoption

This project will generate crucial data and safety case studies that can inform future regulations by the Civil
Aviation Department for autonomous and contact-based UAV operations in urban settings. By demon-
strating a safe and reliable system, this research will pave the way for wider industrial adoption and help
establish Hong Kong as a global leader in urban aerial robotics.




                                                      2
R EFERENCES
 [1] S. Jordan, J. Moore, S. Hovet, J. Box, J. Perry, K. Kirsche, D. Lewis, and Z. T. H. Tse, “State-of-the-art
     technologies for uav inspections,” IET Radar, Sonar & Navigation, vol. 12, no. 2, pp. 151–164, 2018.

 [2] Y. He, Z. Liu, Y. Guo, Q. Zhu, Y. Fang, Y. Yin, Y. Wang, B. Zhang, and Z. Liu, “Uav based sensing
     and imaging technologies for power system detection, monitoring and inspection: a review,” Nonde-
     structive Testing and Evaluation, vol. 0, no. 0, pp. 1–68, 2024.

 [3] T. Qin, P. Li, and S. Shen, “Vins-mono: A robust and versatile monocular visual-inertial state estima-
     tor,” IEEE Transactions on Robotics, vol. 34, no. 4, pp. 1004–1020, 2018.

 [4] T. A. C. Garcia, A. M. G. Tommaselli, L. F. Castanheiro, and M. B. Campos, “A photogrammetric ap-
     proach for real-time visual slam applied to an omnidirectional system,” The Photogrammetric Record,
     vol. 39, no. 187, pp. 577–599, 2024.

 [5] N. Hogan, “Impedance control: An approach to manipulation: Part ii—implementation,” Journal of
     Dynamic Systems, Measurement, and Control, vol. 107, no. 1, pp. 8–16, 03 1985.

 [6] C. Ott, R. Mukherjee, and Y. Nakamura, “Unified impedance and admittance control,” in 2010 IEEE
     International Conference on Robotics and Automation, 2010, pp. 554–561.

 [7] M. Miwa, Y. Shigematsu, and T. Yamashita, Control of Ducted Fan Flying Object Using Thrust Vec-
     toring. Tokyo: Springer Japan, 2013, pp. 97–107.

 [8] M. Muehlebach and R. D’Andrea, “The flying platform – a testbed for ducted fan actuation and control
     design,” Mechatronics, vol. 42, pp. 52–68, 2017.

 [9] T. Manzoor, Y. Xia, Y. Ali, and K. Hussain, “Flight control techniques and classification of ducted fan
     aerial vehicles,” Kongzhi Lilun Yu Yinyong/Control Theory and Applications, vol. 39, no. 2, pp. 201–
     221, Feb. 2022, publisher Copyright: © 2022, Editorial Department of Control Theory & Applications.
     All right reserved.

[10] R. Miyazaki, H. Paul, T. Kominami, R. R. Martinez, and K. Shimonomura, “Flying washer: Devel-
     opment of high-pressure washing aerial robot employing multirotor platform with add-on thrusters,”
     Drones, vol. 6, no. 10, 2022.

[11] S. Wang and N. S. Ahmad, “A comprehensive review on sensor fusion techniques for localization of a
     dynamic target in gps-denied environments,” IEEE Access, vol. 13, pp. 2252–2285, 2025.

[12] R. Bogue, “Climbing robots: recent research and emerging applications,” Industrial Robot: the inter-
     national journal of robotics research and application, vol. 46, no. 6, pp. 721–727, 09 2019.

[13] K. Bodie, M. Brunner, M. Pantic, S. Walser, P. Pfändler, U. Angst, R. Siegwart, and J. Nieto, “Ac-
     tive interaction force control for contact-based inspection with a fully actuated aerial vehicle,” IEEE
     Transactions on Robotics, vol. 37, no. 3, pp. 709–722, 2021.

[14] J. F. Falorca and J. C. G. Lanzinha, “Facade inspections with drones–theoretical analysis and ex-
     ploratory tests,” International Journal of Building Pathology and Adaptation, vol. 39, no. 2, pp. 235–
     258, 05 2020.


                                                      1
[15] B. Lei, Y. Ren, N. Wang, L. Huo, and G. Song, “Design of a new low-cost unmanned aerial vehicle
     and vision-based concrete crack inspection method,” Structural Health Monitoring, vol. 19, no. 6, pp.
     1871–1883, 2020.

[16] Y. Liu, Y. Lin, J. K. W. Yeoh, D. K. H. Chua, L. W. C. Wong, M. H. Ang, W. L. Lee, and M. Y. L.
     Chew, Framework for Automated UAV-Based Inspection of External Building Façades. Singapore:
     Springer Singapore, 2021, pp. 173–194.

[17] Y. Liu, D. K. Chua, and J. K. Yeoh, “Automated engineering analysis of crack mechanisms on building
     façades using uavs,” Journal of Building Engineering, vol. 103, p. 112176, 2025.

[18] D. Tzoumanikas et al., “Interaction aerial manipulation using hybrid force and position nmpc applied
     to aerial writing,” in Robotics: Science and Systems (RSS), 2020.

[19] X. Meng, Y. He, Q. Li, F. Gu, L. Yang, T. Yan, and J. Han, “Contact force control of an aerial
     manipulator in pressing an emergency switch process,” in 2018 IEEE/RSJ International Conference
     on Intelligent Robots and Systems (IROS), 2018, pp. 2107–2113.

[20] D. R. McArthur, J. E. Slightam, S. J. Spencer, and S. P. Buerger, “Coordinated admittance-impedance
     control with force excitation for compliant, underactuated aerial manipulation,” IFAC-PapersOnLine,
     vol. 58, no. 28, pp. 558–563, 2024, the 4th Modeling, Estimation, and Control Conference – 2024.

[21] F. Valenti, D. Giaquinto, L. Musto, A. Zinelli, M. Bertozzi, and A. Broggi, “Enabling computer vision-
     based autonomous navigation for unmanned aerial vehicles in cluttered gps-denied environments,” in
     2018 21st International Conference on Intelligent Transportation Systems (ITSC), 2018, pp. 3886–
     3891.

[22] C. Campos, R. Elvira, J. J. G. Rodrı́guez, J. M. M. Montiel, and J. D. Tardós, “Orb-slam3: An accurate
     open-source library for visual, visual–inertial, and multimap slam,” IEEE Transactions on Robotics,
     vol. 37, no. 6, pp. 1874–1890, 2021.

[23] T. Lee, “Geometric controls for a tethered quadrotor uav,” in 2015 54th IEEE Conference on Decision
     and Control (CDC), 2015, pp. 2749–2754.

[24] F. Kschischang, B. Frey, and H.-A. Loeliger, “Factor graphs and the sum-product algorithm,” IEEE
     Transactions on Information Theory, vol. 47, no. 2, pp. 498–519, 2001.

[25] N. Michael, D. Mellinger, Q. Lindsey, and V. Kumar, “The grasp multiple micro-uav testbed,” IEEE
     Robotics Automation Magazine, vol. 17, no. 3, pp. 56–65, 2010.




                                                     2
