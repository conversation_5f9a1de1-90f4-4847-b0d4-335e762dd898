#!/usr/bin/env python3
"""
Merge all .txt files in a directory into a single consolidated .txt file.

Features:
- Scans the specified directory for .txt files
- Optionally includes .txt files from subdirectories (with --recursive)
- Writes a clear header and separator before each file's content
- Handles read errors (e.g., encoding/permission issues) gracefully and continues
- Allows specifying the text encoding for both reading and writing (default: UTF-8)

Usage examples:
1) Merge only the root level of JRFS into merged_files.txt (default behavior):
   python3 merge_txt.py --dir . --output merged_files.txt

2) Include .txt files from subdirectories as well:
   python3 merge_txt.py --dir . --recursive --output merged_files.txt

3) Specify encoding explicitly (UTF-8) and provide fallback encodings if some files differ:
   python3 merge_txt.py --dir . --recursive --encoding utf-8 --fallback-encodings gb18030 latin-1

Notes:
- By default, WITHOUT --recursive, only .txt files at the root of the provided directory are included.
- The output file itself is automatically excluded from the merge to avoid self-inclusion.
- Files are merged in deterministic alphabetical order (by relative path from the base directory).
"""
from __future__ import annotations

import argparse
import sys
from pathlib import Path
from typing import Iterable, List, Tuple

SEPARATOR_LINE = "=" * 80


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            "Merge all .txt files in a directory into a single consolidated .txt file."
        )
    )
    parser.add_argument(
        "--dir",
        dest="directory",
        default=".",
        help=(
            "Directory to scan for .txt files (default: current directory). "
            "Without --recursive, only the root level is scanned."
        ),
    )
    parser.add_argument(
        "--output",
        dest="output",
        default="merged_files.txt",
        help=(
            "Output filename (relative to --dir unless absolute). Default: merged_files.txt"
        ),
    )
    parser.add_argument(
        "--encoding",
        dest="encoding",
        default="utf-8",
        help="Encoding to use for both reading and writing. Default: utf-8",
    )
    parser.add_argument(
        "--fallback-encodings",
        nargs="*",
        default=[],
        help=(
            "Optional list of fallback encodings to try when reading a file fails with the primary encoding. "
            "Example: --fallback-encodings utf-8-sig gb18030 latin-1"
        ),
    )
    parser.add_argument(
        "--recursive",
        action="store_true",
        help="Include .txt files from subdirectories as well.",
    )
    return parser.parse_args()


def find_txt_files(base_dir: Path, recursive: bool) -> List[Path]:
    pattern_iter = base_dir.rglob("*.txt") if recursive else base_dir.glob("*.txt")
    # Ensure only files (exclude directories or special entries)
    files = [p for p in pattern_iter if p.is_file()]
    # Sort deterministically by relative path (case-insensitive for consistency)
    files.sort(key=lambda p: str(p.relative_to(base_dir)).lower())
    return files


def try_read_text(path: Path, encodings: Iterable[str]) -> Tuple[bool, str | None, str | None]:
    """Attempt to read text using the provided encodings in order.

    Returns (success, content, encoding_used_or_error)
    - On success: (True, content, encoding_used)
    - On failure: (False, None, error_message)
    """
    last_error = None
    for enc in encodings:
        try:
            with path.open("r", encoding=enc, errors="strict") as f:
                return True, f.read(), enc
        except Exception as e:  # OSError, UnicodeError, etc.
            last_error = f"{type(e).__name__}: {e}"
    return False, None, last_error


def write_header(out, rel_path: str) -> None:
    out.write(f"{SEPARATOR_LINE}\n")
    out.write(f"Source file: {rel_path}\n")
    out.write(f"{SEPARATOR_LINE}\n\n")


def main() -> int:
    args = parse_args()

    base_dir = Path(args.directory).resolve()
    if not base_dir.exists() or not base_dir.is_dir():
        print(f"Error: Provided --dir is not a directory: {base_dir}", file=sys.stderr)
        return 2

    # Resolve output path. If relative, place it under base_dir.
    output_path = Path(args.output)
    if not output_path.is_absolute():
        output_path = base_dir / output_path
    output_path = output_path.resolve()

    # Gather .txt files according to the --recursive flag
    files = find_txt_files(base_dir, recursive=args.recursive)

    # Exclude the output file itself if it matches the pattern
    files = [p for p in files if p.resolve() != output_path]

    if not files:
        print("No .txt files found to merge.")
        return 0

    # Compose encoding preference list: primary + fallbacks
    read_encodings: List[str] = [args.encoding] + list(args.fallback_encodings)

    merged_count = 0
    skipped: List[Tuple[Path, str]] = []

    # Ensure output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        with output_path.open("w", encoding=args.encoding, errors="strict") as out:
            for path in files:
                rel = str(path.resolve().relative_to(base_dir))
                ok, content, info = try_read_text(path, read_encodings)
                if not ok:
                    skipped.append((path, info or "Unknown error"))
                    print(
                        f"Skipping {rel} due to read error: {info}",
                        file=sys.stderr,
                    )
                    continue

                # Write header and content
                write_header(out, rel)
                out.write(content)
                # Ensure a trailing newline between files
                if not content.endswith("\n"):
                    out.write("\n")
                out.write("\n")
                merged_count += 1
    except Exception as e:
        print(
            f"Failed to write output file '{output_path}': {type(e).__name__}: {e}",
            file=sys.stderr,
        )
        return 3

    # Summary
    rel_output = (
        str(output_path.relative_to(base_dir))
        if output_path.is_relative_to(base_dir)  # Python 3.9+ method
        else str(output_path)
    )
    print(
        f"Merged {merged_count} file(s) into: {rel_output} using encoding '{args.encoding}'."
    )
    if skipped:
        print("The following files were skipped due to read errors:", file=sys.stderr)
        for path, msg in skipped:
            try:
                rel = str(path.resolve().relative_to(base_dir))
            except Exception:
                rel = str(path)
            print(f" - {rel}: {msg}", file=sys.stderr)

    # Exit with 0 even if some files were skipped; non-zero reserved for fatal errors.
    return 0


if __name__ == "__main__":
    sys.exit(main())

