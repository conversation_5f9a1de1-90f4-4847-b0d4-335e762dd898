                                                 Haitao Lin
                          Email: linhai<PERSON><EMAIL>       Mobile phone: 17780517314

Education Background
College of Material Science and Engineering, Sichuan University                                   Sep. 2015 - Jun.2019
●   Major: Solid State Physics        Degree: Bachelor            GPA: 3.81（91.04/100）                     Ranking: 1/74
School of Engineering, Westlake University & Zhejiang University                                    Sep. 2021 - Present
●   Major: Computer Science           Degree: Doctor              Supervisor: <PERSON><PERSON> <PERSON><PERSON>

Selected Publication
●   [1] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, CBGBench: Fill in the Blank of Protein-
    Molecule Complex Binding Graph, ICLR2025 (spotlight)
●   [2] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, DiffBP: Generative Diffusion of 3D Molecules
    for Target Protein Binding, Chemical Science, Royal Society of Chemistry, 2024
●   [3] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, PPFlow: Target-aware Peptide Design with
    Torsional Flow Matching, ICML 2024
●   [4] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, G<PERSON>AB: Towards Realistic Antibody Design and
    Reliable Affinity Maturation, ICML 2024
●   [5] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Functional-Group-Based Diffusion for Pocket-Specific
    Molecule Generation and Elaboration, NeurIPS 2023
●   [6] H Lin, Z Gao, Y Xu, L Wu, L Li, SZ Li, Conditional Local Convolution for Spatio-temporal Meteorological
    Forecasting, AAAI 2022
●   [7] H Lin, C Tan, L Wu, Z Liu, Z Gao, SZ Li, An Extensive Survey with Empirical Studies on Deep Temporal Point
    Process, IEEE Transactions on Knowledge and Data Engineering 2024
●   [8] H Lin, L Wu, G Zhao, P Liu, SZ Li, Exploring Generative Neural Temporal Point Process, Transactions on Machine
    Learning Research
●   [9] O Zhang*, H Lin*, H, Zhang, H Zhao, Y Huang, T, Hou, Deep Lead Optimization: Leveraging Generative AI for
    Structural Modification, Journal of the American Chemical Society, 2024
●   [10] L Wu, H Lin, C Tan, Z Gao, SZ Li , Self-supervised learning on graphs: Contrastive, generative, or predictive, IEEE
    Transactions on Knowledge and Data Engineering 2022

    For more, see the Google Scholar (https://scholar.google.com/citations?user=o5A23qIAAAAJ&hl=en).


Working Experience
Tsinghua Laboratory of Brain and Intelligence, Tsinghua University                                             Aug. 2019
●   Formed deep insights of computational neuroscience, worked on ML-combined neural science.
School of Humanities & Social Science, Hong Kong University of Science and Technology                           Jul. 2017
●   Completed two weeks’ immersion in Anthropology study and field research, having a basic knowledge in relevant fields.

Honors & Scholarships
●   National Scholarship Academic Rank 1st in the College (2016 – 2017)
●   National Scholarship Academic Rank 1st in the College (2015 – 2016)
●   The 1st Prize in 2017 National Academic English Competition (May.2017)
●   The 2nd Prize in 2016 National Academic Mathematics Competition (Nov.2016)

Language
●   IELTS(7.5), GRE(326)
Small-molecule drugs, which can penetrate cell membranes and act on intracellular targets, are clinically
important in treating diseases such as cancer, infectious diseases, cardiovascular disorders, and
neurological conditions. Advances in high-resolution mass spectrometry and nuclear magnetic resonance
have made structural identification and analysis more accurate, while improved computing power has
accelerated molecular simulations. These developments have driven explosive growth in structural data,
with major databases such as PubChem [1], ChEMBL [2], and ZINC [3] established. Leveraging this
wealth of data to generate new biomedical insights and guide drug design is of great strategic importance.
Furthermore, the rapid development of artificial intelligence has been continuously demonstrated to hold
immense potential in drug discovery, and is driving transformative changes in this traditionally
established field.

However, current methods typically treat molecules as ball-and-stick models, where atoms are the
smallest units. While atoms are more fundamental than amino acids or fragments, this representation
cannot fully capture underlying physicochemical principles such as intermolecular forces and
intramolecular stacking. As such, future methods must consider deeper particle-level information as
physicochemical priors and constraints [4]. Incorporating quantum-scale subatomic information provides
two key benefits: (i) capturing fundamental interactions such as hydrogen bonding and electrostatics
determined by electron clouds; and (ii) enabling exploration of broader chemical spaces [5], as quantum-
level representations can generalize across proteins, peptides, and small molecules.

To fill the gap between the current atom- or fragment-level-based Artificial-Intelligence-Aided Drug
Design and the quantum-scale subatomic informed methods, this project focuses on small-molecule drug
design, aiming to incorporate quantum-scale physicochemical information into deep learning
frameworks, to advance key technologies in molecular generation, screening, and interaction analysis.
The goal is to establish both methodological foundations and applied systems for quantum-scale AI-
assisted drug design. Specifically, this project aims to achieve the following three research objectives:

(i)      Database construction: Build a million-scale core proprietary database of small molecules
         enriched with quantum-scale information, serving as the data foundation for the subsequent
         methodological developments and application systems.
(ii)     Model development: Based on quantum-scale particle-level data, develop high-precision
         molecular screening models, electron-cloud-constrained generative models, and molecular
         optimization models. In the target screening model, the Pearson correlation coefficient for
         binding energy prediction will exceed that of current state-of-the-art models by more than 5%.
         In the generative model, the stability of designed molecules, as measured by downstream
         evaluation metrics, will surpass existing state-of-the-art models by more than 5%. In the
         optimization model, molecules refined by the model will show an average improvement of more
         than 10% in binding energy and related properties compared to known active molecules, while
         maintaining synthesizability.
(iii)    Platform implementation: Develop an integrated drug design platform that unifies molecular
         generation, screening, analysis, and optimization. This platform will be capable of designing
         and modifying novel drug molecules, with the goal of successfully designing or optimizing at
         least two small-molecule drugs that meet practical application requirements.
Year 1 (September 2026 – August 2027): Data Preparation and Foundational Models

The first year will focus on data consolidation and the establishment of core models. The existing
biomolecular structural database (over one million entries) will be refined: low-precision structures will
be recalculated using molecular dynamics, and protein structures with confidence levels above 70%
will be retained. In parallel, ~30,000 biomolecules will undergo DFT simulations to obtain high-
fidelity electron cloud and orbital data. These will train the first neural operator model, which will then
be applied to predict quantum-scale properties for large datasets, expanding the quantum-informed
database. A multi-scale pretraining model will also be built, unifying atomistic, fragment, and electron
cloud information. Vector quantization will discretize embeddings to reflect quantum energy levels,
producing a robust representation framework by the end of the year.

⸻

Year 2 (September 2027 – August 2028): Generative Models and Experimental Validation

The second year will establish an electron-cloud-constrained generative model, combining diffusion-
based electron cloud generation with autoregressive molecular generation. This framework will be
applied to relevant targets such as ADRB1, generating candidate molecules subsequently screened by
the pretraining model. Top candidates will be selected for wet-lab validation to confirm predicted
activities.

In addition, an optimization pipeline will refine validated leads by integrating property prediction with
generative constraints. This will improve binding affinity, stability, and synthesizability. The iterative
cycle of generation–validation–optimization will form the project’s core translational pathway.

⸻

Year 3 (September 2028 – September 2029): Platform Development and Dissemination

The final year will focus on delivering a practical drug design platform that integrates generation,
screening, optimization, and analysis. The platform will host the models and datasets developed,
enabling use by academic and industrial collaborators. Meanwhile, optimization of validated molecules
will continue, aiming to advance at least two compounds toward preclinical readiness.

Research outcomes will be consolidated into publications, technical reports, and a final project
summary. By project completion, the integration of quantum-scale methods with AI will be
demonstrated not only in theory but also in application, establishing a pioneering framework for next-
generation drug discovery.
The research framework and technical roadmap of this project aim to realize the overall plan illustrated
in Figure 1. To address the three aforementioned key challenges, we propose three interconnected
modules as the methodological core:

     (i)   Development of models for high-precision electron cloud computation.

     (ii) Research on multi-scale molecular representation with the training of quantized embedding.

     (iii) Construction of electron-cloud-constrained molecular generation and optimization models.

Together, these modules form an integrated framework for embedding quantum-scale physicochemical
information into AI-assisted drug design, thereby advancing the discovery, screening, and optimization
of small-molecule drugs.

1.   Neural Operator Models for High-Precision Electron Cloud Generation

The central goal of this project is to incorporate quantum-scale information into AI methodologies,
enabling the learning of unified particle-level interaction patterns within biomolecules. By capturing
these subatomic interactions, we aim to uncover the underlying chemical properties of drugs, thus
establishing more accurate models for drug discovery, screening, and optimization. However, as a data-
driven paradigm, AI requires large volumes of high-quality data.

Compared to the atomistic “ball-and-stick” models used in molecular dynamics, quantum-scale data
such as electron cloud distributions are far more difficult to obtain. Simulations at this scale must
capture quantum mechanical behavior, requiring approaches such as density functional theory (DFT) or
quantum Monte Carlo (QMC). These yield accurate electronic structures and energy landscapes but at
high computational cost.

Recently, neural operators have emerged as powerful tools for simulating continuous physical fields.
They approximate mappings between infinite-dimensional function spaces, making them suitable for
simulating distributions of electron clouds. Their generalization capabilities allow transfer across
diverse molecular systems: by training on a broad set of molecules, neural operators can learn
transferable physical laws and predict electron cloud distributions.

Figure 2 illustrates the proposed approach. We will employ ~30,000 biomolecular structures, including
small molecules, peptides, and proteins, as training data. Using DFT, we will calculate electron clouds
and related quantum properties to serve as ground truth for training neural operator models. Once
trained, the models will infer quantum properties of additional biomolecules, expanding the dataset to
the million scale. This large database of biomolecular structures enriched with quantum-scale
information will support the following modules.

2.   Molecular Representation with Embedded Quantum-Scale Information

2.1 Multi-Scale Unified Representation
Quantum-scale particles capture fundamental forces governing intra- and intermolecular dynamics.
Electron clouds provide direct insight into drug–target interactions, revealing electrostatics, van der
Waals forces, and hydrogen bonding—critical for affinity and selectivity. For instance, polarity of
electron cloud regions can complement protein binding pockets, while charge distributions influence
binding strength. Electron-rich and -deficient regions also indicate potential reactive centers.
Additional subatomic information such as orbitals and nuclear spin further enriches molecular
descriptions.

Thus, electron cloud densities, atomistic ball-and-stick structures, and molecular fragments represent
three complementary scales of representation. Clouds (from DFT) are voxelized 3D grids suited for
Convolutional Neural Networks, while atoms and fragments are point clouds, encoded with Graph
Neural Networks. To integrate these modalities, we propose a multi-scale unified framework (Figure
3): Graph Transformers extract atom/fragment features, Vision Transformers process electron clouds,
and contrastive alignment methods unify modalities into a consistent embedding space.

This unified embedding provides a quantum-informed representation of molecules and complexes,
enabling more accurate downstream predictions. Preliminary experiments show superior performance
to state-of-the-art (GET), with +2.6% Pearson and +3.1% Spearman improvements in binding affinity
prediction.

2.2 Training with Quantized Embedding Spaces

The unified embeddings described above are continuous, which does not reflect the quantized nature of
quantum states. Molecular systems exhibit discrete energy levels governed by the Schrödinger
equation; electrons occupy orbitals defined by quantum numbers that determine interaction properties.

To align with this, we propose a quantized embedding training method (Figure 4). Using vector
quantization, embeddings of molecules with similar electron clouds are mapped to shared discrete
codes. Equivariant neural networks reconstruct molecular structures and electron clouds, while
invariant networks reconstruct geometric invariants (atom types, bond lengths, angles).

This quantized embedding space captures functional commonalities among small molecules, serving as
a generalized biomolecular functional code. Encodings derived here can predict drug–target affinities
and properties, strengthening foundations for downstream drug discovery and optimization.

3.   Molecular Generation and Optimization at the Quantum Scale

3.1 Electron-Cloud-Constrained Generative Models

The chemical space of drug-like molecules is vast, making exhaustive exploration infeasible.
Generative models such as VAEs, GANs, and diffusion models provide new tools but face challenges:
(i) sparse coverage of target-specific molecules, and (ii) poor capture of critical interactions, such as π–
π stacking between aromatic fragments and protein residues.
We propose electron-cloud-constrained generation (Figure 5), which offers: (i) Data expansion:
embedding quantum interactions allows diverse biomolecular data (small molecules, peptides, proteins,
DNA, RNA) to be leveraged, enriching training space. (ii) Interaction fidelity: constraints with electron
clouds enable reproduction of fundamental interactions, producing molecules with higher specificity
and stability.

Our two-stage strategy first uses a denoising diffusion model to generate electron cloud densities in
binding pockets. Second, using the unified representation (Section 2.1), we encode these clouds and
apply GPT-style autoregressive modeling to generate molecules that replicate desired features.

In training, electron clouds serve as unifying descriptors. Distributions computed via neural operators
(Section 1) act as supervision, while structures can be sourced from public data or AlphaFold
predictions. As shown in Figure 6, preliminary experiments confirm feasibility: distinct molecules were
generated while maintaining consistent electron cloud structures and binding interactions.

3.2 Quantum-Scale Optimization of Drug Candidates

Lead optimization is critical for enhancing binding affinity and selectivity while reducing off-target
effects. We propose a quantum-informed optimization framework (Figure 7), integrating quantum
energy landscapes (Section 1) and electron-cloud-constrained structural landscapes (Section 3.1).

Candidate molecules are optimized via energy-minimization sampling, achieving: (i) Functional
enhancement by minimizing property-related energy. (ii) Interaction preservation by maintaining
structural consistency with original binding. This dual-constraint approach ensures molecules retain
essential activity while improving efficiency, stability, and drug-likeness.

Conclusion

In summary, this project establishes a comprehensive research framework for quantum-scale AI-
assisted drug design. By combining neural operator models for electron cloud prediction, multi-scale
representation learning with quantized embeddings, and electron-cloud-constrained generative and
optimization models, the framework directly addresses the limitations of current AI-driven drug
discovery. The integration of quantum-scale information enriches data, improves accuracy, and
enhances interpretability, ultimately enabling the discovery, screening, and optimization of small-
molecule drugs with unprecedented precision.
             Figure 1 Overall Framework                     Figure 2 Neural Operator for Electron Cloud Prediction




                               Figure 3 Method for Multi-Scale Molecule Representation




       Figure 4 Training with Quantilization               Figure 5 Electron Cloud Constraint Molecule Generation


Method for Multi-Scale Molecule Representation          Method for Multi-Scale Molecule Representation




                            Figure 6 Results of Electron Cloud Constraint Molecule
                                                   Generation


                      Method for Multi-Scale Molecule Representation




           Figure 7 Quantum-Scale Property Model-Guided Molecular Optimization Framework


          Method for Multi-Scale Molecule Representation
A key industry collaborator for this project will be Insilico Medicine, a global leader in artificial
intelligence-driven drug discovery and development. Insilico has established itself as a pioneer in
applying generative models and reinforcement learning to pharmaceutical innovation, with a strong
track record of advancing drug candidates from computational design to preclinical and clinical stages.
Its end-to-end AI platform integrates target identification, molecular generation, and optimization, and
has already delivered promising candidates into phase I and II clinical trials. Partnering with Insilico
Medicine provides this project with a unique opportunity to ensure that the proposed quantum-scale AI
methodologies not only achieve academic significance but also demonstrate real-world translational
value.

The critical role of Insilico in this collaboration lies in bridging quantum-scale methodological
innovation with industry-level drug development pipelines. The project aims to develop neural-
operator-based electron cloud prediction, multi-scale quantum-informed representations, and electron-
cloud-constrained generative and optimization models. While these advances address fundamental
challenges in accuracy, interpretability, and efficiency of AI drug design, their true impact requires
integration into industrial workflows. Insilico’s infrastructure—including large-scale molecular
databases, high-performance computing resources, and automated medicinal chemistry platforms—will
enable rapid validation of the proposed methods on therapeutically relevant targets.

Moreover, Insilico’s expertise in targeted oncology, fibrosis, and infectious disease drug programs
directly complements this project’s objectives. By embedding quantum-scale insights into Insilico’s
generative AI pipelines, the collaboration will allow more precise prediction of molecular interactions,
faster identification of lead compounds, and enhanced optimization of drug-like properties. This not
only accelerates the discovery of next-generation targeted anticancer and antiviral agents but also
ensures alignment with pressing healthcare needs.

Through this partnership, the pledged impact of the project will be actualized in two dimensions. First,
by enhancing the efficiency and success rate of small-molecule discovery, thereby reducing time and
cost to clinical translation. Second, by contributing to healthcare resilience and preparedness, ensuring
that future drug discovery pipelines are capable of responding rapidly to emerging threats such as
pandemics. In sum, collaboration with Insilico Medicine guarantees that the scientific advances of this
Fellowship are transformed into tangible societal and healthcare benefits, bridging the gap between
frontier AI research and real-world therapeutic innovation.
The proposed research on quantum-scale AI-assisted drug design carries profound implications for
healthcare, with demonstrable benefits that extend well beyond academic boundaries. Small-molecule
therapeutics remain the cornerstone of modern medicine, providing effective interventions for cancer,
infectious diseases, cardiovascular disorders, and neurological conditions. Their unique ability to
penetrate cell membranes and act on intracellular targets makes them indispensable in clinical practice.
Yet, despite their importance, the pathways by which small molecules are discovered and optimized
remain slow, inefficient, and costly. Conventional pipelines require over a decade of effort and billions
of dollars in investment, while attrition rates in preclinical and clinical stages remain alarmingly high.
The healthcare impact of such inefficiency is significant: delayed access to life-saving treatments,
escalating costs of care, and unmet medical needs. By integrating quantum-scale physicochemical
information into AI models, this project seeks to offer a transformative shift in how drugs are discovered
and designed.

Accelerating Drug Discovery

Drug discovery is the first and most resource-intensive stage of therapeutic development. Traditional
approaches rely on high-throughput screening of massive chemical libraries or virtual docking methods
that oversimplify molecular interactions. These strategies often miss subtle yet decisive physicochemical
forces—hydrogen bonding, electrostatics, orbital interactions—that dictate whether a molecule will truly
bind to its target with therapeutic potential. As a result, enormous experimental effort is wasted on
molecules that ultimately fail. The proposed research directly addresses these limitations. By embedding
electron cloud distributions, orbital quantization, and energy discretization into AI frameworks, the
project aims to enhance predictive accuracy at the earliest stages of drug discovery. This ensures that
candidate molecules prioritized for synthesis and testing are more likely to succeed in binding affinity,
stability, and drug-likeness. The healthcare implications are immediate: timelines for identifying
promising leads can be shortened from years to months, resources can be conserved for the most viable
candidates, and patients can access novel therapies far sooner. The urgency of such innovation was made
clear during the COVID-19 pandemic. As SARS-CoV-2 spread globally, the world urgently sought
antiviral agents. Yet in the absence of robust discovery tools, researchers relied heavily on repurposing
existing drugs, many of which ultimately failed in clinical trials despite initial promise. The lack of new
small-molecule antivirals left millions of patients without effective oral treatments during the crucial
early phases of the pandemic. Quantum-informed AI models, capable of accurately capturing viral
protein–ligand interactions, could have dramatically accelerated the identification of viable drug
candidates. In future outbreaks, such platforms will provide healthcare systems with the ability to respond
to emergent pathogens within weeks, transforming the speed and effectiveness of global therapeutic
response.

Revolutionizing Drug Design

Beyond discovery, drug design focuses on refining initial hits into clinically viable therapies by
improving selectivity, stability, bioavailability, and safety. Traditionally, this stage relies on iterative
cycles of synthesis and testing, a process that is both slow and expensive. Even promising leads often
fail late in development due to unanticipated weaknesses in molecular stability or off-target effects. The
proposed research seeks to introduce a paradigm shift by applying electron-cloud-constrained generative
models to drug design. Rather than merely generating molecules that geometrically “fit” a binding pocket,
the models will account for quantum-scale constraints that underlie true stability and efficacy. This
approach allows the direct generation of drug candidates that are optimized for binding, stability, and
synthesizability from the outset. For healthcare, this means fewer failures in late-stage development,
reduced wastage of resources, and faster delivery of safe, effective therapies. Such advances hold
particular promise in oncology, where resistance mutations frequently undermine existing drugs.
Quantum-informed generative models can simulate how electronic rearrangements caused by mutations
alter drug binding, allowing the design of next-generation inhibitors tailored to resistant cancer variants.
For patients, this translates to extended survival and improved quality of life. Similarly, for rare and
neglected diseases—long overlooked due to the prohibitive costs of traditional design—more efficient
pipelines will enable new therapies where none currently exist.

Lessons from COVID-19 and Future Preparedness

The COVID-19 crisis underscored the fragility of healthcare systems when confronted with emergent
diseases and limited therapeutic pipelines. Vaccines proved powerful but were not sufficient alone;
challenges in logistics, hesitancy, and variant evolution highlighted the indispensable role of small-
molecule drugs. Yet such drugs were unavailable when most needed, in part due to the inadequacies of
current discovery and design methods. The proposed project has the potential to addresses this gap by
creating a platform that can rapidly generate and optimize drug candidates at scale. In future pandemics,
this capability could provide effective antivirals within months, easing the burden on hospitals and
reducing mortality. More broadly, it strengthens healthcare resilience by equipping systems with tools to
respond flexibly to crises, whether from emerging pathogens or the growing challenge of antimicrobial
resistance.

Additional Healthcare Benefits

While the core contributions of the project lie in transforming drug discovery and design, several
additional healthcare benefits follow naturally. More accurate predictions reduce the risk of toxicity and
off-target effects, improving patient safety and adherence. Lower development costs, achieved through
reduced attrition, translate into more affordable drugs and broader patient access.

Conclusion

In summary, the proposed research delivers contributions to healthcare that extend well beyond academia,
with its most profound impact in accelerating and improving drug discovery and design. By embedding
quantum-scale information into AI models, it has the potential to addresse the inefficiencies, delays, and
failures that have long plagued therapeutic development. Its applications range from targeted anticancer
drug design, pandemic preparedness to oncology, rare diseases, and beyond.
[1] Kim, S., Chen, J., Cheng, T., Gindulyte, A., He, J., He, S., Li, Q., Shoemaker, B.A., Thiessen, P.A.,
     Yu, B., Zaslavsky, L.Y., Zhang, J., & Bolton, E.E. (2020). PubChem in 2021: new data content and
     improved web interfaces. Nucleic Acids Research, 49, D1388 - D1395.

[2] Gaulton, A., Bellis, L.J., Bento, A.P., Chambers, J., Davies, M., Hersey, A., Light, Y., McGlinchey,
     S., Michalovich, D., Al-Lazikani, B., & Overington, J.P. (2011). ChEMBL: a large-scale bioactivity
     database for drug discovery. Nucleic Acids Research, 40, D1100 - D1107.

[3] Tingle, B.I., Tang, K.G., Castañón, M.L., Gutierrez, J.J., Khurelbaatar, M., Dandarchuluun, C., Moroz,
     Y.S., & Irwin, J.J. (2023). ZINC-22—A Free Multi-Billion-Scale Database of Tangible Compounds
     for Ligand Discovery. Journal of Chemical Information and Modeling, 63, 1166 - 1176.

[4] Jensen, F. (2017). Introduction to Computational Chemistry. John Wiley & Sons.

[5] Zhang, O., Jin, J., Lin, H., Zhang, J., Hua, C., Huang, Y., Zhao, H., Hsieh, C., & Hou, T. (2024).
     ECloudGen: Access to Broader Chemical Space for Structure-based Molecule Generation. bioRxiv.

[6] Jumper, J.M., Evans, R., Pritzel, A., Green, T., Figurnov, M., Ronneberger, O., Tunyasuvunakool, K.,
     Bates, R., Žídek, A., Potapenko, A., Bridgland, A., Meyer, C., Kohl, S.A., Ballard, A., Cowie, A.,
     Romera-Paredes, B., Nikolov, S., Jain, R., Adler, J., Back, T., Petersen, S., Reiman, D., Clancy, E.,
     Zielinski, M., Steinegger, M., Pacholska, M., Berghammer, T., Bodenstein, S., Silver, D., Vinyals,
     O., Senior, A.W., Kavukcuoglu, K., Kohli, P., & Hassabis, D. (2021). Highly accurate protein
     structure prediction with AlphaFold. Nature, 596, 583 - 589.

[7] Vaswani, A., Shazeer, N.M., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., &
     Polosukhin, I. (2017). Attention is All you Need. Neural Information Processing Systems.

[8] Dauparas, J., Anishchenko, I.V., Bennett, N.R., Bai, H., Ragotte, R.J., Milles, L.F., Wicky, B.I.,
     Courbet, A., de Haas, R.J., Bethel, N.P., Leung, P.J., Huddy, T.F., Pellock, S.J., Tischer, D.K., Chan,
     F., Koepnick, B., Nguyen, H., Kang, A., Sankaran, B., Bera, A.K., King, N.P., & Baker, D. (2022).
     Robust deep learning based protein sequence design using ProteinMPNN. Science (New York, N.Y.),
     378, 49 - 56.

[9] Watson, J.L., Juergens, D., Bennett, N.R., Trippe, B.L., Yim, J., Eisenach, H.E., Ahern, W., Borst,
     A.J., Ragotte, R.J., Milles, L.F., Wicky, B.I., Hanikel, N., Pellock, S.J., Courbet, A., Sheffler, W.,
     Wang, J., Venkatesh, P., Sappington, I., Torres, S.V., Lauko, A., De Bortoli, V., Mathieu, E.,
     Ovchinnikov, S., Barzilay, R., Jaakkola, T., DiMaio, F., Baek, M., & Baker, D. (2023). De novo
     design of protein structure and function with RFdiffusion. Nature, 620, 1089 - 1100.

[10] Guan, J., Qian, W.W., Peng, X., Su, Y., Peng, J., & Ma, J. (2023). 3D Equivariant Diffusion for
     Target-Aware Molecule Generation and Affinity Prediction. ArXiv, abs/2303.03543.

[11] Lin, H., Huang, Y., Liu, M., Li, X.C., Ji, S., & Li, S. (2022). DiffBP: Generative Diffusion of 3D
     Molecules for Target Protein Binding. ArXiv, abs/2211.11214.
[12] Peng, X., Luo, S., Guan, J., Xie, Q., Peng, J., & Ma, J. (2022). Pocket2Mol: Efficient Molecular
     Sampling Based on 3D Protein Pockets. International Conference on Machine Learning. PLMR,
     17644-17655.

[13] Lin, H., Huang, Y., Zhang, O., Liu, Y., Wu, L., Li, S., Chen, Z., & Li, S. (2024). Functional-group-
     based diffusion for pocket-specific molecule generation and elaboration. Advances in Neural
     Information Processing Systems, 36.

[14] Guan, J., Zhou, X., Yang, Y., Bao, Y., Peng, J., Ma, J., Liu, Q., Wang, L.& Gu, Q. (2023).
     DECOMPDIFF: Diffusion Models with Decomposed Priors for Structure-Based Drug Design.
     International Conference on Machine Learning.

[15] Corso, G., Stärk, H., Jing, B., Barzilay, R., & Jaakkola, T. (2023). DiffDock: Diffusion Steps, Twists,
     and Turns for Molecular Docking. The Eleventh International Conference on Learning
     Representations.

[16] Kong, X., Huang, W., & Liu, Y. (2024). Generalist Equivariant Transformer Towards 3D Molecular
     Interaction Learning. International Conference on Machine Learnings.

[17] Abramson, J., Adler, J., Dunger, J., Evans, R., Green, T., Pritzel, A., Ronneberger, O., Willmore, L.,
     Ballard, A.J., Bambrick, J., Bodenstein, S.W., Evans, D.A., Hung, C., O’Neill, M., Reiman, D.,
     Tunyasuvunakool, K., Wu, Z., Žemgulytė, A., Arvaniti, E., Beattie, C., Bertolli, O., Bridgland, A.,
     Cherepanov, A., Congreve, M., Cowen-Rivers, A.I., Cowie, A., Figurnov, M., Fuchs, F.B., Gladman,
     H., Jain, R., Khan, Y.A., Low, C.M., Perlin, K., Potapenko, A., Savy, P., Singh, S., Stecula, A.,
     Thillaisundaram, A., Tong, C., Yakneen, S., Zhong, E.D., Zielinski, M., Žídek, A., Bapst, V.T., Kohli,
     P., Jaderberg, M., Hassabis, D., & Jumper, J.M. (2024). Accurate structure prediction of
     biomolecular interactions with AlphaFold 3. Nature, 630, 493 - 500.

[18] Tortorella, S., Carosati, E., Sorbi, G., Bocci, G., Cross, S., Cruciani, G., & Storchi, L. (2021).
     Combining machine learning and quantum mechanics yields more chemically aware molecular
     descriptors for medicinal chemistry applications. Journal of Computational Chemistry, 42, 2068 -
     2078.
