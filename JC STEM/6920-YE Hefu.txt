Dr. <PERSON><PERSON>, Research Assistant
           Department of Mechanical Engineering, The University of Hong Kong, Hong Kong
           Address: Pokfulam Road, Hong Kong, ✆+86-19936078191, 🖂 <EMAIL>, <EMAIL>
           Date of Birth: 24/10/1996; Marital status: Married; Nationality: China
             ORCID: 0000-0002-5927-9574, Researcher ID: ABM-7215-2022

I.         Education
       •   Ph.D, Control Science and Engineering, Chongqing University, China, 01/09/2019-25/06/2025, 6 years;
           Dissertation title: Time-varying feedback based prescribed-time control for complex nonlinear systems;
           (Supervisor: Prof<PERSON>, IEEE Fellow, EIC of IEEE Transactions on Neural Networks and
           Learning Systems and EIC of Journal of Automation and Intelligence).
       •   B.Eng, Measurement and Control Technology and Instruments, Harbin Institute of Technology, China,
           01/09/2015-01/06/2019, 4 years.
II.        Professional Experience
       •   Research Assistant, Department of Mechanical Engineering, The University of Hong Kong, Hong Kong,
           China. 12/01/2025-now, 9 months; (Supervisor: Prof<PERSON> <PERSON>, IEEE Fellow, EIC of Journal of the
           Franklin Institute and EIC of IET Control Theory & Applications).
       •   Visiting Ph.D. Student, Electrical & Electronic Engineering, Nanyang Technological University,
           Singapore, 10/10/2022-10/10/2023, 12 months; (Supervisor: <PERSON><PERSON>, IEEE Fellow, Co-EIC
           of IEEE Transactions on Industrial Electronics).
III.       Research Interests
       •   Robotics, nonlinear systems, networked systems, interconnected systems, UGVs, UAVs, MIMO systems,
           multi-agent systems, missile guidance & control systems.
       •   Adaptive control, decentralized control, distributed control, prescribed-time control, event-triggered control,
           sliding mode control, optimal control, prescribed performance control, funnel control.
IV.        Professional Activities
       •   Publishing Chair, Joint International Conference on Automation Intelligence Safety & International
           Symposium on Autonomous Systems, Xi’an, China, 2025.
       •   Office Assistant, IEEE Transactions on Neural Networks and Learning Systems, January 2023 to now.
       •   Editorial Board Member: International Transactions on Electrical Energy Systems
       •   Editorial Board Member: Journal of Applied Mathematics
       •   Volunteer, Joint International Conference on Automation-Intelligence-Safety & International Symposium
           on Autonomous Systems, 2024.
       •   Outstanding Reviewer, Chinese Conference on Swarm Intelligence and Cooperative Control, 2021.
       •   The reviewer of more than 30 peer-reviewed journals, for instance:
           Automatica;
           IEEE Transactions on Automatic Control;
           IEEE Transactions on Control of Network Systems;
           IEEE Transactions on Neural Networks and Learning Systems;
           IEEE Transactions on Systems, Man, and Cybernetics: Systems;
           IEEE Transactions on Fuzzy Systems;
           IEEE Transactions on Cybernetics;
           IEEE Transactions on Circuits and Systems II: Express Briefs;
           IEEE Transactions on Intelligent Transportation Systems;
           IEEE Transactions on Industrial Electronics;
           IEEE/ASME Transactions on Mechatronics;
           IEEE Control Systems Letters;
          IEEE/CAA Journal of Automatica Sinica;
          International Journal of Robust and Nonlinear Control;
          System and Control Letters;
          SIAM Journal on Control and Optimization;
          European Journal of Control;
          Asian Journal of Control;
V.        Monograph
      [1] Yongduan Song, Kai Zhao, Hefu Ye, Control of Nonlinear Systems: Stability and Performance, CRC Press,
          Taylor & Francis Group, 2024.
VI.     Published Journals
     I have published 16 peer-reviewed SCI articles (14 as the first author). The Google Scholar Citations
are 630, h-index 11 & i10-index 13. (Ref - https://scholar.google.com/citations?user=7HAlG_gAAAAJ&hl=en)
     Area 1 – Prescribed-Time Control for Single Plants
     These papers have laid a scientific foundation for the prescribed-time control of various nonlinear systems,
exploring the advantages of time-varying feedback in rejecting external disturbances, handling nonlinear coupling,
offsetting filtering errors, and other aspects. They provide support for the realization of O2 of the CCHUS project.
     [1] Hefu Ye, and Yongduan Song, Prescribed-time tracking control of MIMO nonlinear systems with non-
         vanishing uncertainties, IEEE Transactions on Automatic Control, vol. 68, no. 6, pp. 3664-3671, 2023.
         (JCR Q1, IF: 7, Citations: 109, ESI highly cited paper).
     [2] Hefu Ye, and Yongduan Song, A separation principle for prescribed-time stabilization of nonlinear
         systems, Automatica, vol. 171, Art. 111983, 2025. (JCR Q1, IF: 5.9, Citations: 5).
     [3] Yongduan Song, Hefu Ye, and Frank L. Lewis, Prescribed-time control and its latest developments, IEEE
         Transactions on Systems, Man, and Cybernetics: Systems, vol. 53, no. 7, pp. 4102-4116, 2023. (JCR Q1,
         IF: 8.7, Citations: 215, ESI highly cited paper, 2025 IEEE Andrew P. Sage Best Transactions Paper Awards)
     [4] Hefu Ye, and Yongduan Song, Prescribed-time control for linear systems in canonical form via nonlinear
         feedback, IEEE Transactions on Systems, Man, and Cybernetics: Systems, vol. 53. no. 2, pp. 1126-1135,
         2023. (JCR Q1, IF: 8.7, Citations: 40).
     [5] Hefu Ye, and Yongduan Song, Backstepping design embedded with time-varying command filters, IEEE
         Transactions on Circuits and Systems II: Express Briefs, vol. 69, no. 6, pp. 2832-2836, 2022. (JCR Q2,
         IF: 4, Citations: 27).
     [6] Hefu Ye, and Yongduan Song, Prescribed-time control of uncertain strict-feedback-like systems,
         International Journal of Robust and Nonlinear Control, vol. 31, no. 11, pp. 5281-5297, 2020. (JCR Q2,
         IF: 3.2, Citations: 51).
     Area 2 – Decentralized and Distributed Control for Networked Systems
     These papers have established a scientific foundation for decentralized or distributed control of networked
systems under interaction or communication, addressing controller design issues such as unknown interconnected
strength, directed communication, and unknown distributed loads. They provide support for the realization of O1
and O2 of the CCHUS project.
     [7] Hefu Ye, Changyun Wen, and Yongduan Song, Decentralized and distributed control of large-scale
         interconnected multi-agent systems in prescribed time, IEEE Transactions on Automatic Control, vol. 70,
         no. 2, pp. 1115-1130, 2025. (JCR Q1, IF: 7, Citations: 24, ESI highly cited paper)
     [8] Hefu Ye, Changyun Wen, and Yongduan Song, Decentralized prescribed-time control for interconnected
         nonlinear systems via output-feedback, Automatica, vol. 163, Art. 111571, 2024. (JCR Q1, IF: 5.9,
         Citations: 29).
     [9] Hefu Ye, Changyun Wen, James Lam, and Petros Ioannou, Decentralized prescribed-time input-to-state
         stabilization for interconnected normal form nonlinear systems, Automatica, vol. 180, Art. 112480, 2025.
         (JCR Q1, IF: 5.9).
   [10] Hefu Ye, Yongduan Song, Petros Ioannou, and James Lam, Decentralized prescribed-time control of
        robotic arm-finger systems for grasping and moving tasks, IEEE Transactions on Cybernetics, vol. 55, no.
        9, pp. 4386-4399, 2025.. (JCR Q1, IF: 10.5).
     Area 3 – Adaptive Control for Performance Improvement
     These papers have laid a scientific foundation for adaptive control of nonlinear systems and its integration
with prescribed-time control. They provide insights for designing adaptive prescribed-time safety filters, thereby
supporting the achievement of O2 of the CCHUS project.
   [11] Hefu Ye, and Yongduan Song, Adaptive control with guaranteed transient behavior and zero steady-state
        error for systems with time-varying parameters, IEEE/CAA Journal of Automatica Sinica, vol. 9, no. 6,
        pp. 1073-1082, 2022. (JCR Q1, IF: 19.2, Citations: 25).
   [12] Hefu Ye, and Yongduan Song, Prescribed-time control for time-varying nonlinear systems: A temporal
        scaling based robust adaptive approach, System & Control Letters, vol. 181, pp. 105602, 2023. (JCR Q3,
        IF: 2.5, Citations: 25).
   [13] Hefu Ye, Kai Zhao, Haijia Wu, and Yongduan Song, Adaptive control with global exponential stability for
        parameter-varying nonlinear systems under unknown control gains, IEEE Transactions on Cybernetics,
        vol. 53, no. 12, pp. 7858-7867, 2023. (JCR Q1, IF: 10.5, Citations: 30).
   [14] Lan Cao, Xiucai Huang, Hefu Ye, and Yongduan Song, Neuroadaptive asymptotic tracking control with
        guaranteed performance under mismatched uncertainties and saturated inputs, IEEE Transactions on
        Systems, Man, and Cybernetics: Systems, vol. 53, no. 6, pp. 3784-3794, 2023. (JCR Q1, IF: 8.7, Citations:
        10).
     Area 4 – Matrix Pencil Analysis Method for Conservativeness Reduction
     These papers conducted preliminary research on the application of matrix pencil formulations in stability
analysis, discussing how matrix pencils can simplify analysis and reduce the conservatism of design parameters. As
such, they provide support for the achievement of O1 of the CCHUS project.
   [15] Hefu Ye, Yongduan Song, Zhirong Zhang, and Changyun Wen, Global dynamic event-triggered control
        for nonlinear systems with sensor and actuator faults: A matrix pencil based approach, IEEE Transactions
        on Automatic Control, vol. 69, no. 3, pp. 2007-2014, 2024. (JCR Q1, IF: 7, Citations: 26).
   [16] Hefu Ye, Marios M. Polycarpou, and Changyun Wen, Matrix pencil based robust control for feedforward
        systems with event-triggered communications and sensor/actuator faults, Journal of Automation and
        Intelligence, vol. 2, no. 3, pp. 139-145, 2023.
VII.    Conferences
   [1] Lan Cao, Xiucai Huang, Hefu Ye, and Yongduan Song, Prescribed performance RISE-based control of
       euler-lagrange systems under saturation, 2022 5th International Symposium on Autonomous Systems
       (ISAS), Hangzhou, China, 2022, pp. 1-6.
   [2] Yan Tan, Liucang Wu, Bing Zhou, and Hefu Ye, Prescribed-time output-feedback tracking for nonlinear
       systems via a novel time-varying gain, 2024 IEEE International Conference on Industrial Technology
       (ICIT), Bristol, United Kingdom, 2024, pp. 1-6.
   [3] Hefu Ye, Yuanqing Wang, and Zhirong Zhang, Decentralized time-varying formation tracking for
       disturbed LTI systems in prescribed time, 2025 China Automation Congress, Harbin, accepted.

VIII. Research Grants
    These projects investigated advanced control algorithms, deep reinforcement learning, and autonomous
unmanned systems, while also discussing the deployment of advanced control algorithms in practical systems.
They provide support for the achievement of O3 of the CCHUS project.
    [1] Adaptive Spatiotemporal Constraint Control for Complex Nonlinear Systems with Fast-Varying
        Parameters, 10,000 RMB, 2022-2024, Principal Investigator.
    [2] Research on Skill Learning and Autonomous Development of Dexterous Arm-Finger Robots, Ministry of
        Science and Technology of the People’s Republic of China, 7,000,000 RMB, 2023-2025, Participant.
      [3] Research on the Efficacy and Reliability of Control Systems Driven by Neural Networks, National Natural
          Science Foundation of China, 1,600,000 RMB, 2023-2024, Participant.
      [4] Research on Advanced Control Theory and Autonomous Collaboration Strategies for Unmanned Systems
          in Dynamic Environments, National Natural Science Foundation of China, 3,010,000 RMB, 2020-2024,
          Participant.
      [5] Control Theory Based on Brain Operant Conditioning and Its Application in Flexible and Dexterous
          Underdriven Robot Systems, National Natural Science Foundation of China, 2,420,000 RMB, 2019-2023,
          Participant.
IX.       Prizes and Awards
      •    2025: IEEE Andrew P. Sage Best Transactions Paper Awards, IEEE SMC Society, 500 USD;
      •    2024: Outstanding Ph.D. Graduate of Chongqing Municipality;
      •    2024: Outstanding Ph.D. Graduate of Chongqing University;
      •    2024: National Scholarship for Ph.D. Students, 30,000 RMB, Ministry of Education, China;
      •    2024: First-Class Scholarship for Ph.D. Students from Chongqing University, 10,000 RMB;
      •    2024: First-Class Scholarship for China Instrument and Control Society, 3,000 RMB;
      •    2023: First-Class Scholarship for Ph.D. Students from Chongqing University, 10,000 RMB;
      •    2022: National Scholarship for Ph.D. Students, 30,000 RMB, Ministry of Education, China;
      •    2022: First-Class Scholarship for Ph.D. Students from Chongqing University, 10,000 RMB;
      •    2022: National Scholarship for studying abroad, 26,400 SGD, China Scholarship Council;
      •    2020: First-Class Scholarship for Master’s Students from Chongqing University, 8,000 RMB.
(a) Goals and Objectives – state the goal and objectives of the project, which include
    details on the specific change to be created by this project and how realistic and
    ambitious the project is.
   The “Cooperative Control of Heterogeneous Unmanned Systems under Complex Tasks”
(CCHUS) project addresses a critical gap in the research of autonomous unmanned systems by
establishing a rigorous scientific foundation for safety and stability analysis, control design,
and experimental validation, aspects that remain underdeveloped in existing literature. Current
studies often lack integrated frameworks capable of ensuring both safety and stability in
complex, large-scale heterogeneous systems with real-world constraints. To tackle these
challenges and advance beyond the current state of the art, the project is structured around the
following three specific objectives (O), each of which introduces significant scientific
challenges and aims to achieve breakthroughs in key areas:
   O1: Construct Control Barrier functions (CBFs) and Control Lyapunov Functions (CLFs)
for concurrent safety guarantee and stability analysis. Challenge: To develop summation-type
CBFs and CLFs for heterogeneous models with interaction, communication and task
dependency, all of which may undermine safety and stability. While CBFs and CLFs are widely
used for safety analysis in closed-loop systems, their direct application becomes challenging
when considering large-scale networked systems with interconnected subsystems featuring
coupling, communication, and task dependencies. Inter-system coupling, insufficient available
information, and additional time-varying characteristics complicate the use of classical CBFs
and CLFs. Furthermore, if the safety analysis framework also incorporates requirements for
low conservatism in control parameters and computational efficiency, the complexity increases
significantly. High-dimensional matrices, unmeasurable states, the computational complexity
of recursive methods, and the construction and reprocessing of specialized matrices will be
other scientific challenges addressed in O1.
    O2: Develop high-level decentralized and distributed prescribed-time synthesis control
strategies for heterogeneous models to accomplish desired tasks. Challenge: Correlated some
novel design techniques with the analysis methods of O1 for the development of complete
advanced algorithms. Decentralized control and distributed control serve as two fundamental
paradigms for dealing with interconnected systems and multi-agent systems, respectively.
Among these, prescribed-time control offers an effective means to manage task dependencies
in multi-system operations, owing to its ability to arbitrarily prescribed task completion times.
However, few existing control methods demonstrate effectiveness when applied to nonlinear
multi-agent systems with dynamic interactions. Furthermore, the inherent heterogeneity of such
systems, whether in terms of varying system orders or distinct nonlinear structures, introduces
significant additional challenges in controller design. The major challenge in O2 involves
developing comprehensive decentralized and distributed control methodologies along with
prescribed-time safety control approaches, while ensuring complete compatibility with the
analytical framework established in O1.
   O3: Develop a joint algorithm for trajectory generation and advanced precision control:
reinforcement learning (RL) for high-level control with PID for low-level execution. Challenge:
The primary challenge in O3 will be to experimentally validate the proposed algorithm on
physical unmanned aerial vehicle (UAV) and unmanned ground vehicle (UGV) platforms. This
validation must address several critical technical issues: the non-differentiability of desired
trajectories derived from real-world perception data, potential architectural mismatches
between the high-level RL controller and the low-level PID controller, and the necessity for
improved robustness against external disturbances. Although conventional PID control can be
successfully deployed in real systems, applying advanced user-designed algorithms remains
challenging. This is primarily because most industrial and commercial UAV/UGV platforms
do not offer open motor control loops. In such closed-architecture systems, it is generally not
feasible to implement standard advanced control strategies.
(b) Timeliness and Novelty – provide background of the project; and describe why this
    research is important and should be conducted now.
   Autonomous unmanned systems have emerged as a focal point of global technological
competition in the 21st century, with their level of advancement directly influencing a nation’s
or region’s position in the future economic and technological landscape. Hong Kong has
identified intelligent systems and robotics as a key strategic focus in its latest policy address,
with substantial funding allocated to research and development in emerging technologies. In
the context of smart city development, autonomous unmanned systems play an integral role in
enhancing urban management, logistics, public safety, and infrastructure resilience. To
strengthen Hong Kong’s role as an international innovation hub, autonomous unmanned
systems must be advanced through coordinated efforts across multiple dimensions, among
which swarm control is recognized as one of the most critical research challenges [1].
   In recent years, the fields of autonomous unmanned systems and robotics have witnessed
significant advancements, driven by advanced control theory or reinforcement learning (RL).
While single-unit autonomous systems have achieved considerable maturity, the scalability and
coordination of heterogeneous swarms, such as collaborative teams of UAVs and UGVs,
introduce unprecedented challenges in control stability, operational safety, and real-time
adaptability [2]. A critical and often overlooked issue is that even highly advanced single-unit
control algorithms frequently fail when deployed within interconnected multi-agent systems.
This performance degradation stems from the lack of a unified theoretical foundation and
integrated design framework capable of addressing the complexities of swarm interactions, with
significant research gaps persisting across three core areas: safety verification, high-level
cooperative control, and low-level actuation integration [3].
   From a control perspective, both UAVs and UGVs consist of multiple coupled subsystems
(pose and dynamics subsystem, actuator subsystem, actuator-environment interaction
subsystem). Designing safe and high-performance control algorithms is a challenging problem
with both practical and theoretical significance. Current approaches to ensuring swarm stability
can be broadly categorized into two paradigms: analytical methods rooted in advanced control
theory and data-driven methods based on RL [4]. While analytical methods offer generalizable
insights and rigorous performance guarantees, they often depend on idealized simplifications
that limit real-world applicability. On the other hand, RL-based techniques excel in handling
model uncertainties and adaptive scenarios but tend to produce solutions that are poorly
transferable and lack interpretability. At the high-control level, distributed cooperative
frameworks have made significant strides in enabling adaptive and precise swarm operations
in dynamic environments. At the low-control level, advances in high-precision sensing, high-
torque actuation, and modular software architectures, such as the Robot Operating System
(ROS), have facilitated robust hardware-software integration and rapid prototyping.
   The accelerating global deployment of autonomous systems across sectors, from smart city
infrastructure and logistics to emergency response and environmental monitoring, highlights
the urgent need for scalable, safe, and adaptive swarm control solutions. This is especially
critical for heterogeneous systems involving collaborative UAV/UGV teams, which offer
enhanced capability but compounded complexity. By addressing the intertwined theoretical and
practical challenges in stability analysis, cooperative control, and actuation design, this research
will not only contribute to fundamental control theory but also support Hong Kong’s strategic
goals in innovation and technology. Conducting this research now is imperative to build local
technological capacity, foster international collaboration, and ensure Hong Kong remains
competitive in the global smart city and autonomous systems arena.
(c) Methodology – provide a detailed description of the exact work to be completed.
    Describe the programme of work, indicating the research to be undertaken and the
    milestones that can be used to measure progress. Detail the methodology to be used
    in pursuit of the research and justify this choice. What similar research is being/
    has been undertaken nationally and internationally and how does your project
    differ?
   To establish robust scientific foundations at the forefront of collaborative unmanned
systems research, three critical challenges must be addressed: comprehensive safety
analysis, advanced high-level control design, and precise low-level actuation
coordination; thus, to achieve the objectives in Section C(a), the overall methodology
presented in Figure 1 involves three key steps, and the following parts will review
similar ongoing and completed research efforts from both domestic and international
sources addressing these three challenges, as well as outline the methods we intend to
adopt.
   Comprehensive Safety Analysis: Comprehensive safety analysis requires the closed
loop system to be stable and the states to always remain within the safe region, thus
encompassing both stability analysis and safety considerations [5]. For a single plant
system, safety is usually captured by introducing constraints on system states and the
environment. For multi-agent systems, the meaning of safety extends to inter-agent
interactions [6]. To address uncertainties caused by external disturbances, unmodeled
dynamics, and interactions, Control Lyapunov Functions (CLFs) provide a crucial
theoretical foundation for stability analysis. Safety in control systems is often certified
through Control Barrier functions (CBFs), which strictly ensure the safety of the closed
loop system at all times by enforcing the boundedness of the inner product between the
derivative of the CBFs and the vector field of the controlled system [7]. Additionally,
defining constraints simultaneously through CLFs and CBFs has gradually emerged as
a viable approach. This method ensures stability and safety (collision avoidance) across
all states of each subsystem while automatically balancing these two aspects to
maximize robustness [8].
   Advanced High-Level Control Design: Modern applications of autonomous
unmanned systems typically involve a hybrid control system with a low-level PID
control loop and a high-level user-specified control loop for specifying the velocity (or
position) command [9]. To minimize reliance on communication resources and ensure
scalability in interconnected systems, it is essential to develop high-level distributed
control or decentralized control algorithms for achieving synchronization, formation,
and containment [10]. Meanwhile, to accomplish complex tasks, it is often necessary
to dynamically and efficiently allocate tasks across multiple time intervals. In terms of
task coordination and allocation, certain finite-time control schemes have garnered
significant attention. Notably, finite-time control [11] and fixed-time control [12] may
hinder the smooth transition between tasks due to their conservative estimation of the
upper bound on convergence time. In contrast, the prescribed-time control algorithm
can more accurately estimate the convergence time [13] and avoid the input chattering
caused by other finite-time control algorithms, making it more favourable for high-level
control in unmanned systems.
   Precise Low-Level Actuation Coordination: Most industrial/commercial unmanned
systems do not have an open motor control loop, making standard high-level control
generally unachievable in unmanned systems with closed architectures [14]. A common
solution is to use advanced control algorithms for dynamic compensation, but this
requires the assumption that the combination of advanced and low-level control is
stable and that dynamic effects are negligible. However, this specific assumption only
holds if the given task is slow enough [15]. Additionally, the desired trajectory in the
task space needs to be decoupled through inverse kinematics to convert it into the
reference signals required for controller design. Yet, unknown system dynamics hinder
such conversion [16]. Trajectory generation schemes based on reinforcement learning
strategies can effectively integrate sensor information and generate reference signals
for the controller in real time [17]. In fact, the integration of reinforcement learning and
control has demonstrated unique advantages in multiple practical applications, such as
UAVs and UGVs.
   The methodologies to be used for each key step are given as follows.
   Safety Guarantee and Stability Analysis Framework (O1): To simultaneously ensure
safety and stability within a unified analytical framework, this project employs a
combined CBF-CLF method [18]. This method allows CBFs and CLFs to be designed
independently and then systematically integrated, providing flexibility to adapt to
different task requirements. Given the challenge of partial observability, each CLF and
CBF primitive is designed to incorporate both observer states and observer errors. The
core of the integration lies in formulating a unified control framework that harnesses
the stability guarantees of CLFs while rigorously enforcing safety constraints via CBFs.
Moreover, since the system involves multiple heterogeneous units, the overall
composite function is constructed through a weighted summation of multiple primitive
functions. Additionally, we embed the matrix pencil formulation [19] and sum-of-
squares [20] methods into the stability analysis. The matrix pencil technique captures
detailed system dynamics directly from matrix elements, eschewing conventional
norm-based approximations, while the sum-of-squares approach utilizes this refined
data to construct rigorously certified stability proofs via polynomial Lyapunov
functions. This integration reduces the conservatism of control design parameters while
maintaining computational efficiency, thereby facilitating automatic balancing between
safety and stability. These two techniques, proposed by the applicant and his host
supervisor respectively, represent a key highlight of this project.
   Decentralized and Distributed Prescribed-Time Synthesis Control (O2): To enable
the collaborative operation of multiple UAVs and UGVs in complex environments, this
project proposes a hierarchical control architecture. The first layer is a decentralized
control [21] layer for individual units, where local controllers are developed based on
the dynamic properties of each interconnected unit. It will fully leverage time-varying
feedback to suppress unknown strengths of interactions, thereby theoretically
addressing the issue of algorithm failure in practical applications caused by
heterogeneous nonlinear functions within the same class of models. The second layer
is a distributed formation control [22] layer, responsible for managing topological
constraints and cooperative motion control under communication. This project
specifically addresses the design of a fully distributed algorithm for scenarios where
the leader’s input is non-zero, accounting for practical situations in which the leader
must also accomplish specific tasks. The third layer introduces the novel time-space
deformation [23] approach to reformulate the prescribed-time control problem in the
original time domain into an asymptotic control problem in a new infinite-time domain.
This transformation preserves the desired system stability while enabling direct
application of the Lyapunov-based stability analysis framework developed in O1 for
controller design. In particular, we incorporate solutions to a class of dual algebraic
Riccati equations and integrate the nonlinear output regulation method [24] into the
controller to address challenges stemming from model heterogeneity. Additionally, a
safety filter [25] is introduced to enforce state constraints over an a priori prescribed
time horizon. The prescribed-time controller and filter are designed to effectively reject
external disturbances without relying on prior knowledge of disturbance bounds,
thereby enabling practical deployment of the algorithm.
   RL for High-Level Control with PID for Low-Level Execution (O3): To bridge the
critical gap between theoretical development and practical implementation, this project
proposes a novel dynamic model and RL dual-driven composite control strategy [26].
In this architecture, the high-level RL module receives observation information and is
responsible for generating desired commands. Simultaneously, a user-designed high-
level controller refines these commands based on the dynamic model of the
heterogeneous unmanned system, producing modified PID control inputs for the
underlying actuators (see Figure 2). Note that the advanced controller designed in O2
will no longer be directly used to generate control commands. Instead, it will produce
a new dynamic system as a reference trajectory for the underlying PID controller to
execute. This approach ensures compatibility with the low-level closed architecture.
The combination of the high-level advanced algorithm and data-driven RL leverages
their respective strengths, thereby enhancing the robustness of the heterogeneous
unmanned system and making it more resilient to external disturbances. Additionally,
this cyber-physical control framework [27] facilitates the smooth deployment of
commands generated by the high-level control algorithm on the underlying PID motor
controllers, reducing the complexity of experimental validation and improving
feasibility. The framework enables the algorithm to achieve obstacle-avoidance
tracking and cooperative control of heterogeneous UAVs and UGVs within 30 seconds,
with steady-state errors in the control vectors remaining below 5%. It also demonstrates
robustness against system parameter variations of up to 50%. This control strategy will
be validated on the Crazyflie and Quanser experimental platforms at the Chinese
University of Hong Kong, both of which support continuous operation for over 30
minutes.
   The CCHUS project constitutes an ambitious research initiative that not only
establishes fundamental theoretical frameworks but also delivers experimentally
validated control architectures for autonomous unmanned systems under complex tasks.
It bridges the critical gap between theoretical development and practical
implementation, providing both conceptual advancements and experimentally
verifiable solutions for autonomous systems under challenging real-world conditions.
The main aspects that CCHUS goes beyond the state-of-the-art are, as follows:
   1) The majority of studies uses either CLFs or CBFs for analyzing stability and safety
separately, whereas we will employ a unified CLF-CBF framework to concurrently
evaluate both stability and safety while exploring methods to automatically balance
these two aspects for enhanced practicality.
   2) The study pioneers the integration of matrix pencil formulation and sum-of-
squares methods in safety analysis, simultaneously reducing conservatism in control
design while advancing the modeling and resolution of associated polynomial
optimization problems.
   3) Applying the concept of decentralized and distributed prescribed-time synthesis
control in heterogeneous autonomous unmanned systems has never been done before.
   4) Combine reinforcement learning, high-level control, and low-level PID actuation
into a novel control framework, overcoming the limitations of traditional high level-
only approaches. It solves practical technical challenges while enabling the HK to
integrate other advanced algorithms for different control tasks in the future.
(d) Images and Diagrams – upload any images and/or diagrams related to your
   project that add value to your application. Any images/ diagrams uploaded must
   be referenced in the application form. The images/ diagrams must be collated and
   uploaded as a single PDF in the order you wish them to be viewed.




                             Figure 1 Overall Methodology




                         Figure 2 Control Framework in 02 and O3
(e) Project Timeline – a Gantt chart or equivalent to show the schedule of activities
    for the duration of the Fellowship. Please include all annual reports (for the report
    period ending 30 September to be submitted on or before 31 October) and ensure
    major milestones are clearly plotted.
  The CCHUS project is organized in 3 work packages (WPs) (see Table 1). Three specific
objectives (O1 to O3) listed in Section C(a) are planned in WP1 to WP3, respectively.
Table 1 Work package arrangements
  The Gantt chart below displays the project schedule in accordance with Table 1.
    WP1                    O1: Safety Guarantee and Stability Analysis Framework
                 T1.1: Combined CLF-CBF analysis framework
     Tasks
                 T1.2: Matrix pencil and Sum-of-Square method for low-conservative design
  Deliverables   D1: Reports on Lyapunov function design criteria and advanced analysis approach
                 M1: Derivation of effective summation-type CLF-CBF methods, and effective integration of
   Milestones
                 matrix pencils and sum of squares
     WP2             O2: Decentralized and Distributed Prescribed-Time Synthesis Control
                 T2.1: Decentralized and distributed synthesis control design for heterogeneous systems
     Tasks       T2.2: Time-space deformation based prescribed-time control design
                 T2.3: Nonlinear output regulation based prescribed-time safety filter design
                 D2: Reports on decentralized and distributed prescribed-time control algorithms and
  Deliverables
                 verification results, and two manuscripts for submitting
   Milestones    M2: Development of the decentralized and distributed prescribed-time control algorithm
     WP3               O3: RL for High-Level Control with PID for Low-Level Execution
                 T3.1: Dynamic model and RL dual-driven composite control strategy
     Tasks       T3.2: Integrate user-designed high-level controller and the lower-level PID motor execution
                 T3.3: Mathematical simulation and experimental platform verification
                 D3: Development of the learning-based upper-level control design with lower-level PID
  Deliverables
                 control deployment, and two manuscripts for submitting
   Milestones    M3: Cooperative control for UAVs and UGVs realization

  The Gantt chart in Section C(e) describes the timeline of 3 WPs for the CCHUS project.
   Year              Year1                        Year2                       Year3
  Month      1-4       5-8      9-12    13-16     17-20    21-24     25-28    29-32     33-36
              T1.1
   WP1                           T1.2
                              D1/M1
                                          T2.1
                                                     T2.2
   WP2
                                                              T2.3
                                                          D2/M2
                                                                        T3.1
   WP3                                                                           T3.2
                                                                                            T3.3
                                                                                       D3/M3
  The technical body of the CCHUS project is scheduled in WP1 (12 months), WP2 (12
months) and WP3 (12 months), corresponding to three specific objectives. For WP1 and WP2,
there is 3 months overlap between T1.2 and T2.1, because during deriving a simple yet effective
UAV/UGV model, the interactions and communication topology needs to be revisited. For
WP2, since I have rich experience in the decentralized control, distributed control, and
prescribed-time control, allocating 3 months for T2.1-T2.3 is proper. Considering that it is not
easy to apply the high-level user-designed control algorithm to the actual UAV and UGV
platforms, we have decided to take one year to complete this task in WP3.
(f) Potential Collaborator(s) in Hong Kong – please name the potential industry
   collaborator(s) you will create and/ or maintain during the Fellowship period.
   Provide details on how the collaborator will play a critical role in the project and
   actualise the pledged impact. The potential industry collaborator(s) may be the host
   of your translational research industry attachment.
     We are also committed to fruitful collaborations, particularly with Prof. James
   Lam, Chair Professor of Department of Mechanical Engineering at the University
   of Hong Kong. Prof. Lam will provide crucial expertise in advanced control of
   UAVs, focusing on his strengths in robust control and algorithm implementation.
(g) Translational Impact – explain the demonstrable contributions, beneficial effects,
    valuable changes or advantages that research qualitatively brings to the economy,
    society, culture, public policy or services, health, the environment or quality of life;
    and that are beyond the academia.
1) Suitability and quality of the measures to disseminate and exploit the project
   outcomes
  The CCHUS project will promote the stable and efficient operation of microgrids from
hybrid modeling, stability analysis and control design aspects. Research outcomes of each
aspect will be disseminated in high-impact journals (J) and conferences (C) (see Table 1).
                                               Expected title                                                 Target J/C
 Matrix pencil and sum-of-square frameworks for stability and safety analysis of heterogeneous systems (J1)   IEEE TAC
    Stability criterion and stability enhancement for interconnected autonomous unmanned systems (J2)         IEEE TRO
     Decentralized and distributed synthesis of cooperative control for interconnected multi-UGVs (J3)        Automatica
       Prescribed-time formation and containment control for heterogeneous networked systems (J4)             IEEE TAC
       Design and experiment of prescribed-time controllers for interconnected UGVs and UAVs (J5)              IEEE TIE
  Exploring the modeling of interconnected UGVs and its potential application in complex scenarios (C1)         IECON
              High-low hierarchical control framework control for heterogeneous systems (C2)                      ECC
      Reinforcement learning to adaptive cooperative control of heterogeneous nonlinear systems (C3)             IROS
   Two primary stakeholders will be regarded as the dissemination targets of the
CCHUS project. (i) Stakeholder in academic institutions: Project outcomes will be
disseminated to researchers in academia among different disciplines, including robotics,
networked systems, control theory and applied mathematics, by publishing high-impact
journal papers. The subjects of five expected journal papers tightly correspond to the
three specific objectives of the CCHUS project. J1 and J2 corresponding to O1 will be
disseminated to researchers of applied mathematics and theoretical researchers of
robotics. J3 and J4 corresponding to O2 will be disseminated to researchers of control
theory and networked systems. J5 corresponding to O3 will be disseminated to
researchers of robotics. These scientific findings are intended to help the researchers
gain a better understanding of cooperative control of future robotic systems. (ii)
Stakeholder in industrial communities: I will take part in flagship international
conferences to disseminate the research results and conclusions to scientists and
technicians in the industrial communities. C1 corresponding to O1 will be disseminated
to experts in the IEEE Industrial Electronics Society. C2 corresponding to O2 will be
disseminated to experts in the IEEE Control Systems Society. C3 corresponding to O3
will be disseminated to experts in the IEEE Robotics and Automation Society. By
displaying posters and providing oral presentations and experimental tutorials, the
research findings will be widely communicated, promoting their further exploitation
and application in real industries.
2) Credibility of the measures to enhance the career perspectives and employability of
     the researcher and contribution to his/her skills development
   The successful application of JC Fellowship will pave the way for my more
prosperous academic career and will indicate a stepping-stone and turning point of my
life. The fellowship will provide me with an unparalleled chance to strengthen
professional skills, enhance career prospects, and promote employability.
   Contribution to skill development: Conducting the interdisciplinary CCHUS project
will not only increase my professional knowledge, but also enhance my skills to
collaborate across disciplines. By realizing three objectives, I will fully understand the
operating mechanisms of heterogeneous autonomous unmanned systems. Discussing
with the host supervisor, as well as his group members, will widen my scope of
specialized knowledge on cooperative control. Through working on the CCHUS
project, I will have a thorough understanding of the nature of cooperative control for
reconfigurable robotic systems, laying a solid foundation for my future research.
   Impact on career perspectives: The professional skills developed as a result of the
CCHUS project will in turn boost my career advancement, cultivate my creativity and
encourage my inventive potential. The completion of this multidisciplinary research
will greatly enhance my grasp of autonomous unmanned systems background and
cooperative control approaches, as well as my professional talent and expertise.
Training activities and scientific courses will assist me in better demonstrating research
results in high-impact publications, and in strengthening my skills for future funding
applications and implementation. Furthermore, close collaborations with my host
supervisor and other team members will broaden my academic social networks, which
will be greatly beneficial to my academic career development. Besides, working on the
CCHUS project will reach or re-enforce my professional maturity, and improve future
career prospects, such as independently conducting research, project management and
mentoring students. I recognize the significant potential of the CCHUS project to
contribute to the HK’s Smart City initiatives by advancing autonomous unmanned
systems and intelligent control systems.
   Impact on employability: The successful application of JC Fellowship will ensure
me to carry out the CCHUS project and realize the research objectives. The effective
implementation of the CCHUS project will greatly enhance my individual competence
and help me become an expert in the field of robotic control. Both the obtained scientific
results and training activities will make my CV outstanding. My long-term goal is to
secure a tenure-track assistant professor position in the HK. This renowned JC
Fellowship will make a substantial contribution in this direction.
3) The magnitude and importance of the project’s contribution to the expected
    scientific, societal and economic impacts
   Scientific Impacts: The CCHUS project drives theoretical breakthroughs in robotics
and autonomous control, particularly in multi-agent coordination, distributed
optimization algorithms, and cooperative tasks for smart city applications. This
research fosters interdisciplinary integration, combining artificial intelligence (e.g.,
reinforcement learning, swarm intelligence), urban mobility dynamics, and
communication networks to advance solutions for intelligent transportation,
infrastructure inspection, and emergency response. Additionally, it may contribute to
open-source algorithmic frameworks, lowering barriers to entry for global smart city
research teams and accelerating progress in autonomous urban systems.
   Economic Impacts: This research has the potential to optimize urban automation,
enhancing the efficiency of UGV and UAV fleets in logistics, traffic management, and
infrastructure maintenance, thereby reducing operational costs. The project strengthens
Europe’s competitiveness in smart city robotics and may spur spin-offs or patented
technologies for commercial applications, such as drone-based delivery systems or
autonomous surveillance platforms. In the long term, these innovations could expand
Europe’s share in the global smart mobility market while creating high-skilled jobs in
autonomous systems engineering and AI-driven urban planning.
   Social Impacts: Heterogeneous UGV and UAV cooperative control can improve
urban resilience by enabling adaptive disaster response, precision emergency services,
and real-time environmental monitoring. Applications include search-and-rescue
operations, pollution tracking, and smart surveillance for public safety. In addition, the
JC Fellowship’s international collaboration framework promotes global talent mobility,
fostering science diplomacy, and accelerating knowledge sharing for sustainable urban
development.
(h) Reference
[1] G. Yang, J. Bellingham, P. Dupont, et al. “The grand challenges of science
     robotics,” Science Robotics, vol. 3, no. 14: eaar7650, 2018.
[2] M. Dorigo, G. Theraulaz, and V. Trianni. “Reflections on the future of swarm
     robotics,” Science Robotics, vol. 5, no. 49, pp. eabe4385, 2020.
[3] G. Liang, D. Wu, Y. Tu, and T. Lam. “Decoding modular reconfigurable robots:
     A survey on mechanisms and design,” International Journal of Robotics Research,
     vol. 44, no. 5, pp. 740-767, 2025.
[4] S. Lyu, X. Lang, H. Zhao, H. Zhang, P. Ding, and D. Wang. “RL2AC:
     reinforcement learning-based rapid online adaptive control for legged robot robust
     locomotion,” Proceedings of the Robotics: Science and Systems, 2024.
[5] A. Ames, X. Xu, J. Grizzle, and P. Tabuada. “Control barrier function based
     quadratic programs for safety critical systems,” IEEE Transactions on Automatic
     Control, vol. 62, no. 8, pp. 3861-3876, 2016.
[6] H. Wang, A. Papachristodoulou, and K. Margellos. “Distributed safe control
     design and probabilistic safety verification for multi-agent systems,” Automatica,
     vol. 179, Art. 112393, 2025.
[7] A. Abel, D. Steeves, M. Krstić, and M. Janković. “Prescribed-time safety design
     for strict-feedback nonlinear systems,” IEEE Transactions on Automatic Control,
     vol. 69, no. 3, pp. 1464-1479, 2023.
[8] M. Bahreinian, M. Kermanshah and R. Tron, “Designing robust linear output
     feedback controller based on CLF–CBF framework via linear-programming (LP–
     CLF–CBF),” IEEE Transactions on Automatic Control, vol. 70, no. 4, pp. 2683-
     2689, 2025.
[9] K. Li, X. Fang, K. Zhao, and Y. Song. “Angle-constrained adaptive formation
     control with prescribed performance for multiple nonholonomic mobile robots,”
     IEEE Transactions on Industrial Electronics, vol. 72, no. 6, pp. 6307-6316, 2025.
[10] Y. Hua, X. Dong, G. Hu, Q. Li, and Z. Ren. “Distributed time-varying output
     formation tracking for heterogeneous linear multiagent systems with a
     nonautonomous leader of unknown input,” IEEE Transactions on Automatic
     Control, vol. 64, no. 10, pp. 4292-4299, 2019.
[11] S. Bhat and D. Bernstein. “Continuous finite-time stabilization of the translational
     and rotational double integrators,” IEEE Transactions on Automatic Control, vol.
     43, no. 5, pp. 678-682, 1998.
[12] A. Polyakov. “Nonlinear feedback design for fixed-time stabilization of linear
     control systems,” IEEE Transactions on Automatic Control, vol. 57, no. 8, pp.
     2106-2110, 2012.
[13] Y. Song, H. Ye, and F. L. Lewis. “Prescribed-time control and its latest
     developments,” IEEE Transactions on Systems, Man, and Cybernetics: Systems,
     vol. 53, no. 7, pp. 4102-4116, 2023.
[14] F. Sanfilippo, L. Hatledal, H. Zhang, M. Fago, and K. Pettersen. “Controlling Kuka
     industrial robots: Flexible communication interface JOpenShowVar,” IEEE
     Robotics and Automation Magazine, vol. 22, no. 4, pp. 96-109, 2015.
[15] M. Grotjahn and B. Heimann. “Model-based feedforward control in industrial
     robotics,” The International Journal of Robotics Research vol. 21, no. 1, pp. 45-60,
     2002.
[16] H. Wang, W. Ren, C. C. Cheah, Y. Xie, and S. Lyu. “Dynamic modularity
     approach to adaptive control of robotic systems with closed architecture,” IEEE
     Transactions on Automatic Control, vol. 65, no. 6, pp. 2760-2767, 2019.
[17] C. Song, J. Liu, P. Wang, C. Xu, S. Cai, and J. Chu. “Accelerated optimization in
     deep learning with a proportional-integral-derivative controller,” Nature
     Communications, vol. 15, no. 1, Art. 10263, 2024.
[18] W. Xiao, C. Belta, and C. Cassandras, “Event-triggered control for safety-critical
     systems with unknown dynamics,” IEEE Transactions on Automatic Control, vol.
     68, no. 7, pp. 4143-4158, 2023.
[19] H. Ye, Y. Song, Z. Zhang, and C. Wen. “Global dynamic event-triggered control
     for nonlinear systems with sensor and actuator faults: A matrix-pencil-based
     approach,” IEEE Transactions on Automatic Control, vol. 69, no. 3, pp. 2007-2014,
     2024.
[20] A. Papachristodoulou and S. Prajna, “On the construction of Lyapunov functions
     using the sum of squares decomposition,” Proceedings of the 41st IEEE
     Conference on Decision and Control, 2002., Las Vegas, NV, USA, vol. 3, pp.
     3482-3487, 2002.
[21] H. Ye, C. Wen, J. Lam, and P. Ioannou. “Decentralized prescribed-time input-to-
     state stabilization for interconnected normal form nonlinear systems,” Automatica,
     vol. 180, Art. 112480, 2025.
[22] X. Meng, J. Mei, Z. Miao, A. Wu, and G. Ma. “Fully distributed consensus of
     multiple Euler–Lagrange systems under switching directed graphs using only
     position measurements,” IEEE Transactions on Automatic Control, vol. 69, no. 3,
     pp. 1781-1788, 2024.
[23] Y. Orlov. “Time space deformation approach to prescribed-time stabilization:
     Synergy of time-varying and non-Lipschitz feedback designs,” Automatica, vol.
     144, Art. 110485, 2022.
[24] J. Huang. “Nonlinear output regulation: theory and applications,” Society for
     Industrial and Applied Mathematics, 2004.
[25] A. Bertino, P. Naseradinmousavi, and M. Krstić. “Prescribed-time safety filter for
     a 7-DOF robot manipulator: experiment and design,” IEEE Transactions on
     Control Systems Technology, vol. 31, no. 4, pp. 1762-1773, 2023.
[26] E. Restrepo, A. Loría, I. Sarras, and J. Marzat. “Robust consensus of high-order
     systems under output constraints: Application to rendezvous of underactuated
     UAVs,” IEEE Transactions on Automatic Control, vol. 68, no. 1, pp. 329-342,
     2023.
[27] B. Wang and C. Shen. “A time-varying observer-based approach to equilibrium
     estimation and compensation for synchronization of heterogeneous nonlinear
     cyber-physical systems,” IEEE Transactions on Automatic Control, vol. 68, no. 10,
     pp. 6176-6183, 2023.
