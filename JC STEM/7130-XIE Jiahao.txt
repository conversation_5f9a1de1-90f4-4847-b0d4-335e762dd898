                                                     <PERSON><PERSON><PERSON>
                                             Postdoc, Max Planck Institute for Informatics, Germany
                          <EMAIL>   |  jiahao000.github.io   |  Jiahao000   |  @JiahaoXie3   |  Ji<PERSON><PERSON>e



Research Interests
My research interests include computer vision and machine learning, with a focus on foundation models and their real‑world applications. This
involves self‑supervised learning, representation learning, multimodal learning, generative models, and other topics related to foundation mod‑
els. The goal of my research is to develop intelligent systems that can understand and interact with the real world in ways that mirror or even
surpass human sensation, perception, and reaction.



Education
Nanyang Technological University                                                                                                            Singapore
PH.D. iN COMPUTER SCiENCE AND ENGiNEERiNG                                                                                           Jul. 2019 ‑ Nov. 2023
• Affiliated with S‑Lab and MMLab@NTU.
• Advisors: Prof. <PERSON> and Prof. <PERSON><PERSON>
• Awarded Chinese Government Award for Outstanding Self‑financed Students Abroad, the highest doctoral student award granted by the Chi‑
  nese government to Chinese students overseas.
• Dedicated to open source. I am one of the core developers and maintainers of OpenMMLab, the most popular open‑source algorithm platform
  for computer vision.
Northwestern Polytechnical University                                                                                                      Xi’an, China
B.ENG. iN AUTOMATiON                                                                                                               Sep. 2014 – Jun. 2018
• GPA: 92.46/100, Ranking: 1/147
• Awarded National Scholarship twice, the highest undergraduate student award in China.



Experience
Max Planck Institute for Informatics                                                                                            Saarbrücken, Germany
POSTDOCTORAL RESEARCHER                                                                                                              Dec. 2023 ‑ Present
• Advisor: Prof. Bernt Schiele, Member of European Academy of Engineering, ACM/IEEE/IAPR Fellow
• Collaboration with Google under the VIA Center, working on vision and language models.
Shanghai AI Laboratory                                                                                                               Shanghai, China
RESEARCH INTERN AT THE OPENMMLAB TEAM                                                                                               Jul. 2021 ‑ Jun. 2023
• Mentor: Dr. Kai Chen, Research Scientist & Head of Large Model Center, Shanghai AI Laboratory
• Worked on OpenMMLab self‑supervised learning toolbox and benchmark development.
• Worked on OpenMMLab pre‑training toolbox and benchmark development.
SenseTime Group Ltd.                                                                                                                 Shenzhen, China
RESEARCH INTERN AT THE OPENMMLAB TEAM                                                                                               Apr. 2021 ‑ Jul. 2021
• Mentor: Dr. Kai Chen, Director, SenseTime
• Worked on OpenMMLab self‑supervised learning toolbox and benchmark development.



Open Source Project
MMSelfSup: OpenMMLab Self‑Supervised Learning Toolbox and Benchmark [GitHub]                                                        Github star 3.3k+
CORE DEVELOPER & MAiNTAiNER                                                                                                          Jun. 2020 ‑ Present
• MMSelfSup unifies the pipeline and modular design of multiple self‑supervised visual representation learning frameworks.
• It supports various self‑supervised tasks including classification, joint clustering and feature learning, contrastive learning, masked image mod‑
  eling, etc., with high efficiency and state‑of‑the‑art results.
• It standardizes the evaluation benchmarks and supports smooth downstream task evaluations on other OpenMMLab projects like MMClassifi‑
  cation, MMDetection, and MMSegmentation.
• It is one of the most popular research platforms for self‑supervised learning.
MMPreTrain: OpenMMLab Pre‑Training Toolbox and Benchmark [GitHub]                                                                  Github star 3.7k+
CORE DEVELOPER & MAiNTAiNER                                                                                                         Apr. 2023 ‑ Present
 • MMPreTrain unifies the pipeline and modular design of MMClassification and MMSelfSup, integrating them into one codebase.
 • It supports rich training strategies including supervised learning, self‑supervised learning, multi‑modal learning, etc., with high efficiency and
   state‑of‑the‑art results.
 • It supports various out‑of‑box inference tasks including image classification, image captioning, visual question answering, visual grounding,
   and retrieval (image‑to‑image, text‑to‑image, image‑to‑text).
 • It is one of the most popular research platforms for foundation models.


Publications
First‑Author Papers:

Chain‑of‑Region Verification Reduces Hallucinations in Large Vision‑Language Models
JiAHAO XiE, ALESSiO TONiONi, NATHALiE RAUSCHMAYR, FEDERiCO TOMBARi, BERNT SCHiELE
Under Review at Conference on Neural Information Processing Systems (NeurIPS), 2025
A region‑aware chain‑of‑verification method to reduce hallucinations in large vision‑language models

Test‑Time Visual In‑Context Tuning [PDF]
JiAHAO XiE, ALESSiO TONiONi, NATHALiE RAUSCHMAYR, FEDERiCO TOMBARi, BERNT SCHiELE
IEEE Conference on Computer Vision and Pattern Recognition (CVPR), 2025
First test‑time training work to adapt vision generalist models on the fly with a single test sample

MosaicFusion: Diffusion Models as Data Augmenters for Large Vocabulary Instance
Segmentation [PDF]
JiAHAO XiE, WEi Li, XiANGTAi Li, ZiWEi LiU, YEW SOON ONG, CHEN CHANGE LOY
International Journal of Computer Vision (IJCV), 2024
First diffusion‑based multi‑object data augmentation approach for large vocabulary instance segmentation

Masked Frequency Modeling for Self‑Supervised Visual Pre‑Training [PDF]
JiAHAO XiE, WEi Li, XiAOHANG ZHAN, ZiWEi LiU, YEW SOON ONG, CHEN CHANGE LOY
International Conference on Learning Representations (ICLR), 2023
First unified, flexible and robust self‑supervised visual pre‑training paradigm in the frequency domain

Delving into Inter‑Image Invariance for Unsupervised Visual Representations [PDF]
JiAHAO XiE, XiAOHANG ZHAN, ZiWEi LiU, YEW SOON ONG, CHEN CHANGE LOY
International Journal of Computer Vision (IJCV), 2022
A comprehensive empirical study on unsupervised inter‑image invariance learning

Unsupervised Object‑Level Representation Learning from Scene Images [PDF]
JiAHAO XiE, XiAOHANG ZHAN, ZiWEi LiU, YEW SOON ONG, CHEN CHANGE LOY
Conference on Neural Information Processing Systems (NeurIPS), 2021
First object‑level self‑supervised representation learning framework towards scene images

Online Deep Clustering for Unsupervised Representation Learning [PDF]
XiAOHANG ZHAN∗ , JiAHAO XiE∗ , ZiWEi LiU, YEW SOON ONG, CHEN CHANGE LOY (∗ CO‑FiRST AUTHORS)
IEEE Conference on Computer Vision and Pattern Recognition (CVPR), 2020
Key component for the 1st place method (all four tracks) in Facebook AI Self‑Supervision Challenge 2019

Co‑Author Papers:

Improved Vision‑Language Alignment via Text‑Conditioned Image Embeddings
SWETA MAHAJAN∗ , SUKRUT RAO∗ , JiAHAO XiE, ALEXANDER KOLLER, BERNT SCHiELE (∗ CO‑FiRST AUTHORS)
Under Review at Conference on Neural Information Processing Systems (NeurIPS), 2025
Editable image embeddings conditioned on text for better aligned vision‑language models

CNS‑Bench: Benchmarking Model Robustness Under Continuous Nuisance Shifts [PDF]
OLAF DÜNKEL, ARTUR JESSLEN∗ , JiAHAO XiE∗ , CHRiSTiAN THEOBALT, CHRiSTiAN RUPPRECHT, ADAM KORTYLEWSKi
(∗ CO‑SECOND AUTHORS)
IEEE International Conference on Computer Vision (ICCV), 2025
A generative benchmark to evaluate the robustness of foundation models under various continuous nuisance shifts
Correlational Image Modeling for Self‑Supervised Visual Pre‑Training [PDF]
WEi Li, JiAHAO XiE, CHEN CHANGE LOY
IEEE Conference on Computer Vision and Pattern Recognition (CVPR), 2023
A novel crop‑and‑correlate self‑supervised paradigm beyond contrastive learning and masked image modeling

UniVIP: A Unified Framework for Self‑Supervised Visual Pre‑training [PDF]
ZHAOWEN Li, YOUSONG ZHU, FAN YANG, WEi Li, CHAOYANG ZHAO, YiNGYiNG CHEN, ZHiYANG CHEN, JiAHAO XiE, LiWEi WU,
RUi ZHAO, MiNG TANG, JiNQiAO WANG
IEEE Conference on Computer Vision and Pattern Recognition (CVPR), 2022
A unified self‑supervised framework to learn versatile representations on both single‑centric‑object and non‑iconic images


Academic Competition
Facebook AI Self‑Supervision Challenge                                                                         ICCV Workshop on Extreme Vision
CHAMPiONS OF ALL FOUR TRACKS                                                                                                          Oct. 2019
• Team members: Xiaohang Zhan , Jiahao Xie , Ziwei Liu, Yew Soon Ong, Chen Change Loy ( equal contribution)
                                      ∗         ∗                                              ∗




Honors & Awards
   2025     Notable Reviewer, ICLR
   2023     Outstanding Reviewer, CVPR
   2022     Chinese Government Award for Outstanding Self‑financed Students Abroad, China Scholarship Council
   2021     Outstanding Reviewer, CVPR
   2019     Champions of all four tracks, Facebook AI Self‑Supervision Challenge
   2019     Research Scholarship, Nanyang Technological University
   2018     Outstanding Bachelor Thesis Award, Northwestern Polytechnical University
   2017     Honorable Mention Award, Mathematical Contest in Modeling (MCM)
   2016     National Scholarship (top 1%), Ministry of Education of China
   2015     National Scholarship (top 1%), Ministry of Education of China


Academic Services
Conference Reviewer
• IEEE Conference on Computer Vision and Pattern Recognition (CVPR)
• IEEE International Conference on Computer Vision (ICCV)
• European Conference on Computer Vision (ECCV)
• Conference on Neural Information Processing Systems (NeurIPS)
• International Conference on Learning Representations (ICLR)
• International Conference on Machine Learning (ICML)
• AAAI Conference on Artificial Intelligence (AAAI)
Journal Reviewer
• IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)
• International Journal of Computer Vision (IJCV)



Teaching
Nanyang Technological University
TEACHiNG ASSiSTANT
• CE/CZ3004: Multidisciplinary Design Project, Fall 2021
• AI6126: Advanced Computer Vision, Fall 2020
• CE7491: Special Advanced Topic: Digital Image Processing, Fall 2020
• RE1016: Engineering Computation, Spring 2020, 2021, 2022
                                  Goals and Objectives

The overarching goal of this project is to develop multimodal multitask self-supervised
foundation models that can learn from heterogeneous data modalities, adapt flexibly to diverse
tasks, and be validated through real-world applications. Unlike existing models, which are
typically limited to single modalities or isolated tasks, the proposed research aims to create a
unified, efficient, and versatile foundation model framework that better reflects human
cognition — inherently multimodal and multitask — while also addressing pressing challenges
in scalability, robustness, and data scarcity.

This project responds directly to the global momentum toward Artificial General Intelligence
(AGI) and the Hong Kong Innovation and Technology (I&T) development strategy, which
emphasizes building Artificial Intelligence (AI) that would match or surpass human
cognitive abilities. By leveraging recent advances in self-supervised learning, generative
modeling, and multimodal representation learning, this research will advance both fundamental
AI science and its translational pathways.

The project is structured around three interconnected objectives:

1. Multimodal Data Synthesis and Automatic Annotation
   • Develop generative data construction pipelines to synthesize multimodal data (e.g.,
     image, video, audio, text, point cloud).
   • Create automatic annotation and quality filtering mechanisms to ensure scalability and
     reliability, thereby mitigating the growing problem of data scarcity in AI.
2. Multimodal Unified Pre-Training and Modality Alignment
   • Construct a shared feature space by designing multimodal pre-training tasks that can
     unify both paired and unpaired data.
   • Enable robust cross-modal alignment across semantic granularities, allowing the model
     to integrate information flexibly across modalities.
3. Multimodal Downstream Adaptation and Inference Scaling
   • Investigate efficient adaptation strategies such as multimodal prompting and
     lightweight fine-tuning for zero-shot and few-shot learning.
   • Extend model reasoning capabilities through multimodal chain-of-thought reasoning
     and test-time scaling, improving robustness and generalization in complex real-world
     environments.

Together, these objectives will deliver an end-to-end research pipeline (see Figure 1 in Images
and Diagrams), progressing from fundamental research (TRL 1–3) to prototype demonstration
with industry collaborators (TRL 4–6). The ultimate aim is to build an intelligent system
capable of perceiving, understanding, reasoning, and making decisions in a self-
supervised and self-evolving manner. The outcomes will not only advance state-of-the-art
AI methods but also provide concrete societal benefits through open-source dissemination and
industry validation.
                                 Timeliness and Novelty

Artificial intelligence (AI) is entering a critical juncture. Foundation models such as GPT have
demonstrated unprecedented generalization across modalities and tasks. However, current
approaches face fundamental limitations: they remain heavily data- and resource-intensive, are
often constrained to single-modality or single-task paradigms, and lack the robustness required
for real-world applications. These challenges make this project both timely and necessary.

1. Timeliness
   • Data Scarcity: The growth of high-quality annotated datasets has plateaued, while
      demand for multimodal learning continues to rise. Self-supervised learning and
      generative approaches now provide feasible pathways to synthetic data creation and
      automatic annotation, reducing reliance on costly manual labeling.
   • Technological Maturity: Advances in self-supervised learning, diffusion models, and
      multimodal representation learning have reached a point where unified multimodal
      multitask integration is technically achievable but remains underdeveloped.
   • Translational Opportunity: With strong government investment in healthcare,
      robotics, and autonomous driving, Hong Kong offers fertile ground for translating
      foundation models into high-impact applications. This aligns directly with the Hong
      Kong Innovation and Technology (I&T) development strategy.
   • Open-Source Ecosystem: The global AI community increasingly depends on open and
      transparent toolkits. My role as a core developer and maintainer of OpenMMLab
      projects provides a unique channel for rapid and large-scale dissemination of outcomes.

2. Novelty
   • Generative Multimodal Data Construction: This project moves beyond traditional
      manual dataset collection by using controllable generative models for multimodal data
      synthesis and automatic annotation. In addition, it introduces scalable filtering pipelines
      that ensure both data diversity and quality.
   • Unified Multimodal Self-Supervised Tasks: Unlike existing models that rely
      primarily on paired multimodal data, this project incorporates both paired and unpaired
      data and proposes novel pretext tasks that unify multiple modalities into a shared feature
      space, enabling more fine-grained alignment and broader generalization.
   • Reasoning-Enhanced Multimodal Adaptation: This project integrates multimodal
      prompting and chain-of-thought reasoning into downstream applications, scaling
      models beyond static feature extraction and improving zero-shot and few-shot
      capabilities in complex, dynamic, and unknown environments.
   • End-to-End Translational Pathway: Distinct from foundation model projects that
      stop at benchmark performance, this project explicitly targets TRL 1–6 progression:
      from fundamental principles to prototype demonstration with industry collaborators.

In summary, this project is timely as it responds to the urgent need for scalable and robust
multimodal multitask foundation models, while taking advantage of a rare convergence of
technological maturity and translational opportunities in Hong Kong. Its novelty lies in
introducing new paradigms in data collection, task design, downstream application, and
translational validation. This positions the research to make breakthroughs that are not only
academically significant but also societally transformative, reinforcing Hong Kong’s role as a
leader in global AI innovation.
                                       Methodology

This project addresses the core challenges of foundation models, with the goal of achieving a
universal multimodal multitask foundational model beyond human supervision. Based on the
scientific question of “building a foundation model that fuses multiple modalities and
synergizes multiple tasks in a self-supervised and self-evolving manner to enhance multimodal
perception, understanding, reasoning, and decision-making capabilities”, the project will focus
on three aspects: 1) multimodal data synthesis and automatic annotation, 2) multimodal unified
pre-training and modality alignment, and 3) multimodal downstream adaptation and inference
scaling. Figure 1 (see Images and Diagrams) shows the overview and relationship among
three research topics. Together, they form a pipeline from fundamental research (TRL 1–3) to
prototype demonstration (TRL 4–6).

It is worth mentioning that the proposed research work is an extension of my previous research
work: my academic achievements and project experience w.r.t. self-supervised foundational
models in the visual modality can be extended to more modalities and tasks. The self-
supervised data synthesis, task design, and downstream application methods developed in my
previous research lay a solid foundation for multimodal multitask self-supervised data
synthesis, task design, and downstream applications.

1. Multimodal Data Synthesis and Automatic Annotation

1.1 Objective

This research topic aims to overcome data scarcity and annotation cost by generating large-
scale, high-quality multimodal datasets. Research questions include: (1) How to generate
multimodal data of different granularities? (2) How to achieve automatic data labeling? (3)
How to evaluate and filter low-quality data?

1.2 Approach

Recent advances in generative models, especially diffusion models, enable realistic data
generation across modalities (e.g., image, video, audio, 3D point cloud, text). Compared with
manually collected data, using model-synthesized data can achieve large-scale data expansion,
thus effectively alleviating the problem of data exhaustion. In addition, another major
advantage of using model-synthesized data is its controllability. In other words, the generated
data can be controlled by prompts, latent variables, or hyperparameters. The generation
conditions themselves serve as natural supervision signals, and their granularity can be
customized based on the needs of users. Therefore, this research aims to build a generic self-
supervised data synthesis framework based on generative models. The framework integrates
and automates multimodal and multi-granularity data generation, annotation, evaluation, and
filtering processes, narrowing the domain gap between synthetic and real data.

Specifically, as shown in Figure 2 (see Images and Diagrams), given a small number of real
data samples, this project first uses text prompts to instruct a large language model (LLM) to
generate similar data descriptions. For example, for complex scene data, the generated data
description could be a scene graph containing object categories, attributes, relationships, and
layouts. This project then leverages the generated data descriptions, combined with control
signals of different modalities and granularities, to guide data generation by adding various
generation conditions to the diffusion model, ensuring that its output conforms to the given
conditions. Furthermore, this project uses the LLM to call on expert models of different
modalities and tasks to annotate, evaluate and score generated data. By filtering out low-quality
data with poor scores, this project is ultimately able to construct large-scale, high-quality data.

1.3 Deliverables

   •   A scalable multimodal dataset construction toolkit.
   •   2–3 top-tier publications on synthetic multimodal data generation.

2. Multimodal Unified Pre-Training and Modality Alignment

2.1 Objective

This research topic aims to design unified self-supervised pretext tasks for multimodal pre-
training and construct a shared multimodal feature space to achieve multi-granularity semantic
alignment. Research questions include: (1) How to design a unified multimodal self-supervised
pretext task? (2) How to build a unified multimodal feature space? (3) How to achieve multi-
granular semantic alignment of modalities?

2.2 Approach

Multimodal data often appear in pairs. For example, videos in movies are usually accompanied
by audios, and pictures in social media are usually accompanied by text descriptions. Self-
supervision signals naturally exist in paired multimodal data. Therefore, how to utilize paired
self-supervision signals in multimodal data is the key to designing multimodal self-supervised
pretext tasks. On the other hand, there are also a lot of unpaired cases in multimodal data. How
to leverage multimodal self-supervised pretext tasks for pairing and alignment between
modalities is also one of the major challenges. Therefore, this research aims to construct a
unified multimodal feature space through multimodal self-supervised learning, achieve
multimodal feature fusion based on paired and unpaired data, and explore the correlation of
multimodal knowledge in feature space.

Specifically, as shown in Figure 3 (see Images and Diagrams), based on the multimodal data
generated in Section 1, self-supervised multimodal fusion can be achieved in two ways: 1)
using an end-to-end modality fusion architecture for multimodal pre-training, and 2) merging
independently pre-trained unimodal models via connectors. Regarding 1), this project first
converts data from different modalities into a unified input format (e.g., discrete sequences)
and then pre-trains them using one or more unified self-supervised pretext tasks (e.g.,
autoregressive modeling, masked modality modeling, or a combination thereof). Regarding 2),
this project first defines an anchor modality (e.g., text or image modality) and connects multiple
independently pre-trained unimodal models through a simple mapping layer (e.g., MLP). This
mapping layer is then trained using one or more self-supervised pretext tasks (e.g.,
autoregressive modeling, masked modality modeling, contrastive learning, or a combination
thereof) to align unimodal features with the anchor modality in a unified feature space. This
operation indirectly aligns each modality with other modalities without repeating the pairwise
alignment process between modalities, thus significantly reducing the complexity of model
training and the amount of data required.

2.3 Deliverables
   •   A unified multimodal backbone pretrained on both synthetic and real data.
   •   Benchmarks demonstrating superior generalizability compared to prior works.
   •   2–3 top-tier publications on multimodal pre-training and modality alignment.

3. Multimodal Downstream Adaptation and Inference Scaling

3.1 Objective

This research topic aims to develop adaptation mechanisms and reasoning strategies that allow
models to transfer effectively to new tasks and domains. Research questions include: (1) How
to adapt foundation models efficiently to downstream tasks? (2) How to construct multimodal
prompts for zero-/few-shot transfer? (3) How to extend reasoning capabilities during inference?

3.2 Approach

Through the research in the first two parts, this project has built a unified multimodal model
and obtained a unified multimodal feature space. The ultimate goal of building a multimodal
foundation model is to apply and deploy it in a wide range of downstream tasks. Therefore,
this research aims to improve the generalizability, reliability, and robustness of foundation
models in complex and diverse downstream tasks and distributions.

As shown in Figure 4 (see Images and Diagrams), this project intends to construct unified
multimodal prompts to achieve fast downstream adaptation in a zero-shot or few-shot manner.
Specifically, on the one hand, this project intends to construct prompts with the format
corresponding to the pre-training data format to achieve zero-shot adaptation. On the other
hand, this project intends to fine-tune learnable prompts using a small amount of real or
synthetic data from downstream tasks to achieve few-shot adaptation. To further improve the
robustness and generalization ability, this project intends to perform test-time multimodal
prompt tuning to scale up the model at test time. Specifically, since the annotations of test data
are unknown, this project intends to design multimodal self-supervision signals to dynamically
update the learnable prompts so that they can adapt to unknown environments on the fly. In
addition, this project intends to leverage the emergent ability of foundation models to construct
long multimodal thinking chains, allowing models to self-verify, self-reflect, and self-evolve.

3.3 Deliverables

   •   An efficient multimodal downstream application toolkit.
   •   2–3 top-tier publications on multimodal downstream applications.
                                                                                   Images and Diagrams
    Scientific                   How to build a foundation model that fuses multiple modalities and synergizes multiple tasks in a self-supervised and
    Question                     self-evolving manner to enhance multimodal perception, understanding, reasoning, and decision-making capabilities?




    Research                          Multimodal Data Synthesis                                                        Multimodal Unified Pre-Training                                                                Multimodal Downstream Adapation
    Content                           and Automatic Annotation                                                            and Modality Alignment                                                                            and Inference Scaling



                                 Multimodal                                 Efficient                                    Efficient                               Unified                                               Multimodal                                Multimodal
                                Data Synthesis                             Multimodal                                  Data Mapping                            Multimodal                                             Prompt Tuning                               Chain-of-
       Key
                               with Controllable                          Data Filtering                                in a Unified                         Multi-Granularity                                         Tailored for                            Thought Design
    Techniques                    Generative                              and Selection                                 Multimodal                           Self-Supervised                                           Downstream                              with Inference-
                                    Models                                  Strategies                                     Space                             Representations                                           Distributions                            Time Scaling

                                                                                      Upstream Data                                                                           Representations+Model
                                                                                                                                                                                 Adapting+Training
                                                                                                                              Downstream Data

    Research                                                "Multimodal Multitask Data Generation" → "Unified Multimodal Multitask Pre-Training"
     Goal                                                                   → "Multimodal Multitask Downstream Applications"

                                                              Figure 1: Overview of research plan
                                                                                                                                                         Control Signal


                                                                                                                                                                              Generate                                                      Filter
                                                                                                                                                   Text-to-Image                                                       Image
                                                                                                                                                  Diffusion Model                                                      Scorer



                                                                                                                                                         Control Signal


                                                                                                                                                                              Generate                                                      Filter
                                                                                                                                                   Text-to-Video                                                       Video
                                  Text Prompts                                                                                                    Diffusion Model                                                      Scorer

                                                                                                          Filter
                                                         Generate                         Text
                                         LLM
                                                                                         Scorer                                                          Control Signal


                                                                                                                                                                              Generate                                                      Filter
                                   Data Priors                                                                                                     Text-to-Audio                                                       Audio
                                                                                                                                                  Diffusion Model                                                      Scorer




                                                                                                                                                         Control Signal


                                                                                                                                                                              Generate                                                      Filter
                                                                                                                                                     Text-to-3D                                                          3D
                                                                                                                                                  Diffusion Model                                                      Scorer




  Figure 2: Multimodal data synthesis and automatic annotation pipeline
                                                                                                                 Trainable
                                                                                                                 Frozen

                                                                                                                                                                                                     Align                                                      Anchor Modality
                                                                                        ...


                               Detokenize
                                                                                                             Global               Local             Global             Local             Global                Local            Global             Local        Global             Local
                                                                                                              [CLS]           0    ...    L          [CLS]         0    ...    L             [CLS]         0    ...    L        [CLS]          0     ...   L     [CLS]         0    ...    L
                                                                    ...                       [EOS]
                                                                                                                   Connector                             Connector                               Connector                          Connector                        Connector

                                   Transformer                                                                          Image                             Video                                   Audio                                3D                              Text
       (Next-Token Prediction/Masked Modality Modeling)                                                                Encoder                           Encoder                                 Encoder                             Encoder                          Encoder

                                                                                                             0          1   ... L-1      L           0       1   ... L-1      L              0       1   ... L-1      L         0       1    ... L-1       L     0       1   ... L-1      L

     [BOS]                                                                   ...                                               Masking                                                                                                          Masking                         Masking
                                                                                                                                                                    Masking                                 Masking
                                                                                                             0          1   ... L-1      L           0       1   ... L-1      L              0       1   ... L-1      L         0       1    ... L-1       L     0       1   ... L-1      L
                                 Tokenize           Masking
                                                                                                                                  Tokenize                             Tokenize                                Tokenize                            Tokenize                        Tokenize

                                                                                        ...                                                                                                                                                                                                ...



       (a) End-to-End Multimodal Fusion Architecture                                                                                              (b) Connector-Based Multimodal Fusion Architecture

Figure 3: Multimodal unified pre-training and modality alignment pipeline
   Stage 1：Training-Time Training                             Stage 2: Test-Time Training                                                Stage 3: Inference                                                                         Stage 4: Reasoning


     Task Loss + Self-Supervised Loss                                     Self-Supervised Loss                                                     Prediction                                                                                                        [MM-CoTs] [Answer]




       Large Multimodal Model                                   Large Multimodal Model                                            Large Multimodal Model                                                                   Large Multimodal Model


                                                                                                                                                                                                                                                   ...
    Image     Video   Audio       3D       Text              Image         Video     Audio       3D      Text               Image          Video     Audio         3D          Text               [Image][Video][Audio][3D][Text]...[Instruction][Question]                  [MM-CoTs]
    Prompt   Prompt   Prompt    Prompt    Prompt             Prompt       Prompt     Prompt    Prompt   Prompt              Prompt        Prompt     Prompt      Prompt       Prompt

      +        +        +         +                ...          +           +          +          +              ...           +              +          +          +                  ...
                                           +




                                                                                                         +




                                                                                                                                                                                  +




                                                                                                                                                                                                                                                   ...

                Training Input                                                     Test Input                                                      Test Input                                                         Test Input




                                                           (a) Multimodal Prompt Tuning                                                                                                                               (b) Multimodal Chain-of-Thoughts

Figure 4: Multimodal downstream adaptation and inference scaling pipeline
                                                                                          Project Timeline
                                                       TRL 1-3                                                TRL 2-4                                              TRL 3-5                                             TRL 4-6

           Large-Scale Synthesized Datatset                        Large-Scale Pre-Trained Model                Efficient Downstream Application Toolkit                              Prototype Demonstration



     Multimodal Data Synthesis and Automatic Annotation




                                              Multimodal Unified Pre-Training and Modality Alignment




                                                                                                   Multimodal Downstream Adaptation and Inference Scaling




                                                                                                                                                           Prototype Demonstration with Industry Collaborator(s)




                                                                                               Open-Source Release and Dissemination



                 Annual Report 1                                       Annual Report 2                                          Annual Report 3                                     Annual Report 4



Apr 2026      Jul 2026     Oct 2026      Jan 2027      Apr 2027      Jul 2027       Oct 2027    Jan 2028      Apr 2028        Jul 2028   Oct 2028     Jan 2029     Apr 2029      Jul 2029      Oct 2029     Jan 2030   Apr 2030

                                                                                                           Time (Quarterly)

                                                              Workstream Activity                            Major Milestone                          Annual Report




The project is organized over four years, progressing from fundamental research (TRL 1–3) to
prototype demonstration (TRL 4-6). The Gantt chart above illustrates the schedule of activities
for the duration of the Fellowship, with major milestones and annual reports clearly plotted.
                      Potential Collaborator(s) in Hong Kong

1. Primary Industry Collaborator: Hong Kong Applied Science and Technology
   Research Institute (ASTRI)

ASTRI, Hong Kong’s flagship applied R&D institute, has a government mandate to transfer
technologies into industry across AI, robotics, mobility, and healthcare. With extensive
experience co-developing prototypes with universities and incubatees, ASTRI is ideally
positioned to guide this Fellowship from TRL-4 validation to TRL-6 demonstrations.

Specifically, ASTRI will co-design use cases in smart logistics robotics and urban mobility,
provide engineering support in MLOps and embedded systems, and conduct field pilots with
industrial and government partners to assess robustness, compliance, and user acceptance. It
will also contribute to commercialisation through joint technology briefs and engagement with
CUHK’s Technology Transfer Office (TTO) on licensing or FTA pathways. The industry
attachment, hosted at ASTRI and planned for 6–9 months (from late Year-3 to Year-4), will
involve weekly sprints, robustness audits, and pilot deployments. Expected outcomes include
deployable prototype(s), a field-trial report with ablations, and a translational case study or
demo day.

2. Parallel Industry Collaborator: Huawei (Hong Kong)

Huawei brings world-class AI infrastructure—Ascend NPUs, Atlas edge devices, and
ModelArts MLOps pipelines—that is critical for large-scale training and edge optimisation. It
is already active in collaborations with Hong Kong universities and industry, making it a
strategic partner for scaling and efficiency.

Specifically, Huawei will provide GPU/NPU clusters and reproducible pipelines for
multimodal self-supervised pre-training, co-develop deployment containers optimized for
Ascend/Atlas hardware, and facilitate access to enterprise testbeds in healthcare and robotics.
This collaboration will run in parallel with the ASTRI attachment, focusing specifically on
scalability and hardware optimization. Expected outcomes include reference deployment
configurations, comparative efficiency benchmarks, and joint publications or whitepapers on
hardware-aware AI.

3. Summary

With ASTRI serving as the primary translational host and Huawei as a parallel infrastructure
partner, the Fellowship combines ecosystem-driven prototyping with infrastructure-level
optimization. This dual collaboration maximises impact, ensuring prototypes that are not only
scientifically robust but also deployable, efficient, and directly relevant to Hong Kong’s
innovation and technology priorities.
                                   Translational Impact

This project aims to develop multimodal multitask self-supervised foundation models that
can adapt flexibly across tasks and be validated in real-world applications. While its scientific
contributions will advance the frontiers of AI, the Fellowship’s ultimate significance lies in the
demonstrable benefits it will bring to Hong Kong’s economy, society, culture, health,
environment, and innovation ecosystem. By translating research into deployable prototypes
through ASTRI and scaling them efficiently with Huawei (Hong Kong), the project ensures
that outcomes move beyond academic publications and into tangible impact.

1. Economic and Innovation Benefits

Lowering barriers for SMEs and start-ups. Current AI solutions are often out of reach for
small firms due to reliance on massive annotated datasets and costly retraining. By developing
synthetic data pipelines, parameter-efficient tuning, and unified toolkits, this project reduces
development costs dramatically. Small and medium-sized enterprises (SMEs) and start-ups in
Hong Kong Science Park (HKSTP) and Cyberport can access foundation-model capabilities
without the prohibitive expense of manual data collection, enabling them to innovate in
healthcare, logistics, fintech, and smart city applications.

Accelerating commercialization. Through collaboration with ASTRI, validated prototypes
(TRL-4 to TRL-6) will be co-developed with industrial partners and trialled in field pilots. This
will shorten the time from academic breakthrough to market-ready solutions. By working with
CUHK’s Technology Transfer Office (TTO), the project will prepare technology briefs,
licensing agreements, and potential spin-offs. This pathway contributes directly to Hong
Kong’s Innovation and Technology (I&T) Development Blueprint.

Strengthening Hong Kong’s competitiveness. Huawei’s infrastructure partnership ensures
that models are hardware-optimized for deployment on Ascend NPUs and Atlas devices. This
hardware–software synergy not only increases efficiency but also positions Hong Kong as a
leader in hardware-aware AI, boosting the region’s attractiveness for investment and R&D
partnerships. Together, these collaborations expand Hong Kong’s role as a hub for AI
translation in Asia.

2. Societal and Healthcare Benefits

Improved healthcare delivery. Foundation models fine-tuned on medical imaging tasks can
serve as triage assistants, highlighting anomalies for radiologists and reducing diagnostic
backlogs. Even small improvements in sensitivity and efficiency can directly benefit patient
outcomes, particularly in under-resourced settings. By employing synthetic data and self-
supervised pre-training, this project minimises reliance on sensitive clinical data while still
enabling robust models.

Enhanced robotics and mobility. By integrating multimodal inputs (e.g., image, video, audio,
text, 3D), the models developed will improve robustness in logistics robots and autonomous
systems. Applications include warehouse automation, delivery robots, and autonomous
vehicles. These improvements enhance worker safety, increase efficiency in supply chains, and
support smart city development. ASTRI’s field pilots will validate these prototypes in realistic
environments, ensuring that societal benefits are measurable and trusted.
Inclusive access. By embedding robustness and zero-/few-shot learning capabilities, the
project enables AI that adapts quickly to new contexts with minimal supervision. This is
particularly valuable for public institutions, NGOs, and SMEs that lack the resources for
extensive retraining, broadening access to advanced AI beyond elite institutions.

3. Cultural, Educational, and Policy Impact

Open-source leadership. All major outcomes—including data synthesis pipelines, pretrained
backbones, and adaptation toolkits—will be released via OpenMMLab, one of the most
widely used open-source platforms in AI. This ensures rapid global uptake and visibility,
positioning Hong Kong as not only a consumer but also a contributor of cutting-edge AI
research. Open releases also set new cultural norms around transparency, reproducibility, and
collaboration.

Talent development. The Fellowship will provide hands-on training for postgraduate students,
RAs, and junior engineers in Hong Kong, equipping them with expertise in self-supervised
learning, multimodal alignment, and efficient adaptation. By running tutorials, workshops, and
hackathons with partners such as ASTRI, Huawei, and HKSTP, the project will help cultivate
a new generation of AI talent ready to support Hong Kong’s innovation ecosystem.

Policy and governance. Through transparent documentation (e.g., model cards, data cards)
and red-team robustness audits, the project will contribute to Hong Kong’s efforts in building
trustworthy AI. The methodologies developed can inform regulatory discussions on safety,
fairness, and data governance, particularly in healthcare and mobility contexts where trust is
essential.

4. Environmental Sustainability

Efficiency-first design. The project directly addresses the growing carbon footprint of AI by
advancing parameter-efficient tuning, synthetic data augmentation, and test-time adaptation,
all of which reduce retraining costs and energy usage. Early results suggest such approaches
can lower compute requirements by over 50% compared with naïve fine-tuning, while retaining
performance.

Hardware-aware optimization. With Huawei’s Ascend/Atlas hardware, models will be
profiled for latency, throughput, and energy use. Deploying models on efficient edge devices
reduces dependency on large data centres and supports greener AI solutions, aligning with
Hong Kong’s broader sustainability agenda.

5. Summary

This Fellowship delivers far more than academic contributions. By uniting ASTRI’s
translational mandate with Huawei’s infrastructure leadership, the project creates a dual
pathway: prototypes that are validated in Hong Kong’s real-world environments and models
that are scalable, efficient, and sustainable. The benefits are broad—empowering SMEs,
enhancing healthcare delivery, improving safety in robotics and mobility, supporting
environmental sustainability, and strengthening Hong Kong’s role as a global contributor to
trustworthy and open AI. Together, these outcomes represent demonstrable, valuable changes
for Hong Kong’s economy, society, culture, and quality of life.
                                          Reference

1. OpenAI. (2023). GPT-4 Technical Report. arXiv preprint arXiv:2303.08774.
2. Xie, J.; Tonioni, A.; Rauschmayr, N.; Tombari, F.; Schiele, B. (2025). Test‑Time Visual
In‑Context Tuning. In CVPR.
3. Xie, J.; Li, W.; Li, X.; Liu, Z.; Ong, Y. S.; Loy, C. C. (2024). MosaicFusion: Diffusion
Models as Data Augmenters for Large Vocabulary Instance Segmentation. IJCV.
4. Xie, J.; Li, W.; Zhan, X.; Liu, Z.; Ong, Y. S.; Loy, C. C. (2023). Masked Frequency Modeling
for Self‑Supervised Visual Pre‑Training. ICLR.
5. Xie, J.; Zhan, X.; Liu, Z.; Ong, Y. S.; Loy, C. C. (2022). Delving into Inter‑Image Invariance
for Unsupervised Visual Representations. IJCV.
6. Xie, J.; Zhan, X.; Liu, Z.; Ong, Y. S.; Loy, C. C. (2021). Unsupervised Object‑Level
Representation Learning from Scene Images. NeurIPS.
7. Zhan, X.; Xie, J.; Liu, Z.; Ong, Y. S.; Loy, C. C. (2020). Online Deep Clustering for
Unsupervised Representation Learning. CVPR.
8. Li, W.; Xie, J.; Loy, C. C. (2023). Correlational Image Modeling for Self‑Supervised Visual
Pre‑Training. CVPR.
9. Li, Z.; Zhu, Y.; Yang, F.; Li, W.; Zhao, C.; Chen, Y.; Chen, Z.; Xie, J.; Wu, L.; Zhao, R.;
Tang, M.; Wang, J. (2022). UniVIP: A Unified Framework for Self‑Supervised Visual
Pre‑Training. CVPR.
10. Dünkel, O.; Jesslen, A.; Xie, J.; Theobalt, C.; Rupprecht, C.; Kortylewski, A. (2025).
CNS‑Bench: Benchmarking Image Classifier Robustness Under Continuous Nuisance Shifts.
ICCV.
11. Radford, A.; Kim, J.W.; Hallacy, C.; Ramesh, A.; Goh, G.; Agarwal, S.; Sastry, G.; Askell,
A.; Mishkin, P.; Clark, J.; Krueger, G. (2021). Learning Transferable Visual Models from
Natural Language Supervision. ICML.
12. He, K.; Chen, X.; Xie, S.; Li, Y.; Dollár, P.; Girshick, R. (2022). Masked Autoencoders
Are Scalable Vision Learners. CVPR.
13. Chen, T.; Kornblith, S.; Norouzi, M.; Hinton, G. (2020). A Simple Framework for
Contrastive Learning of Visual Representations. ICML.
14. He, K.; Fan, H.; Wu, Y.; Xie, S.; Girshick, R. (2020). Momentum Contrast for
Unsupervised Visual Representation Learning. CVPR.
15. Radford, A.; Kim, J. W.; Hallacy, C.; Ramesh, A.; Goh, G.; Agarwal, S.; Sastry, G.; Askell,
A.; Mishkin, P.; Clark, J.; Krueger, G.; Sutskever, I. (2021). Learning Transferable Visual
Models from Natural Language Supervision. ICML.
16. Dosovitskiy, A.; Beyer, L.; Kolesnikov, A.; Weissenborn, D.; Zhai, X.; Unterthiner, T.;
Dehghani, M.; Minderer, M.; Heigold, G.; Gelly, S.; Uszkoreit, J.; Houlsby, N. (2021). An
Image is Worth 16×16 Words: Transformers for Image Recognition at Scale. ICLR.
17. Caron, M.; Touvron, H.; Misra, I.; Jégou, H.; Mairal, J.; Bojanowski, P.; Joulin, A. (2021).
Emerging Properties in Self‑Supervised Vision Transformers. ICCV.
18. Ho, J.; Jain, A.; Abbeel, P. (2020). Denoising Diffusion Probabilistic Models. NeurIPS.
19. Rombach, R.; Blattmann, A.; Lorenz, D.; Esser, P.; Ommer, B. (2022). High‑Resolution
Image Synthesis with Latent Diffusion Models. CVPR.
20. Li, J.; Li, D.; Savarese, S.; Hoi, S. C. H. (2023). BLIP‑2: Bootstrapping Language‑Image
Pre‑Training. ICML.
21. Alayrac, J.‑B.; Donahue, J.; Luc, P.; Miech, A.; Barr, I.; Hasson, Y.; Lenc, K.; Mensch, A.;
Millican, K.; Reynolds, M.; Ring, R.; Rutherford, E.; Cabi, S.; Han, T.; Gong, Z.; Samangooei,
S.; Monteiro, M.; Menick, J.; Borgeaud, S.; Brock, A.; Nematzadeh, A.; Sharifzadeh, S.;
Binkowski, M.; Barreira, R.; Vinyals, O.; Zisserman, A.; Simonyan, K. (2022). Flamingo: A
Visual Language Model for Few-Shot Learning. NeurIPS.
22. Driess, D.; Xia, F.; Sajjadi, M. S. M.; Lynch, C.; Chowdhery, A.; Ichter, B.; Wahid, A.;
Tompson, J.; Vuong, Q.; Yu, T.; Huang, W.; Chebotar, Y.; Sermanet, P.; Duckworth, D.;
Levine, S.; Vanhoucke, V.; Hausman, K.; Toussaint, M.; Zeng, A.; Mordatch, I.; Florence, P.
(2023). PaLM‑E: An Embodied Multimodal Language Model. arXiv preprint
arXiv:2303.03378.
23. Liu, H.; Li, C.; Wu, Q.; Lee, Y. J. (2023). Visual Instruction Tuning. NeurIPS.
24. Kirillov, A.; Mintun, E.; Ravi, N.; Mao, H.; Rolland, C.; Gustafson, L.; Xiao, T.;
Whitehead, S.; Berg, A. C.; Lo, W.‑Y.; Dollár, P.; Girshick, R. (2023). Segment Anything.
ICCV.
25. Wei, J.; Wang, X.; Schuurmans, D.; Bosma, M.; Ichter, B.; Xia, F.; Chi, E. H.; Le, Q. V.;
Zhou, D. (2022). Chain‑of‑Thought Prompting Elicits Reasoning in Large Language Models.
NeurIPS.
26. Hu, E. J.; Shen, Y.; Wallis, P.; Allen‑Zhu, Z.; Li, Y.; Wang, S.; Wang, L.; Chen, W. (2022).
LoRA: Low-Rank Adaptation of Large Language Models. ICLR.
27. Houlsby, N.; Giurgiu, A.; Jastrzebski, S.; Morrone, B.; de Laroussilhe, Q.; Gesmundo, A.;
Attariyan, M.; Gelly, S. (2019). Parameter‑Efficient Transfer Learning for NLP. ICML.
28. Hendrycks, D.; Dietterich, T. (2019). Benchmarking Neural Network Robustness to
Common Corruptions. ICLR.
29. Innovation, Technology and Industry Bureau (HKSAR). (2022). Hong Kong Innovation
and Technology Development Blueprint. Government publication.
30. ASTRI. (2024). Annual Report 2023/24. Hong Kong Applied Science and Technology
Research Institute.
31. Huawei Cloud. (2024). ‘Leap to Intelligence’ Summit—Ascend AI Cloud & ModelArts
updates (Hong Kong). Corporate press release.
