#!/usr/bin/env python3
"""
Convert evaluation text files to CSV format for Excel compatibility.

This script processes two evaluation files in the 'post' directory:
- JC-gemini-evaluation.txt
- JRFS-gemini-evaluation.txt

And converts them to CSV files with proper headers and formatting for Excel.
"""

import csv
import re
import os
from typing import List, Dict, Optional


def parse_evaluation_file(file_path: str) -> List[Dict[str, str]]:
    """
    Parse an evaluation text file and extract candidate information.
    
    Args:
        file_path: Path to the evaluation text file
        
    Returns:
        List of dictionaries containing candidate information
    """
    candidates = []
    current_candidate = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except UnicodeDecodeError:
        # Try with different encoding if UTF-8 fails
        with open(file_path, 'r', encoding='latin-1') as file:
            content = file.read()
    
    # Split content into blocks for each candidate
    # Look for "Candidate Name:" as the delimiter
    blocks = re.split(r'(?=Candidate Name:)', content)
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = [line.strip() for line in block.strip().split('\n') if line.strip()]
        
        if not lines:
            continue
            
        candidate = {}
        comments_started = False
        comments_lines = []
        
        for line in lines:
            if line.startswith('Candidate Name:'):
                candidate['Candidate Name'] = line.replace('Candidate Name:', '').strip()
            elif line.startswith('Folder:'):
                candidate['Folder'] = line.replace('Folder:', '').strip()
            elif line.startswith('Academic strength:'):
                candidate['Academic Strength'] = line.replace('Academic strength:', '').strip()
            elif line.startswith('Research ability and potential:'):
                candidate['Research Ability and Potential'] = line.replace('Research ability and potential:', '').strip()
            elif line.startswith('Research ability to and potential:'):  # Handle typo in JRFS file
                candidate['Research Ability and Potential'] = line.replace('Research ability to and potential:', '').strip()
            elif line.startswith('Industry Attachment'):
                candidate['Industry Attachment'] = line.split(':', 1)[1].strip()
            elif line.startswith('Comments:'):
                comments_started = True
                comment_text = line.replace('Comments:', '').strip()
                if comment_text:
                    comments_lines.append(comment_text)
            elif comments_started:
                comments_lines.append(line)
        
        if comments_lines:
            candidate['Comments'] = ' '.join(comments_lines)
        
        # Only add candidate if we have essential information
        if 'Candidate Name' in candidate and candidate['Candidate Name']:
            candidates.append(candidate)
    
    return candidates


def write_csv_file(candidates: List[Dict[str, str]], output_path: str) -> None:
    """
    Write candidate data to a CSV file with proper Excel formatting.
    
    Args:
        candidates: List of candidate dictionaries
        output_path: Path for the output CSV file
    """
    if not candidates:
        print(f"No candidates found for {output_path}")
        return
    
    # Define the column order
    fieldnames = [
        'Candidate Name',
        'Folder',
        'Academic Strength',
        'Research Ability and Potential',
        'Industry Attachment',
        'Comments'
    ]
    
    with open(output_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # Write header
        writer.writeheader()
        
        # Write candidate data
        for candidate in candidates:
            # Ensure all fields exist, fill with empty string if missing
            row = {}
            for field in fieldnames:
                row[field] = candidate.get(field, '')
            writer.writerow(row)
    
    print(f"Successfully created {output_path} with {len(candidates)} candidates")


def main():
    """Main function to process both evaluation files."""
    
    # Define input and output paths
    input_dir = 'post'
    output_dir = 'post'
    
    files_to_process = [
        {
            'input': os.path.join(input_dir, 'JC-gemini-evaluation.txt'),
            'output': os.path.join(output_dir, 'JC-gemini-evaluation.csv'),
            'description': 'JC STEM candidates'
        },
        {
            'input': os.path.join(input_dir, 'JRFS-gemini-evaluation.txt'),
            'output': os.path.join(output_dir, 'JRFS-gemini-evaluation.csv'),
            'description': 'JRFS candidates'
        }
    ]
    
    print("Converting evaluation files to CSV format...")
    print("=" * 50)
    
    for file_info in files_to_process:
        input_path = file_info['input']
        output_path = file_info['output']
        description = file_info['description']
        
        print(f"\nProcessing {description}...")
        print(f"Input file: {input_path}")
        print(f"Output file: {output_path}")
        
        if not os.path.exists(input_path):
            print(f"Error: Input file {input_path} not found!")
            continue
        
        try:
            # Parse the evaluation file
            candidates = parse_evaluation_file(input_path)
            
            # Write to CSV
            write_csv_file(candidates, output_path)
            
            # Display summary
            if candidates:
                print(f"✓ Successfully processed {len(candidates)} candidates")
                print(f"  Sample candidate: {candidates[0].get('Candidate Name', 'Unknown')}")
            
        except Exception as e:
            print(f"Error processing {input_path}: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Conversion completed!")
    print("\nThe CSV files are now ready for Excel:")
    print("- JC-gemini-evaluation.csv (JC STEM candidates)")
    print("- JRFS-gemini-evaluation.csv (JRFS candidates)")
    print("\nFeatures:")
    print("- UTF-8 with BOM encoding for proper Excel display")
    print("- Structured columns with clear headers")
    print("- Preserved all evaluation data and comments")
    print("- Ready for Excel import and analysis")


if __name__ == "__main__":
    main()
