                                            <PERSON><PERSON><PERSON>
                                  l<PERSON>@mae.cuhk.edu.hk | +852-59621812 |
                                         Google Scholar | Research Gate
Education
The Chinese University of Hong Kong, Ph.D in Mechanical and Automation                  Aug 2021 – July 2025
Engineering
• GPA: 3.90/4.00
• Coursework: Matrix Analysis and Computations (A), Foundations of Optimization (A), Linear System Theory
  and Design (A), Nonlinear Control Systems (A), Topics in Robotics (A)

Huazhong University of Science and Technology, B.E in Flight Vehicle Design and            Sep 2017 – June 2021
Engineering
• GPA: 3.96/4.00 (2/42)
• Coursework: Calculus (98), Linear Algebra (99), Complex Function and Integral Transformation (98),
  Probability Theory and Mathematical Statistics (93), Ordinary Differential Equations (100), Applied Partial
  Differential Equations (96)

Honors and Awards
• Second prize in Hubei division of National Mathematics Competition                                       Nov 2018
• Kwang-Hua Scholarship                                                                                    Oct 2019
• Outstanding student of Qiming College, Huazhong University of Science and                                Nov 2019
  Technology
• Scholarship for studying excellent                                                                      Oct 2020
• Postgraduate scholarship in CUHK                                                            Aug 2021 – July 2025
• Best student paper award in NSCC & ICSR 2025                                                           May 2025

Research Interests
I am focused on merging data-driven control techniques with output regulation theory in my research. My efforts
are directed towards alleviating the persistent excitation condition for diverse data-driven algorithms and crafting
innovative algorithms with reduced computational costs for addressing the adaptive output regulation problem
and cooperative adaptive output regulation problem. Furthermore, my research also focuses on developing
data-driven output-feedback techniques to solve a range of control problems. This work includes applications in
optimal control, such as the linear-quadratic regulator (LQR), as well as more complex scenarios like
linear-quadratic differential games and the H∞ output regulation problem.
Key Words: Multi-agent systems, cooperative control, output regulation, data-driven control, reinforcement
learning, linear-quadratic regulator.

Publications

Journal Papers
• L. Lin, D. Yurchenko, W. Tong and K. Yang, “Stochastic vibration responses of the bistable electromagnetic
  actuator with elastic boundary controlled by the random signals”, Nonlinear Dynamics, vol. 108, no. 1, pp.
  113-140, 2022.
• L. Lin and J. Huang, “Refined algorithms for adaptive optimal output regulation and adaptive optimal
  cooperative output regulation problems”, IEEE Transactions on Control of Network Systems, vol. 12, no. 1, pp.
  241-250, 2024.
• L. Lin and J. Huang, “Distributed adaptive cooperative optimal output regulation via integral reinforcement
  learning”, Automatica, vol. 170, pp. 111861, 2024.
• L. Lin and J. Huang, “Data-driven optimal output regulation for continuous-time linear systems via internal
  model principle”, IEEE Transactions on Automatic Control, vol. 70, no. 6, pp. 4202-4208, 2025.
• L. Lin and J. Huang, “Direct adaptive cooperative output regulation of unknown multi-agent systems via
  distributed internal model”, IEEE Transactions on Automatic Control, DOI: 10.1109/TAC.2025.3576180,
  November, 2025.
• L. Lin and J. Huang, “Adaptive optimal output regulation for unknown linear systems via internal model
  principle and policy iteration”, Unmanned Systems, DOI: 10.1142/S230138502544008X, 2025.
• L. Lin, H. Lin and J. Huang, “A new approach to the data-driven output-based LQR problem of continuous-time
  linear systems”, under review by IEEE Transactions on Automatic Control.
• X. Xiong, T. Yu, L. Lin, J. Zhao and J. Wu, “Nonlinear instability characterization of hypersonic laminar
  boundary layer”, AIAA journal, vol. 58, no. 12, pp. 5254-5263, 2020.
• Z. Zhang, Y. Zhang, L. Lin, J. Wu, H. Yu, X. Pan, G. Li, J. Wu and T. Xue, “Study on productivity and aerosol
  emissions of magnetic field-assisted EDM process of SiCp/Al composite with high volume fractions”, Journal of
  Cleaner Production, vol. 292, pp. 126018, 2021.
• K. Yang, W. Tong, L. Lin, D. Yurchenko and J. Wang, “Active vibration isolation performance of the bistable
  nonlinear electromagnetic actuator with the elastic boundary”, Journal of Sound and Vibration, vol. 520, pp.
  116588, 2022.

Conference Papers
• L. Lin and J. Huang, “Adaptive Output Regulation via Internal Model Principle and Policy Iteration”, in the 19th
  IEEE International Conference on Control & Automation, 2025.
• L. Lin and J. Huang, “Adaptive cooperative output regulation via distributed internal model and policy
  iteration”, in the 7th International Conference on Industrial Artificial Intelligence (IAI), August, 2025.
Abstract of Research
In nature, limited resources lead many organisms to behave in ways that optimize resource usage
while achieving key objectives. To do so, organisms continuously interact with the environment,
using feedback from these interactions to improve their actions. This process of refining behavior
through environmental engagement forms the conceptual basis of reinforcement learning (RL). In
control theory, optimality is also a crucial concern. However, real-world systems often operate
under fluctuating and uncertain conditions, making the design of an optimal controller highly chal-
lenging. Utilizing the adaptive and optimal nature of RL, researchers from the control community
developed a class of RL-based control methods known as adaptive dynamic programming (ADP),
which is able to achieve optimal control goals for uncertain systems. The RL or ADP was initially
developed to address adaptive optimal control problems for discrete-time (DT) systems. Since
2009, an extended formulation known as integral reinforcement learning (IRL) has been further
introduced to handle uncertain continuous-time (CT) systems.
    Linear systems hold a position of fundamental importance in control theory and engineering.
While model-based control methods are well-established for linear systems, recent years have wit-
nessed growing interest in data-driven control approaches. In particular, IRL has emerged as one
of the key methods for developing adaptive optimal control strategies for various optimal control
problems, including linear-quadratic-regulator (LQR) problem, optimal output regulation problem
(ORP), and linear differential zero-sum game, for unknown continuous-time linear systems. Over
recent years, we have also delved into this field and developed various efficient data-driven control
methods for ORP via IRL, as well as constructed a fundamental output-feedback IRL framework.
Nevertheless, research on data-driven control for linear uncertain systems using IRL remains at an
early stage, with many fundamental issues still unaddressed. For instance, in tackling the ORP,
existing IRL approaches necessitate that the dimension of the exosystem’s system matrix equals
the degree of its minimal polynomial. As another example, current output-based IRL methods
can’t start data collecting and learning until the state observer converges. Even worse, the cur-
rent output-based IRL framework can’t apply to multi-input multi-output (MIMO) linear systems.
These limitations significantly restrict the generalizability of IRL-based methods.
    Building on these observations, this project aims to complete the puzzles of the IRL-based con-
trol framework. Indeed, the three limitations mentioned above exhibit a progressive relationship.
Thus, we divide this project into the following three steps. First, a systematic method will be pro-
posed to construct a minimal realization of the autonomous exosystem in the ORP, which allows
the recasting of the exosystem so that the state of its minimal realization can be utilized in place of
the original exosystem state during the learning process. This will eliminate the existing restriction
on the exosystem when applying IRL methods to ORP. Second, noticing that the observer error for
the state observer in the output-based IRL framework takes the same mode of dynamics as that of
the exosystem in ORP, we can use the technique developed in step one to eliminate the waiting time
by recasting the error dynamics. Third, extend the current output-based IRL framework to MIMO
linear system. By implementing these three steps, we will significantly broaden the applicability
of the current IRL framework. Moreover, the proposed complete IRL-based framework offers a
powerful and versatile technique, with potential applications to a wide range of practical complex
control problems, including the H∞ output regulation problem, output-feedback linear quadratic
differential games, and cooperative output regulation.
Proposed Objectives and Potential Impact of Research Project
Linear systems are of fundamental importance in control theory and engineering. This is because
many physical systems, such as electrical circuits and mechanical springs, can be accurately mod-
eled as linear systems. Furthermore, even real-world nonlinear systems can often be effectively
linearized around an operating point, enabling the application of powerful linear analysis and con-
trol design techniques. However, although model-based control methods for linear systems are
well-established for a variety of control problems, their practical application remains challenging.
This is largely due to inaccuracies in system models, since many real-world systems operate under
fluctuating and uncertain conditions with system parameters changing over time. The challenge
of inaccurate system modeling has spurred growing interest in data-driven control methods. As a
powerful technique in this domain, the IRL framework has been widely applied to solve diverse
optimal control problems in the past a few years. Nevertheless, the current IRL framework remains
incomplete, with various challenges still awaiting resolution. For example, when addressing the
optimal ORP, existing IRL methods require the dimension of the exosystem’s system matrix to
match the degree of its minimal polynomial. Besides, current output-based IRL approaches can-
not begin data collection and learning until the state observer has fully converged. More critically,
present output-based IRL framework is not applicable to MIMO linear systems. These limitations
substantially constrain the generalizability of IRL-based methods. Based on these observations,
this project aims to achieve the following objectives:

Objectives
   1. Propose a systematic method to construct a minimal realization of the autonomous exosystem
      in the ORP, and use the state of the minimal realization to replace the original exosystem state
      during the learning process. This will eliminate the existing restriction on the exosystem
      when applying IRL methods to ORP.

   2. Use the technique developed in Objective 1 to eliminate the waiting time of the output-based
      IRL framework by recasting the dynamics of the observer error.

   3. Extend the current output-based IRL framework to MIMO linear system.

   4. Apply the proposed enhanced IRL-based frameworks to solve some practical complex con-
      trol problems, such as the H∞ output regulation problem, output-feedback linear quadratic
      differential games, and cooperative output regulation.
Potential Impact
Academic: The successful completion of the proposed objectives will substantially broaden the
applicability of the IRL-based control framework. A systematic IRL-based approach will be pro-
posed for solving general ORP. The output-based IRL framework will also be enhanced to handle
general linear systems. Under our enhanced framework, the data collection and learning can start at
any time without waiting. Furthermore, with our enhanced IRL framework, we could solve many
practical complex control problems, such as the H∞ output regulation problem, output-feedback
linear quadratic differential games, and cooperative output regulation, with a better exposition. Be-
sides, the methodologies developed are expected to inspire and inform future research in other areas
of data-driven control, such as data informativity, data-enabled policy optimization, and inverse re-
inforcement learning.

Potential Practical Applications: The theoretical advances developed in this project hold poten-
tial for application across a range of practical domains, including power systems, vehicle cruise
control, motor speed regulation, robotics, and actuator control.

Educational: The primary outcomes of this project will include publications in leading journals,
conference proceedings, and book chapters. Additional deliverables will consist of software pack-
ages and patent filings. To ensure broad knowledge dissemination, results will also be shared
through workshops and short courses tailored for both academic and professional audiences.
Background of Research, Research Plan and Methodology
Background
As a data-driven technique, reinforcement learning (RL) or adaptive dynamic programming (ADP)
has proven to be effective in addressing a wide range of optimal control problems with partially or
completely unknown dynamics [1] [2] [3]. For instance, [4] studied the linear-quadratic regulator
(LQR) problem for partially unknown linear systems by devising a policy-iteration (PI) method to
iteratively solve an algebraic Riccati equation online. [5] extended the results of [4] to completely
unknown linear systems using a PI-based approach. As the PI-based method necessitates a stabi-
lizing feedback gain to initiate the iteration process, [6] introduced a value-iteration (VI) technique
to address the LQR problem for unknown linear systems without needing an initial stabilizing
feedback gain. Based on the PI method proposed by [5], [7] further studied the data-driven opti-
mal output regulation problem (ORP) for linear systems with unknown state equation. Then, [8]
applied the VI method of [6] to solve the optimal output regulation problem. More recently, the
results of [7] were further improved by [9]. On the other hand, recent years have also witnessed
a surge of interest in studying cooperative output regulation problem (CORP) for unknown linear
multi-agent systems (MASs) via integral reinforcement learning (IRL) approaches. For example,
reference [10] considered the optimal CORP for linear MASs with unknown state equations by
combining the PI method and the distributed-observer approach. Reference [11] further considered
the optimal CORP for linear MASs with unknown state equations over jointly connected switching
networks. Due to the use of feedforward control, the approaches in [10] and [11] require the output
equation to be known. To deal with the unknown output equation, reference [12] developed both
PI-based and VI-based methods to handle the optimal CORP by combining the distributed observer
and distributed-internal-model approach. However, the approaches in [12] are limited to unknown
MASs with single-input and single-output (SISO) followers over acyclic communication networks.
Recently, [13] has extended the VI-based method of [12] to unknown MASs with multi-input and
multi-output (MIMO) followers over a general static and connected digraph, and proposed an im-
proved VI-based method with less computing cost and milder solvability conditions. Nevertheless,
for ORP and CORP of unknown linear systems, existing IRL methods require the dimension of
the exosystem’s system matrix to match the degree of its minimal polynomial. This limitation has
severely prevented the application of current IRL-based methods.
    On the other hand, all papers mentioned above assumed the availability of the state of the
system. Since, in more practical scenarios, only the output of the system is available, it is interesting
to further engage studies on the RL/ADP methods based on the measurable output information.
The first attempt on solving the data-driven output-based LQR problem for continuous-time linear
systems was made in [14] for systems with known input matrix. [15] proposed a data-driven output-
based RL algorithm to solve the LQR problem for completely unknown continuous-time linear
system. However, the method of [15] introduced a discounted cost function whose discount factor
had an upper bound determined by the system model for ensuring stability. On the basis of the
state-based ADP algorithms in [5] and [6], [16] and [17] developed both PI-based and VI-based
output-based ADP algorithms that involves the construction of a model-free observer, thus avoiding
the use of discounted cost functions. More recently, [18] proposed an output-based ADP algorithm
that utilizes the historical continuous-time input-output trajectory data to reconstruct the current
state without using a state observer at the cost of complicated function approximation with neural
networks.
    The approach of [16] and [17] appears quite attractive as it leverages the well-known separation
principle. Inspired by this approach, we proposed a new output-based IRL framework in [19]. This
new framework leads to improved output-based PI and VI methods for the LQR problem with less
computing cost and milder solvability conditions compared with the PI and VI methods in [16]
and [17]. However, it is noted that the current output-based IRL frameworks in [17] and [19]
both require a waiting period before data collection and learning due to the use of model-free state
observer. Without waiting until the state observer converges, the collected data will not be reliable
due to estimation error. However, the choice of waiting time is highly subjective, relying on the
engineer’s experience and the unknown initial error, which introduces significant uncertainty into
the learning results. More critically, existing output-based IRL frameworks are not applicable to
MIMO linear systems. These limitations significantly hinder the applicability of current output-
based IRL frameworks.
    Based on the above observations, this project aims to fix these limitations to enhance the cur-
rent IRL-based control framework and apply the improved framework to solve various complex,
practical control problems.

Methodology
The method of RL involves an actor or agent that interacts with its environment and adjusts its
actions or control policies in response to stimuli it receives from its actions. Reinforcement learning
methods typically adopt an Actor-Critic framework, wherein an actor component implements an
action or control policy to the environment, while a critic component evaluates the value of that
action. Following this evaluation, different strategies can be employed to enhance the action to
ensure that the updated policy produces a value superior to the previous one. Thus, the Actor-Critic
structure implies two steps: policy evaluation by the critic followed by policy improvement, as
shown in Fig. 1.
     While the research of RL on discrete-time (DT) systems bloomed in the early stage [20], its
extension to continuous-time (CT) systems has lagged. This is mainly because RL is significantly
more challenging for CT systems compared to DT systems [20, 21]. To see this, let’s consider
a CT systemR ẋ = f (x, u) with feedback control policy u = u(x). Define the cost function as
               ∞
V u (x(t)) = t r(x, u)dτ . Then, the Hamiltonian function for this CT system is given by

                                  H = r(x, u) + (∇x V u )T f (x, u)                                (1)

Thus, the CT Bellman equation is given by

                                   0 = r(x, u) + (∇x V u )T f (x, u)                               (2)

Indeed, the Bellman function for CT systems typically incorporates the complete system dynamics
f (x, u) within its formulation while the DT’s doesn’t. This implies that utilizing the CT Bellman
equation as a foundation for reinforcement learning becomes challenging unless the entire dynam-
ics of the system are known. To avoid the appearance of system dynamics
                                                                     u
                                                                           in CTuBellman equation,
Baird tried to approximate the term (∇x V u )T f (x, u) by the term V (x(t+δt))−V
                                                                             δt
                                                                                  (x(t))
                                                                                         based on the
                        u T             u
observation that (∇x V ) f (x, u) = V̇ [22], where δt is the sample period. However, this is just
an approximation. In 2009, an exact method to deal with the CT Bellman equation is given in [4]
by integrating both sides of equation (2), which gives
                              Z t+δt
                        0=           r(x, u)dτ + V u (x(t + δt)) − V u (x(t))               (3)
                                t

Relying on (3), the reinforcement learning method can be developed for CT systems, and this class
of RL/ADP methods is called the integral reinforcement learning (IRL) methods [3, 20]. There are
essentially two IRL methods: policy-iteration (PI) method [4, 5] and value-iteration (VI) method
[6]. A key distinction is that PI requires a stabilizing control gain to initialize the iteration process,
whereas VI does not. However, this comes with a trade-off: the PI method is simpler and converges
significantly faster.

Research Plan
The research of this project is divided into four tasks, scheduled as Table 2.

Task 1: Design a minimal realization of the autonomous exosystem in the ORP

Consider the exosystem in the following form.

                                                v̇ = Sv                                               (4)

An standard assumption on (4) is made as follows:

Assumption 1. The minimal polynomial of S is known.

    Assumption 1 is made for two purposes. First, under Assumption 1, the minimum p − copy
internal model of the matrix S is known, which is essential for the utilization of the internal model
approach to solve the ORP. Second, as claimed in [7], under Assumption 1, there exist known
vector v̂(t), known matrix Ŝ whose characteristic polynomial is equal to the minimal polynomial
of S, and unknown matrix G depending on v(0) such that, v̂˙ = Ŝv̂, v = Gv̂. If the dimension of
the exosystem’s system matrix is larger than the degree of its minimal polynomial, the dimension
of v is larger than v̂. In this case, the data stack matrix of v can never have a full rank, and thus the
IRL method will fail in this case.
    Nevertheless, it is possible for us to solve this problem by using the minimal realization (Ŝ, G)
to recast exosystem (4). However, [7] didn’t provide a rigorous proof of the existence of (Ŝ, G),
and also didn’t give an explicit and general way on how to obtain (Ŝ, G). In fact, to the best of our
knowledge, no paper has discussed how to systematically construct Ŝ and G yet. The main purpose
of this task is to give a systematic approach for designing Ŝ and G.

Task 2: Eliminate the waiting time of the output-based IRL framework
Consider the the linear system ẋ = Ax + Bu, y = Cx, where x ∈ Rn is the system state, u ∈ Rm
is the input, y ∈ Rp is the measurable output, and A, B, C are unknown matrices. Assume the pair
(A, C) is observable. Then, there exists an observer gain L such that the eigenvalues of A − LC
can be arbitrarily placed. If A − LC is Hurwitz, a model-free observer of x can be designed as
                                                                   
                                                 Im ⊗ b           0
                            ζ̇ = (Im+p ⊗ A)ζ +            u+            y                      (5)
                                                   0           Ip ⊗ b

such that lim (M ζ − x) = 0 exponentially, where M is an unknown parametrization matrix, A ∈
         t→∞
Rn×n has the same characteristic polynomial as A − LC, and (A, b) is a known controllable pair.
Let eζ = M ζ − x be the observer error. Then, (5) can be reformulated as ζ̇ = Aζ ζ + Bζ u + Eζ eζ
with some constant matrices Aζ , Bζ , Eζ . [19] proposed an output-based IRL control framework by
using the steady state of ζ for learning after the inaccessible variable eζ becomes small enough.
However, the choice of waiting time is highly subjective, relying on the engineer’s experience
and the unknown initial estimation error, which may bring significant uncertainty into the learning
process and result in a failure of control.
    To eliminate the waiting time of the output-based IRL framework, we can consider using the
technique developed in Task 1. Actually, the dynamics of eζ satisfy ėζ = (A − LC)eζ . Even though
A − LC is unknown, its characteristic polynomial is user-defined. Thus, we can recast eζ with the
characteristic polynomial of (A − LC) using the method established in Task 1. In this way, the
inaccessible variable eζ will be replaced by a user-defined known variable, and the waiting time
can be eliminated.

Task 3: Extend the current output-based IRL framework to MIMO linear system
A drawback of the output-based IRL control framework in [19] is that it can only apply to SISO
linear systems. In fact, this problem shows up in almost every output-based data-driven approach.
This is because the output-based data-driven approaches always require using input and output data
to reconstruct the state data. For MIMO linear systems, the reconstruction is not unique, which will
lead to the failure of learning due to non-fulfillment of the persistent excitation conditions. Take
the output-based IRL control framework as an example, there exist multiple L leading to the same
stable characteristic polynomial of A − LC for MIMO linear systems. However, these different L
will lead to the same observer dynamics (5). Thus, in this MIMO case, the data stack matrix of ζ
can never have full rank due to multiple possibilities of system matrices.
    To tackle this problem, we can consider using extra constraints to make the gain matrix L
uniquely determined. The potential methods include optimal observer design (Kalman filter),
eigenstructure assignment (specifying the eigenvectors of A − LC), optimization-based approach
(searching for minimum norm solution), or specifying additional performance criteria.

Task 4: Apply the enhanced IRL-based frameworks to some practical control problems
With the completion of the above three tasks, our enhanced IRL-based frameworks are able to solve
various practical control problems, such as the H∞ output regulation problem, output-feedback lin-
ear quadratic differential games, and cooperative output regulation with an efficient and general
structure. Take the cooperative output regulation problem as an example, using the technique
developed in Task 1 will eliminate the utilization of the distributed observer since the state of
the exosystem has already been recast by a minimal realization. The extension of our IRL-based
frameworks to these practical control problems will bring significant new results and new insights.
Illustrations

                            Table 1: List of Abbreviations

            Abbreviations                   Meaning
               ADP        Adaptive/approximate dynamic programming
               CORP         Cooperative output regulation problem
                CT                     Continuous-time
                DT                        Discrete-time
                IRL             Integral reinforcement learning
               LQR                 Linear-quadratic regulator
               MAS                    Multi-agent system
               MIMO                 Multi-input multi-output
                ORP                Output regulation problem
                 PI                     Policy iteration
                RL                  Reinforcement learning
               SISO                Single-input single-output
                 VI                      Value iteration



                               Actor


                       Policy
                     Improvement              Environment



                               Critic
                          Policy Evaluation



                Figure 1: The Actor-Critic structure of RL methods



                       Table 2: Schedule of the research plan
                         Schedule (months after project go-ahead)
                 Tasks
                         1-4 5-8 9-12 13-16 17-20 21-24
                 Task 1 •
                 Task 2          •
                 Task 3               •        •
                 Task 4                        •       •      •
Pathways to Impact Statement
The impact of this research will be realized through three core pathways:

   1. Academic Pathway: The project will provide a systematic IRL-based framework for solving
      general optimal control problems including optimal ORP, LQR and linear quadratic differ-
      ential games. These advancements will be disseminated to the academic community through
      publications in leading control theory journals and presentations at major international con-
      ferences.

   2. Develop an Open-Source Software Toolkit: We will package the core IRL algorithms into a
      well-documented MATLAB toolbox. This will allow researchers and engineers in fields like
      power systems, robotics, and vehicle cruise control to prototype and deploy our methods,
      reducing the barrier to adoption.

   3. Educational and Broad Dissemination: All public outputs—including tutorials of findings,
      software and documentation—will be hosted on a dedicated project website to ensure long-
      term access and dissemination.
References
 [1] R. S. Sutton and A. G. Barto, Reinforcement learning: An introduction, MIT press, 2018.

 [2] D. Bertsekas, Reinforcement learning and optimal control, Athena Scientific, 2019.

 [3] F. L. Lewis, D. Vrabie and V. L. Syrmos, Optimal control, John Wiley & Sons, 2012.

 [4] D. Vrabie, O. Pastravanu, M. Abu-Khalaf, and F. L. Lewis, “Adaptive optimal control for
     continuous-time linear systems based on policy iteration”, Automatica, vol. 45, no. 2, pp.
     477-484, 2009.

 [5] Y. Jiang and Z. P. Jiang, “Computational adaptive optimal control for continuous-time linear
     systems with completely unknown dynamics”, Automatica, vol. 48, no. 10, pp. 2699-2704,
     2012.

 [6] T. Bian and Z. P. Jiang, “Value iteration and adaptive dynamic pro- gramming for data-driven
     adaptive optimal control design” Automatica, vol. 71, pp. 348–360, 2016.

 [7] W. Gao and Z. P. Jiang, “Adaptive dynamic programming and adaptive optimal output regu-
     lation of linear systems” IEEE Transactions on Automatic Control, vol. 61, no. 12, pp. 4164-
     4169, 2016.

 [8] Y. Jiang, W. Gao, J. Na, D. Zhang, T. T. Hämäläinen, V. Stojanovic and F. L. Lewis, “Value
     iteration and adaptive optimal output regulation with assured convergence rate”, Control En-
     gineering Practice, vol. 121, pp. 105042, 2022.

 [9] L. Lin and J. Huang, “Refined Algorithms for Adaptive Optimal Output Regulation and Adap-
     tive Optimal Cooperative Output Regulation Problems”, IEEE Transactions on Control of
     Network Systems, DOI: 10.1109/TCNS.2024.3462549, 2024.

[10] W. Gao, Z. P. Jiang, F. L. Lewis and Y. Wang, “Cooperative optimal output regulation of multi-
     agent systems using adaptive dynamic programming”, in 2017 American Control Conference
     (ACC), 2017, pp. 2674-2679.

[11] L. Lin and J. Huang, “Distributed adaptive cooperative optimal output regulation via integral
     reinforcement learning”, Automatica, vol. 170, pp. 111861, 2024.

[12] W. Gao, M. Mynuddin, D. C. Wunsch and Z. P. Jiang, “Reinforcement learning-based coop-
     erative optimal output regulation via distributed adaptive internal model”, IEEE transactions
     on neural networks and learning systems, vol. 33, no. 10, pp. 5229-5240, 2021.

[13] L. Lin and J. Huang, “Direct adaptive cooperative output regulation of unknown multi-agent
     systems via distributed internal model”, IEEE Transactions on Automatic Control, DOI:
     10.1109/TAC.2025.3576180, November, 2025.

[14] L. M. Zhu, H. Modares, G. O. Peen, F. L. Lewis and B. Yue, “Adaptive suboptimal output-
     feedback control for linear systems using integral reinforcement learning”, IEEE Transactions
     on Control Systems Technology, vol. 23, no. 1, pp. 264-273, 2014.
[15] H. Modares, F. L. Lewis and Z. P. Jiang, “Optimal output-feedback control of unknown
     continuous-time linear systems using off-policy reinforcement learning”, IEEE Transactions
     on Cybernetics, vol. 46, no. 11, pp. 2401-2410, 2016.

[16] S. A. A. Rizvi and Z. Lin, “Reinforcement learning-based linear quadratic regulation of
     continuous-time systems using dynamic output feedback”, IEEE transactions on cybernet-
     ics, vol. 50, no. 11, pp. 4670-4679, 2019.

[17] S. A. A. Rizvi and Z. Lin, Output Feedback Reinforcement Learning Control for Linear Sys-
     tems, Birkhäuser, 2023.

[18] L. Cui and Z. P. Jiang, “Learning-Based Control of Continuous-Time Systems Using Output
     Feedback”, in 2023 Proceedings of the Conference on Control and its Applications (CT),
     2023, pp. 17-24.

[19] L. Lin, H. Lin and J. Huang, “A new approach to the data-driven output-based LQR problem of
     continuous-time linear systems”, under review by IEEE Transactions on Automatic Control.

[20] F. L. Lewis and D. Vrabie, “Reinforcement learning and adaptive dynamic programming for
     feedback control”, IEEE circuits and systems magazine, vol. 9, no. 3, pp. 32-50, 2009.

[21] F. L. Lewis and D. Liu, Reinforcement learning and approximate dynamic programming for
     feedback control, John Wiley & Sons, 2013.

[22] L. C. Baird, “Reinforcement learning in continuous time: Advantage updating”, in Proceed-
     ings of 1994 IEEE International Conference on Neural Networks, vol. 4, pp. 2448-2453,
     1994.
Past Research Experience
  • I participated in the provincial Students’ Innovation and Entrepreneurship Training Program
    of 2019 together with my classmates. Project name: research on special processing tech-
    nology of hypersonic aircraft surface microstructure, and I served as the project leader. The
    main purpose of this research is to find the optimum process parameters of magnetic field-
    assisted electrical discharge hole machining. This research has led to a paper in the Journal
    of Cleaner Production.

  • In July 2019, I went to the turbulence laboratory of Changping campus, Peking University,
    with a doctoral student to assist him in the experiments of measuring the unstable waves of
    the hypersonic cone boundary layer. Relevant research results have been compiled into a
    paper accepted by the AIAA journal.

  • In 2021, I conducted research on system dynamics and vibration for my final year project as
    an undergraduate. The work comprehensively investigated the stochastic vibration responses
    of a bistable electromagnetic actuator with an elastic boundary, controlled by both Gaussian
    white noise and low-pass filtered stochastic noise. Utilizing both simulation and experimen-
    tal methods, the study elucidated the influence of key design parameters on the actuator’s
    performance. The results were compiled into two journal papers, published in Nonlinear
    Dynamics (where I am the first author) and the Journal of Sound and Vibration.

  • From August 2021 to July 2025, my doctoral research focused on developing effective data-
    driven control strategies. The core of my work involves merging data-driven control with
    output regulation theory. A key contribution is directed towards alleviating the stringent
    persistent excitation condition required by existing algorithms and crafting innovative meth-
    ods with significantly reduced computational costs. These methods specifically address the
    adaptive and cooperative adaptive output regulation problems. Furthermore, I have developed
    novel data-driven output-feedback techniques to solve fundamental control problems such as
    the linear-quadratic regulator (LQR). During my PhD, I authored a strong publication record,
    including five first-author journal papers—three of which were published in top-tier control
    theory journals (IEEE Transactions on Automatic Control and Automatica)—and two first-
    author conference papers. Additionally, I have one first-author paper currently under review
    with IEEE Transactions on Automatic Control.
Leadership Experience
  • In 2019, I led a project on green manufacturing via electrical discharge machining under the
    Students’ Innovation and Entrepreneurship Training Program as an undergraduate.

  • I chaired a regular session (Learning-Based Control I) in the 2025 IEEE 19th International
    Conference on Control & Automation (ICCA).
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
l    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
l    The nominee should complete Part A of the referee’s report.
l    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
l    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
l    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars            (to be completed by the nominee)
Name of Nominee :       (Surname) Lin
                        (Other name) Liquan
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
l   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
l   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
l   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
l   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
l   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
l   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
l   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known the nominee for approximately three years in my capacity as both his instructor for the course
Linear System Theory and Design and as a member of his PhD dissertation committee.




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                               Excellent      Very Good        Good               Average          No basis
                               (top 5%)       (top 6-20%) (top 21-50%)           or below            for
                                                                                (lower than       judgment
                                                                                   50%)
Intellectual capacity              ☐               R                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                   R               ☐                ☐                 ☐               ☐
Knowledge of proposed
research study
                                   R               ☐                ☐                 ☐               ☐
Analytical power and
reasoning
                                   R               ☐                ☐                 ☐               ☐
Judgment                           ☐               R                ☐                 ☐               ☐
Imagination and
originality
                                   R               ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                   R               ☐                ☐                 ☐               ☐
Skills of writing and
argumentation                      ☐               R                ☐                 ☐               ☐
Capacity for independent
work
                                   R               ☐                ☐                 ☐               ☐
Reliability and sense of
responsibility
                                   R               ☐                ☐                 ☐               ☐
Leadership experience              ☐               R                ☐                ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

Dr. Lin possesses a good technical foundation, evidenced by his academic record and advanced coursework in
mathematics and control theory. His research ability is proven by an impressive publication record, with
multiple first-author papers in top-tier journals like IEEE Transactions on Automatic Control and Automatica.
His work is highly innovative, skillfully merging data-driven control (e.g., reinforcement learning) with
classical theory (e.g., output regulation) to solve complex problems in multi-agent systems. His focus on
reducing computational costs and theoretical limitations demonstrates practical insight and signifies high
potential for future impact. Based on my observation, he is precisely the type of promising early-career
researcher a JRFS is designed to support.




                                                       3
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                  R      Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: Benmei Chen                                     Title: Prof
                             (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*
     University / Organisation: The Chinese University of Hong Kong
     Position: Department Chairman and Graduate Division Head in the Department of MAE
     E-mail Address: <EMAIL>
     Telephone Number#: +852 - 3943 8054
     (# Please provide country code and area code, e.g. +86-10-1234567)
l     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
    R I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
    R I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date: 27/08/2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Lin
                        (Other name) Liquan
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?




About half a year, through workshop/conference.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent   Very Good        Good             Average          No basis
                           (top 5%)    (top 6-20%) (top 21-50%)         or below            for
                                                                       (lower than       judgment
                                                                          50%)
Intellectual capacity                      ☐              ☐                 ☐               ☐
                                            ☐              ☐                 ☐               ☐
Ability for conducting
scholastic research
                              
                                            ☐              ☐                 ☐               ☐
Knowledge of proposed
research study
                              

                                            ☐              ☐                 ☐               ☐
Analytical power and
reasoning
                              
Judgment                                   ☐              ☐                 ☐               ☐
                                            ☐              ☐                 ☐               ☐
Imagination and
originality
                              

                                            ☐              ☐                 ☐               ☐
Motivation and
perseverance
                              

                                            ☐              ☐                 ☐               ☐
Skills of writing and
argumentation
                              

                                            ☐              ☐                 ☐               ☐
Capacity for independent
work
                              

                                            ☐              ☐                 ☐               ☐
Reliability and sense of
responsibility
                              
Leadership experience                      ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)


I am pleased to recommend Liquan Lin based on his strong academic performance. His Ph.D.
training has equipped him with solid mathematical skills, which have translated into notable
achievements in merging data-driven control techniques with output regulation theory. His
impressive GPA, complemented by a commendable publication record in reputable journals,
reflects his expertise and innovation in the field of Automation Engineering. In my assessment,
he represents a promising early-career researcher that JRFS aims to support and foster.




                                                3
                                                       JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: HU GUOQIANG                                     Title: Prof
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Nanyang Technological University, Singapore
 Position: Professor
 E-mail Address: <EMAIL>
 Telephone Number#: +65 67904362
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.




 Signature:                                                  Date: 28 Aug 2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
