                                            LIU MEIYU
                             Tel: (852) 6358 5070        E-mail: <EMAIL>


Education
    Jan 2023- present            The Chinese University of Hong Kong                    Hong Kong SAR
Ph.D. in Mechanical Engineering & Automation                 Supervisor: <PERSON><PERSON><PERSON>
    Sep 2015- Oct 2016           The University of Hong Kong                            Hong Kong SAR
M.S. in Mechanical Engineering                               Supervisor: <PERSON>
    Sep 2011- Jul 2015           South China University of Technology                Guangzhou, China
B.Eng. in Mechanical Engineering & Automation                Supervisor: Lin Li

Academic Research
    Dec 2024– Present            Ph.D. Student              The Chinese University of Hong Kong
Prediction of Curing Deformation in Woven CFRP Based on LSTM-FEA Integrated Simulation

Proposed a cross-scale LSTM-FEA system that accurately predicts curing deformations of thermoset woven
composites featuring multi-scale characteristics and nonuniform features. It innovatively breaks down the
working environment barriers between AI tools and finite element software, truly achieving seamless
interaction and iteration.
    Jan 2023– Nov 2024           Ph.D. Student              The Chinese University of Hong Kong
Multiscale Simulation and Homogenization of Thermo-Mechanical Properties for Curing Process of
Carbon Fiber Reinforced Thermoset Composites

Developed an innovative indirect characterization method to address the challenge of directly measuring
curing parameters for aerospace-grade high-viscosity toughened resins. Established a fully coupled thermo-
chemo-mechanical constitutive model via ABAQUS user subroutines, enabling accurate prediction of curing
deformations in toughened resin-based composite laminates.
    Sep 2021- Jan 2023           Research Assistant         The Chinese University of Hong Kong
Characterizing the Fiber-Matrix Interface for Unidirectional Composites with Cohesive Zone Model

Built a cohesive zone model (CZM) incorporating fiber-matrix interface properties, combined with
representative volume element (RVE) modeling and 3D nonlinear periodic boundary conditions, achieving
accurate prediction of interface debonding behavior under large deformations.
    Oct 2016 – Sep 2021          Project Manager            NanoFIL Filtration Technology Limited
R&D and Industrialization of Multifunctional Nanofiber Air Filters

This project was an incubation initiative at Hong Kong Science Park, dedicated to developing a
multifunctional nanofiber air filter leveraging electrospinning technology. As a founding team member, I took
charge of R&D research team during the initial startup phase. During the technology transformation stage, I
oversaw the establishment of mass production lines and optimized formulation adjustments in line with the
specific characteristics of mass production equipment.
Publications:
[1] Liu, M., Feng, Y., Sun, D., Zhao, Y., Li, X., Li, X., & Zhang, W. (2025). Modeling‐based characterization for thermal
expansion and chemical shrinkage of highly viscous epoxy resin with thermoplastic additives under the curing condition.
Polymer Composites, 46(7), 6657-6669.
[2] Liu M., Zhao, Y., Feng, Y., Li, X., & Zhang, W. (2024). Characterizing thermal conductivity of high toughness resin
and its carbon fiber reinforced composite under curing condition. Journal of Advanced Manufacturing Science and
Technology, 4(2).
[3] Liu M., Feng Y., Sun D., Li X., & Zhang W. Characterization and Modeling for Curing-Induced Deformations of the
Prepregs with Toughened and Highly Viscous Resin. Composites Science and Technology. (Under Revision)
[4] Liu M., Sun D., Feng Y., Hu Z., & Zhang, W. A Cross-Scale LSTM-FEA Integrated Modeling System for Curing
induced deformation Prediction of woven CFRP. (The manuscript has been completed, and the target journal
is Composites Part B: Engineering.)
[5] Liu M., Zheng Q., Zhang, W. Rapid prediction for curing induced deformation of preformed woven CFRPs.
(ASPEN 2025)
[6] Liu M., Zhang, W. An indirect non-contact method for characterizing thermal expansion and chemical shrinkage of
highly viscous epoxy resin BA9916. The 13th Asian-Australasian Conference on Composite Materials (ACCM13).
[7] Liu M., Zhang, W. Multi-scale modeling for the curing induced deformations of the toughened resin-based composite.
The 24th International Conference on Composite Materials (ICCM24).
[8] Sun D, Liu M., Tang C., Feng Y., Zheng Q., & Zhang, W. A mesoscopic modeling scheme for 3D virtual testing
of woven prepregs during forming processes.
[9] Zhang, W., Liu, M., Zou, J., Han, Z., Xiong, Y., Liang, B., & Zhang, W. (2024). Experimental characterization and
numerical prediction for mechanical aging of epoxy and its carbon fiber‐reinforced composite under hydrothermal
conditions. Polymer Composites, 45(12), 11342-11356.
[10] Feng, Y., Han, Z., Liu, M., & Zhang, W. (2024). Multiscale modeling for viscoelasticity of woven CFRP
considering preforming and curing effects via finite element and long-short term memory analysis. Composites Part
A: Applied Science and Manufacturing, 186, 108397.
[11] Feng, Y., Han, Z., Liu, M., Zheng, W., Liang, B., Xiong, Y., & Zhang, W. (2024). Rapid prediction for deflection
history of CFRP beams during curing using LSTM network and its application to stacking sequence optimization
with genetic algorithm. Composites Part A: Applied Science and Manufacturing, 182, 108195.


Awards
2nd Prize, 6th National College Mechanical Innovation Design Competition (2014)
PMP® Certification - Project Management Professional
2nd Prize, 2nd National College Smart Terminal Simulation Technology Competition (2024)


Intellectual Property
• Lin Dezhi, Zhao Yueqing, Zhang Weizhao, Liu Meiyu. Automated Multi-scale RVE Model Generation Software
for Unidirectional Composite Structures. China National Copyright Administration (Registration No.
2024SR1099792)
      Carbon fiber-reinforced polymers (CFRPs) are ideal for lightweight vehicle manufacturing
owing to their excellent strength-to-weight ratio. However, mass production of CFRP is an
energy-intensive process with a significant carbon footprint and expensive resources. Among
all production techniques, thermoforming is commonly adopted for its geometric flexibility and
high efficiency. Thermoforming consists of preforming, compaction and curing steps. A key
challenge in thermoforming is that process induced deformation (PID) usually arises from
curing-induced residual stress, driven by the inherent heterogeneity and anisotropy of CFRP
structures. Such deformations lead to a high scrap rate and impair the service reliability of the
final components.

      Existing PID prediction methods can be mainly divided into the following three categories:
Firstly, the most used Finite Element squared (FE²) method is computationally intensive. Next,
macroscale AI-based methods require retraining databases when changing part geometries. The
third one is the Self-Consistent Clustering Analysis (SCA), which is always restricted by
convergence, especially for multi-field analysis. During thermoforming materials exhibit non-
uniform features, time-temperature-dependent properties, and multi-field coupled behaviors.
To address these drawbacks, this proposal aims to establish a cross-scale LSTM-FEA modeling
system for PID prediction of woven CFRPs.

      A pre-trained LSTM neural network will be integrated with FEA models in the proposed
work. Yarn angles, degree of cure, temperature, and time were trained as inputs to output
stiffness matrix components. Due to the operating environment isolation between AI networks
and FEA models, the LSTM network will be decomposed into transferable mathematical
formulations and parameters, both of which will be integrated into a chemo-thermo-mechanical
coupled model via user-defined subroutines in ABAQUS, enabling rapid local parameter
determination for large structures regardless of geometric and material inhomogeneity.

     Beyond PID prediction, a closed-loop "prediction-feedback" system will be established to
adjust heating temperatures with curing exothermic release. This not only mitigates overheating
but also reduces overall energy consumption. This work provides new possibilities for rapid
prediction, cross-scale modeling of complex 3D woven CFRPs, supporting carbon neutrality
and Industry 4.0 goals.
1. Objectives

Objective 1: Construct a Cross-Scale LSTM-FEA Integrated Modeling Framework for
PID Prediction of Woven CFRPs
      Existing methods for PID prediction face two challenges: high computational cost of
traditional FEA and limited applicability of AI-driven methods. Among AI-driven methods,
macroscopic AI-driven method enables fast prediction but fails in structural geometry changes
due to inability to handle mesoscopic-macroscopic scale transition. In contrast, the other
method, Self-Consistent Clustering Analysis (SCA), solves cross-scale issues but struggles with
convergence under chemo-thermo-mechanical coupling analysis.
      This project will integrate the strengths of both approaches to develop a cross-scale LSTM-
FEA model that achieves geometry-independent, rapid PID prediction.
Critical Elements:
•    Resolve the isolation between LSTM and FEA operating environments that blocks
     seamless data transfer. To this end, a mathematical connection mechanism will be
     established, decomposing the trained LSTM model into transferable mathematical
     formulations and parameters to lay the foundation for subsequent FEA embedding.

Objective 2: Realize the integration of the LSTM model into chemo-thermo-mechanical
coupling analysis in ABAQUS
      Material models during curing are time-temperature-DOC-dependent and linked together
intricately. As demonstrated in Figure 1, the embedded LSTM will acquire temperature data
from heat transfer model and DOC value from curing kinetics simultaneously. Curing process
itself releases reaction exotherm, leading to temperature non-uniform distribution and variation
rate inside composite. Meanwhile, temperature-DOC-dependent thermal expansion and
chemical shrinkage coefficients should be integrated as well to calculate the effective strain for
accurate stress update in the ABAQUS.
Critical Elements:
•    Translate the mathematical formulations of the trained LSTM model into Fortran language
     and store parameters in UEXTERNALDB subroutine for convenient calls.
•    Complete related subroutines, including USDFLD for curing kinetics, UEXPAN for
     effective strain, UMAT for stress update and HETVAL for heat transfer analysis, thereby
     building a fully automated computational workflow.




    Figure 1. Connection among the material models in ABAQUS and the trained LSTM model.
Objective 3: Enable controlled exotherm-driven curing temperature optimization
     The cure exotherm of thermoset resins in carbon fiber-reinforced polymers (CFRPs) has
long been regarded as an issue because it leads to internal temperature inhomogeneity and
overheating. However, by leveraging the principles of frontal curing (FC), this exothermic heat
can be converted into a supplementary heat source. This approach enables dynamic
combination of external heating with exotherm release, effectively mitigating overheating,
reducing overall energy consumption.
Critical Elements:
•   Establish a closed-loop "prediction-feedback " system for external heating, using the
    LSTM-FEA network to forecast exotherm in advance. Besides, a PID control algorithm
    will be utilized to adjust the power of heating devices, enabling identification of potential
    overheating risks.


2. Potential Impact

    The research enriches academic theory and methodology in two key areas:
•   It innovatively breaks down the working environment barriers between AI tools and finite
    element software, truly achieving seamless interaction and iterative calculation between
    AI neural networks and FEA models. It will greatly increase the computational speed while
    maintaining the modeling accuracy for chemo-thermo-mechanical coupled analysis of the
    composite with complex multiscale features and strong non-linear responses.
•   The "prediction-feedback" system expands the application scope of frontal curing and PID-
    based thermal control in materials engineering.
1. Background
     Carbon fiber reinforced polymers (CFRPs) are widely used in aerospace, automotive, and
high-performance engineering for its exceptional specific strength and design adaptability [1].
As demonstrated in Figure 1(a), the global transition of aircraft with composite architecture is
estimated to contribute 15%–20% of industry CO2 reduction targets by 2050 [2]. Nevertheless,
the curing process of thermoset-based CFRP laminates often induces significant residual stress
due to the material’s inherent heterogeneity and anisotropy. These stresses raised from
mismatches of thermal expansion and chemical shrinkage will cause process-induced
deformation (PID) such as warpage or distortion [3-6], as shown in Figure 1(b), impairing
assembly precision, geometric integrity, and even structural reliability.




Figure 1. (a) Materials used in a modern aircraft, the Airbus A350 XWB (b)Schematic of a
typical wing-skin assembly

     Growing demand for lightweight composites promotes adoption of CFRPs with complex
geometry and non-uniform features. While finite element method (FEM) modeling their
mechanical behavior remains computationally consuming, particularly for curing simulations
involving coupled chemical-thermal-mechanical phenomena and nonlinear constitutive
behaviors [7].
     Researchers advances machine learning (ML) models predict residual stresses, reducing
reliance on computationally consuming FEA models. [8, 9]. While most AI approaches use
macroscopic data for training, which limits generalizability to varying geometries or boundaries.
Additionally, AI models and FE solvers operate in isolated environments, which prevents
seamless interaction and limits the efficiency and robustness of prediction workflows.
     On the other hand, a reduced-order method self-consistent clustering analysis (SCA) was
proposed to bridge scales, allowing generalization of geometry [10, 11]. While its application
to curing analysis remains limited due to the high sensitivity of convergence and efficiency to
diverse parameters involved in curing process.

2. Cross-Scale LSTM-FEA Integrated Modeling Framework

     Direct integration of the LSTM model into ABAQUS for FEA is challenging, as the FEA
solvers such as the ABAQUS Standard and Explicit ones need to call numerical functions
written in Fortran, while the LSTM network commonly relies on Python or MATLAB deep
learning frameworks such as TensorFlow and PyTorch. These distinct operating environments
are isolated from each other, preventing seamless interaction without additional time for data
processing and transferring.
      To tackle this problem, the LSTM model trained in MATLAB will be decomposed into
mathematical formulations listed in Eqs. (1) to (6) and corresponding parameters, being
translated into Fortran and then embedded into the user-defined subroutines for computation in
ABAQUS. Then, ABAQUS would provide temperature, DOC, yarn angle and time from FEA
calculation at each integration point and each time step to the LSTM model to determine the
corresponding stiffness matrix components for stress update.
                                 𝑓𝑓𝑡𝑡 = 𝜇𝜇�𝑊𝑊𝑓𝑓 ∙ (ℎ𝑡𝑡−1 , 𝑥𝑥𝑡𝑡 ) + 𝑏𝑏𝑓𝑓 �                (1)

                                 𝑖𝑖𝑡𝑡 = 𝜇𝜇[𝑊𝑊𝑖𝑖 ∙ (ℎ𝑡𝑡−1 , 𝑥𝑥𝑡𝑡 ) + 𝑏𝑏𝑖𝑖 ]                (2)

                              𝐶𝐶�𝑡𝑡 = tanh[𝑊𝑊𝐶𝐶 ∙ (ℎ𝑡𝑡−1 , 𝑥𝑥𝑡𝑡 ) + 𝑏𝑏𝐶𝐶 ]                (3)

                                    𝐶𝐶𝑡𝑡 = 𝑓𝑓𝑡𝑡 ∗ 𝐶𝐶𝑡𝑡−1 + 𝑖𝑖𝑡𝑡 ∗ 𝐶𝐶�𝑡𝑡                   (4)
                                𝑜𝑜𝑡𝑡 = 𝜇𝜇[𝑊𝑊𝑜𝑜 ∙ (ℎ𝑡𝑡−1 , 𝑥𝑥𝑡𝑡 ) + 𝑏𝑏𝑜𝑜 ]                 (5)

                                       ℎ𝑡𝑡 = 𝑜𝑜𝑡𝑡 ∗ tanh(𝐶𝐶𝑡𝑡 )                           (6)

     The LSTM model finished in previous work was trained in a TensorFlow environment
using datasets from the FEM simulation [12]. The parameters for configurating the LSTM
model are documented in Table 1, which were determined via attempting various metrics and
adjusting them to obtain best prediction accuracy for the stiffness tensor.

            Table 1. Summary of configuration parameters of the LSTM model.
        Item                     Value          Item                   Value
        Batch size               6              Number of hidden sizes 200
        Learning rate            0.01           Dropout rate           0.1
        Learning rate decay      0.001          Loss function          MAE
        Input size               1              Optimizer              Adam
        Number of LSTM layers 3                 Epoch                  230

     As illustrated in Figure 2, the loss function initially declines significantly and then
gradually converges after 30 epochs. However, to make the model adequately robust, the epoch
was set to 230. The final loss on the training and testing datasets were 0.0051 and 0.00322,
respectively, and R2 after training reaches 99.5%.
                  Figure 2. Training and validation loss of the LSTM model.


3. LSTM-FEA integration
     To achieve specific implementation of LSTM-FEA integration for chemo-thermo-
mechanical coupled curing analysis, the UEXTERNALDB subroutine is first used to import
weights and bias of each gate from the trained LSTM model. These parameters are stored for
calculation across all subsequent analysis steps in the simulation.
     At each integration point and time increment, the known curing temperature profile and
thermal conductivity allow deriving the degree of cure (DOC) and curing heat of the composite
via the curing kinetics model described by the USDFLD subroutine. Correspondingly, the
UEXPAN subroutine yields the temperature-dependent coefficient of thermal expansion (CTE)
and DOC-dependent chemical shrinkage coefficient (CSC). These values enable calculating the
effective strain of the current time step, which serves as input to the viscoelastic constitutive
equation.
     Simultaneously, using the stored data from UEXTERNALDB, the trained LSTM model’s
parameters and functions calculate the anisotropic stress-relaxation stiffness matrix of the
preformed CFRP for the current step. This calculation uses known DOC, yarn angle,
temperature and step time. Finally, combining viscoelasticity and effective strain in the UMAT,
the stress could be updated at the end. Figure 3 demonstrates the connection between all
subroutines in each step. This combination of subroutines is iterated continuously throughout
the entire part and curing analysis.




      Figure 3. Connection among the subroutines in ABAQUS and the trained LSTM.
4. Exotherm-driven curing temperature optimization

      Exotherm released from thermoset resin in CFRPs often cause internal temperature
inhomogeneity and localized overheating. Although conventionally regarded as an issue,
exothermic heat can instead be applied as a supplementary heat source. The temperature
distribution of composites and their resins during the curing process can be expressed by
Fourier's heat transfer [13, 14] represented in Eqs. (7) and (8).
                                               𝜙𝜙 = 𝛼𝛼 ⋅ 𝐻𝐻𝑡𝑡                                             (7)
                                       𝜕𝜕𝜕𝜕   𝜕𝜕       𝜕𝜕𝜕𝜕
                                      𝜌𝜌𝜌𝜌  =       �𝑘𝑘 � + 𝜙𝜙̇                             (8)
                                        𝜕𝜕𝜕𝜕 𝜕𝜕𝑥𝑥𝑖𝑖     𝑥𝑥𝑖𝑖
where 𝐻𝐻𝑡𝑡 represents the total heat consumption during the curing process, 𝛼𝛼 refers to the
degree of cure, 𝜌𝜌 is the mass density, 𝐶𝐶 the specific heat capacity, 𝑘𝑘 is the thermal
conductivity, and 𝜙𝜙̇ is the curing exothermic rate.
      To address the combination of reaction exotherm and external heat source, a "prediction-
feedback" system will be developed. The pre-established LSTM-FEA cross-scale modeling
system will be utilized to forecast exotherm trends, including peak intensity and duration,
enabling advance identification of potential overheating risks. These predictions are then feed
into a proportional-integral-derivative (PID) control algorithm to adjust heating device power
in real time. The operation flowchart is illustrated in Figure 4. Successful implementation of
this system will enhance curing process stability and lower manufacturing costs.




            Figure 4. The flowchart of the "prediction-feedback-regulation" system


Reference
1.     Xu, P., et al., Phase structure characterization and compatibilization mechanism of epoxy asphalt
       modified by thermoplastic elastomer (SBS). Construction and Building Materials, 2022. 320: p.
       126262.
2.     Bachmann, J., C. Hidalgo, and S. Bricout, Environmental analysis of innovative sustainable
       composites with potential use in aviation sector—A life cycle assessment review. Science China
       Technological Sciences, 2017. 60(9): p. 1301-1317.
3.     Parveez, B., et al., Scientific advancements in composite materials for aircraft applications: a review.
       Polymers, 2022. 14(22): p. 5007.
4.    Struzziero, G., et al., Cure-induced residual stresses for warpage reduction in thermoset laminates.
      Journal of Composite Materials, 2020. 54(22): p. 3055-3065.
5.    Liu, X., et al., Study on cure-induced residual stresses and spring-in deformation of L-shaped
      composite laminates using a simplified constitutive model considering stress relaxation. Composite
      Structures, 2021. 272: p. 114203.
6.    Deng, Y., et al., Spatio-temporal Prediction of Curing-induced Deformation for Composite
      Structures Using a Hybrid CNN-LSTM and Finite Element Approach. Composites Science and
      Technology, 2025: p. 111225.
7.    Rahali, Y., I. Goda, and J.-F. Ganghoffer, Numerical identification of classical and nonclassical
      moduli of 3D woven textiles and analysis of scale effects. Composite Structures, 2016. 135: p. 122-
      139.
8.    Kim, D.-H., S.-W. Kim, and I. Lee, Evaluation of curing process-induced deformation in plain woven
      composite structures based on cure kinetics considering various fabric parameters. Composite
      Structures, 2022. 287: p. 115379.
9.    Suraj, K., et al., Uncertainty quantification of bistable variable stiffness laminate using machine
      learning assisted perturbation approach. Composite Structures, 2023. 319: p. 117072.
10.   Liu, Z., M. Bessa, and W.K. Liu, Self-consistent clustering analysis: an efficient multi-scale scheme
      for inelastic heterogeneous materials. Computer Methods in Applied Mechanics and Engineering,
      2016. 306: p. 319-341.
11.   He, C., et al., From microscale to mesoscale: The non-linear behavior prediction of 3D braided
      composites based on the SCA2 concurrent multiscale simulation. Composites Science and
      Technology, 2021. 213: p. 108947.
12.   Feng, Y., et al., Multiscale modeling for viscoelasticity of woven CFRP considering preforming and
      curing effects via finite element and long-short term memory analysis. Composites Part A: Applied
      Science and Manufacturing, 2024. 186: p. 108397.
13.   Fisher, A., A. Levy, and J. Kratz, Effects of heat transfer coefficient variations on composite curing.
      Journal of Composite Materials, 2023. 57(3): p. 363-376.
14.   Wang, T., et al., Fundamentals of infrared heating and their application in thermosetting polymer
      curing: a review. Coatings, 2024. 14(7): p. 875.
     Figure 1 demonstrates the importance of Carbon fiber reinforced polymers (CFRPs) in
aerospace applications and addresses the main challenge hinders efficient mass production.
     Conventional FEM approaches can no longer meet the simulation requirements of CFRPs
with complex geometry and non-uniform features.




Figure 1. (a) Materials used in a modern aircraft, the Airbus A350 XWB (b)Schematic of a
typical wing-skin assembly




     Therefore, we need to seek assistance from AI tools. Figure 2 illustrates the training
performance of the LSTM model finished in previous work trained in MATLAB, with R2
reaching 99.5%. This LSTM network takes temperature, DOC, yarn angle and time as input
and relaxation tensor components as outputs.




                 Figure 2. Training and validation loss of the LSTM model.




      Existing AI driven approaches cannot meet great generalizability to varying geometries or
boundaries and stable convergence and efficiency to diverse parameters simultaneously. We
innovatively proposed a cross-scale LSTM FEA modeling system to tackle this issue, following
the flowchart shown in Figure 3.
Figure 3. Connection among the subroutines in ABAQUS and the trained LSTM.
    The research proposed focuses on developing a cross-scale LSTM-FEA model for process-
induced deformation (PID) prediction and an exotherm-driven curing optimization system for
woven carbon fiber-reinforced polymers (CFRPs). It directly addresses critical non-academic
needs of reducing manufacturing costs and carbon emissions, improving product reliability, and
advancing lightweight industries.
    This statement outlines pathways on the economy, environment, and society respectively.

1. Pathways to Economic Impact
      CFRPs are pivotal for its excellent strength-to-weight ratio, but its applications are
restricted by high production costs on CID-related scrap. This research will drive economic
value through three actionable pathways:
•    This research breaks through the technical bottlenecks of traditional CID predictions. By
     embedding LSTM into FEA models, the research realizes seamless chemo-thermo-
     mechanical coupling modeling of the composite with complex multiscale features and
     strong non-linear responses, achieving 3 to 5 orders of magnitude faster computation than
     traditional FEA methods.
•    For automotive OEMs, aircraft component suppliers and CFRP fabricators, the LSTM-
     FEA model reduces physical trial times and scrap rate significantly for real-world complex
     CFRP part, greatly reducing production costs
•    The LSTM-FEA integrated modeling solution can be strategically packaged as a licensable
     toolkit for SMEs, offering a transformative approach that significantly accelerates process
     design cycles while ensuring smooth technological adoption through intuitive
     implementation and robust technical support.
•    By reasonable use of curing exotherm, energy costs can be further reduced. It supports the
     design of low-carbon production lines, contributing to Industry 4.0’s "green
     manufacturing" transformation.

2. Pathways to Environmental Impact
The research directly contributes to carbon neutrality and resource efficiency through two core
strategies:
•    By converting curing exotherm into a supplementary heat source, external heating demand
     for CFRP curing can be reduced effectively, especially for partners producing thick-walled
     CFRP components.
•    Carbon fiber is a resource with high embodied carbon. The LSTM-FEA model’s precise
     CID prediction reduces scrap, lowering demand for raw materials and reducing the
     environmental footprint of CFRP manufacturing.

3. Pathways to Societal Impact
•   Facilitated with the cross-scale LSTM-FEA model, CFRP structure can be mass produced
    at a much lower cost. Wide applications of lightweight CFRPs help reduce vehicle weight,
    improve EV range and lower fuel consumption for internal combustion engine vehicles.
•   For automotive OEMs, the LSTM-FEA model can be utilized in the design of EV CFRP
    structures. This will accelerate the adoption of lightweight EVs, making sustainable
    transportation more accessible and reducing "range anxiety" for consumers.
•   The LSTM-FEA model’s ability to predict residual stress will enable manufacturers to
    design more durable parts, reducing the risk of in-service failure and enhancing passenger
    safety.
Reference:
1. Xu, P., et al., Phase structure characterization and compatibilization mechanism of epoxy
asphalt modified by thermoplastic elastomer (SBS). Construction and Building Materials, 2022.
320: p. 126262.
2. Bachmann, J., C. Hidalgo, and S. Bricout, Environmental analysis of innovative sustainable
composites with potential use in aviation sector—A life cycle assessment review. Science China
Technological Sciences, 2017. 60(9): p. 1301-1317.
3. Parveez, B., et al., Scientific advancements in composite materials for aircraft applications:
a review. Polymers, 2022. 14(22): p. 5007.
4. Struzziero, G., et al., Cure-induced residual stresses for warpage reduction in thermoset
laminates. Journal of Composite Materials, 2020. 54(22): p. 3055-3065.
5. Liu, X., et al., Study on cure-induced residual stresses and spring-in deformation of L-
shaped composite laminates using a simplified constitutive model considering stress relaxation.
Composite Structures, 2021. 272: p. 114203.
6. Deng, Y., et al., Spatio-temporal Prediction of Curing-induced Deformation for Composite
Structures Using a Hybrid CNN-LSTM and Finite Element Approach. Composites Science and
Technology, 2025: p. 111225.
7. Rahali, Y., I. Goda, and J.-F. Ganghoffer, Numerical identification of classical and
nonclassical moduli of 3D woven textiles and analysis of scale effects. Composite Structures,
2016. 135: p. 122-139.
8. Kim, D.-H., S.-W. Kim, and I. Lee, Evaluation of curing process-induced deformation in
plain woven composite structures based on cure kinetics considering various fabric parameters.
Composite Structures, 2022. 287: p. 115379.
9. Suraj, K., et al., Uncertainty quantification of bistable variable stiffness laminate using
machine learning assisted perturbation approach. Composite Structures, 2023. 319: p. 117072.
10. Liu, Z., M. Bessa, and W.K. Liu, Self-consistent clustering analysis: an efficient multi-scale
scheme for inelastic heterogeneous materials. Computer Methods in Applied Mechanics and
Engineering, 2016. 306: p. 319-341.
11. He, C., et al., From microscale to mesoscale: The non-linear behavior prediction of 3D
braided composites based on the SCA2 concurrent multiscale simulation. Composites Science
and Technology, 2021. 213: p. 108947.
12. Feng, Y., et al., Multiscale modeling for viscoelasticity of woven CFRP considering
preforming and curing effects via finite element and long-short term memory analysis.
Composites Part A: Applied Science and Manufacturing, 2024. 186: p. 108397.
13. Fisher, A., A. Levy, and J. Kratz, Effects of heat transfer coefficient variations on composite
curing. Journal of Composite Materials, 2023. 57(3): p. 363-376.
14. Wang, T., et al., Fundamentals of infrared heating and their application in thermosetting
polymer curing: a review. Coatings, 2024. 14(7): p. 875.
Project 1:
Multiscale Modeling and Homogenization for Thermal-Mechanical Properties of Carbon
Fiber Reinforced Thermoset Polymers During Curing Process
Time Period: November 2022 – October 2024
     This project aims to develop a systematic multiscale simulation framework for an aircraft-
grade thermoset prepreg. This framework enables efficient and high-precision prediction of
curing deformation in composite laminates, an essential capability for ensuring the structural
integrity, dimensional stability, and performance reliability of aerospace composite components.
     To address the difficulty of directly characterizing the curing parameters of aircraft-grade
high viscosity toughened resins, an innovative non-contact indirect characterization method
was developed. Based on the secondary development of ABAQUS user subroutines, a thermo-
chemo-mechanical multi-field coupled constitutive model was established to achieve accurate
prediction of curing deformation for toughened resin-based unidirectional composite laminates.

Key participation in research project:
•   Conducted experimental characterization of the chemical, thermal and mechanical
    properties of the toughened resin and its composite.
•   Built a chemo-thermo-mechanical multi-field coupled constitutive model was built, and
    user-defined subroutines for ABAQUS were written using Fortran.
•   Carried out curing deformation simulation and experimental verification of three-
    dimensional components.
•   Developed an ABAQUS Plugin module with Python, which can automatically modify
    material subroutines and create new geometric models according to customer-defined
    information. The Plugin was delivered to the project funder to facilitate pre-simulation
    prediction when adjusting material and process parameters in process design.


Project 2:
Multiscale modeling for viscoelasticity of woven CFRP considering preforming and curing
effects via finite element and long-short term memory analysis
Time Period: December 2023 – June 2024
      This project has laid the foundation for the LSTM neural network required for the proposed
project. It addressed the challenge of predicting the viscoelastic behavior of preformed woven
CFRPs during curing, proposing a multiscale intelligent modeling framework. The simulation
results of the validated mesoscale representative volume element (RVE) finite element model
were used as training data to train and test the established LSTM network. For the
viscoelasticity prediction of woven CFRP specimens, this model achieved an average error of
less than 6.62%.

Key participation in research project:
•   Conducted validation experiments for the RVE model before generating training data.
•   Develop a Python script that can automatically generate custom subroutines for responsive
    curing simulation based on the geometric model after preforming simulation, non-uniform
    angle distribution, and curing process parameters.


Project 3:
Modeling of environmental effects on performance degradation of offshore wind turbine
blades made by carbon fiber reinforced plastics (CFRPs)
Time Period: September 2021 – December 2022
     This project aims to develop a cutting-edge MD-FEA integrated modeling method to
simulate performance degradation of CFRP wind turbine blades with offshore environmental
effects included. Upon completion, a numerical tool will be established to aid material selection
and new material design for CFRPs that are to be applied under marine or other harsh conditions.
The underlying principles about electrochemistry erosion at CFRP-metal interfaces can also be
obtained to guide construction of CFRP part joints and selection of component materials for
water-involved applications.

Key participation in research project:
•   Conducted characterization experiments (including axial tension, compression, and shear
    tests) on composites to measure the material's elastoplastic curves and strength.
•   Developed MATLAB code for the automatic generation of INP files for RVE models of
    unidirectional composites with random fiber distribution, including the geometry and
    meshing of the resin, interface, and fiber, as well as the assignment of material properties
    and incorporation of 3D nonlinear periodic boundary conditions.


Project 4:
R&D and Industrialization of High-Air-Permeability Multifunctional Nanofiber Air
Filters
Time Period: August 2017 – August 2021
     This project was an incubation program at Hong Kong Science Park, aiming to develop a
multifunctional nanofiber air filter using electrospinning technology. The filter was designed to
ensure high air permeability while achieving efficient PM1.0 filtration, odor removal, and
effective bactericidal performance. As a member of the founding team, I was responsible for
technology R&D.

Key participation in research project:
•   During the initial R&D phase, I led experiments and testing, ultimately finalizing
    formulations and SOPs for several product versions.
•   In the technology transformation phase, I oversaw the setup of mass production lines,
    including arranging raw material preprocessing and post-treatment, and adjusting
    formulations based on the specific characteristics of mass production equipment.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee:        (Surname) LIU
                        (Other name) Meiyu
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?


     I have collaborated closely with Meiyu and her PhD supervisor in the past years, mainly
on curing modeling of thermoset CFRPs considering the influence of prepreg preforming.
Through this long-standing partnership, I have gained a clear understanding of Meiyu’s
research ability and technical proficiency, including insights into her research work during her
PhD study.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent    Very Good        Good              Average          No basis
                           (top 5%)     (top 6-20%) (top 21-50%)          or below            for
                                                                         (lower than       judgment
                                                                            50%)
Intellectual capacity                      ☐               ☐                 ☐                ☐
                                             ☐              ☐                 ☐                ☐
Ability for conducting
scholastic research
                               
                                             ☐              ☐                 ☐                ☐
Knowledge of proposed
research study
                               

                                             ☐              ☐                 ☐                ☐
Analytical power and
reasoning
                               
Judgment                                    ☐              ☐                 ☐                ☐
                                             ☐              ☐                 ☐                ☐
Imagination and
originality
                               

                                             ☐              ☐                 ☐                ☐
Motivation and
perseverance
                               

                                                            ☐                 ☐                ☐
Skills of writing and
argumentation
                                           ☐
                                             ☐              ☐                 ☐                ☐
Capacity for independent
work
                               

                                             ☐              ☐                 ☐                ☐
Reliability and sense of
responsibility
                               
Leadership experience                      ☐               ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
      Meiyu demonstrates strong proficiency in characterization techniques specific to resin-
based carbon fiber composites. She previously developed a coupled curing simulation
framework that effectively captures the interconnected chemical, thermal and mechanical
phenomena of thermoset CFRPs after preforming. She has also advanced her expertise in
secondary development of simulation software for user-defined material responses.
      The project she proposes directly tackles a longstanding CFRP manufacturing bottleneck:
the trade-off between high-fidelity mechanics analysis and computation efficiency. Notably, it
bridges theoretical modeling with engineering practice, ensuring that the research insights can
deliver real-world manufacturing value.
      Meiyu possesses the research rigor, technical depth and impact-focused mindset required
to conduct this project. I have no doubt that she will make significant contribution to the field
and uphold the high standards of the JRFS program. I strongly recommend her application
without reservation.
                                                 3
                                                         JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: LIANG BIAO                                      Title: Professor
                         (in BLOCK letters)                                (Prof)*

 University / Organization: Northwestern Polytechnical University, China
 Position: Professor
 E-mail Address: <EMAIL>
 Telephone Number#: 86- 18792684894
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date:           2025.09.01




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
