                                                         Jun Huo
                                  +86-13516041015 | <EMAIL> | <EMAIL>
                                                    Wuhan, Hubei Province, China

R ESEARCH I NTERESTS
My current research focus on human-centered wearable rehabilitation assistive robot, including supernumerary
robotic limb (SRL), exoskeletons and other bio-inspired robots. My research lies in the combination of mechanism
design, optimization, motion planning, and learning-based control.

E DUCATION
•   Huazhong University of Science and Technology                                                                     Sept. 2021 - Present
    PhD Degree, Advisor: <PERSON><PERSON>, China
    ◦   Major: Control Science and Engineering
•   Huazhong University of Science and Technology                                                                   Sept. 2018 - June 2021
    Master Degree, Advisor: <PERSON><PERSON>, China
    ◦   Major: Control Science and Engineering
•   Northeastern University                                                                                         Sept. 2014 - June 2018
    Bachelor Degree, Advisor: <PERSON><PERSON><PERSON><PERSON>, China
    ◦   Major: Mechanical Engineering

P ROJECTS
•   Basic research on supernumerary robotic limb for hemiplegia motion assistance                                    Jan. 2020 - Dec. 2023
    Major Participator, National Natural Science Foundation of China (U1913207)
    ◦   To address the challenge of human–robot collaboration in anthropomorphic, multifunctional supernumerary
        robotic limbs (SRLs), a design theory based on ellipsoidal workspace similarity is proposed. Guided by this theory,
        a novel SRL capable of both upper and lower limb assistance is developed. Additionally, a multi-subpopulation
        correction firefly algorithm is introduced to solve high-dimensional, irregular Pareto front optimization problems.
    ◦   A soft six-axis force/torque sensor based on multiple air chambers is developed to enhance flexible force sensing
        and motion intent recognition during human–robot interaction, aiming to overcome the difficulty of accurately
        recognizing user intent.
    ◦   To tackle the uncertainty caused by internal and external disturbances in SRL-assisted walking tasks, an adaptive
        variable impedance switching control algorithm is proposed. This approach integrates an impedance parameter
        generation network to ensure real-time stability and robust performance.
    ◦   To reduce the latency resulting from the loose coupling between humans and SRLs during sit-to-stand (STS)
        transitions, an EMG-driven variable stiffness optimization control algorithm is proposed. This method incorporates
        incremental learning to enable rapid tuning of impedance parameters.
•   Collaborative Research on Key Technologies for Intelligent Walking Assistive Robots                             Sept. 2018 - Dec. 2022
    Participator, the International Science and Technology Cooperation Program of China (2017YFE0128300)
    ◦   To address the lack of sufficient walking assistance for individuals with mild to moderate lower-limb muscle
        weakness, an electrically suspended weight support system was designed to provide stable and consistent force
        output through precise constant-force control.
    ◦   To overcome the significant force sensing delay in walking-assist robotic systems, a passive conductive control
        algorithm based on a system state observer is proposed. The stability of the control strategy is theoretically
        validated using the small-gain theorem.



P UBLICATIONS AND PATENTS                                              C=C ONFERENCE , J=J OURNAL , P=PATENT, S=I N S UBMISSION , T=T HESIS
                                   ∗
[J.1]       Jun Huo, Jian Huang , Jie Zuo, Bo Yang, Zhongzheng Fu, Xi Li, and Samer Mohammed. Innovative Design of
            Multi-functional Supernumerary Robotic Limbs with Ellipsoid Workspace Optimization. IEEE
            Transactions on Robotics, vol. 41, pp. 4699-4718, 2025.
[J.2]       Jun Huo, Kehan Xu, Chengyao Li, Yu Cao, Jie Zuo, Xinxing Chen and Jian Huang∗ . Variable Impedance
            Control for Floating-Base Supernumerary Robotic Leg in Walking Assistance. IEEE Robotics and
            Automation Letters, vol. 10, no. 9, pp. 8698-8705, Sept. 2025.
[J.3]       Jun Huo, Hongge Ru, Bo Yang, Xingjian Chen, Xi Li and Jian Huang∗ . Air-Chamber-Based Soft Six-Axis
            Force/Torque Sensor for Human–Robot Interaction. IEEE Transactions on Instrumentation and
            Measurement, vol. 73, pp. 1-12, 2024, Art no. 9501612.
[J.4]       Jun Huo, Jian Huang∗ , Xikai Tu and Zhongzheng Fu. Force sensorless admittance control of body weight
            support system. Advanced Robotics, 35:7, 425-436, 2021.
[J.5]       Jie Zuo# , Jun Huo# , Xiling Xiao, Yanzhao Zhang and Jian Huang∗ . Human-robot Coordination Control for
            Sit-to-Stand Assistance in Hemiparetic Patients with Supernumerary Robotic Leg. IEEE Transactions on
            Automation Science and Engineering, vol. 22, pp. 16591-16602, 2025.
[J.6]       Zhongzheng Fu, Xinrun He, Enkai Wang, Jun Huo, Jian Huang∗ and Dongrui Wu. Personalized Human
            Activity Recognition Based on Integrated Wearable Sensor and Transfer Learning. Sensors. 2021, 21(3):885.
[S.1]       Kehan Xu, Jun Huo, Yize Zheng, Zixin Chi, Yu Cao, Zhaohui Yang and Jian Huang∗ . Adaptive Gait
            Assistance for Foot Drop Rehabilitation Based on Uncertainty Fusion of Contra-lateral Limb Information.
            IEEE Transactions on Neural Systems and Rehabilitation Engineering (In Submission).
[S.2]       Bo Yang, Zejia Zhang, Yu Cao, Jun Huo, Jie Zuo, Xiling Xiao, Wei Luo and Jian Huang∗ . Gaze Foot Hybrid
            Upper Limb Assistive Robotic System: An Innovative Approach to Aid Individuals with Limb
            Impairments. IEEE Transactions on Biomedical Engineering (Major Revision).
[C.1]       Jun Huo, Xinyi Wang, Bo Yang, Qingyang Yan, Zhaofan Yuan, and Jian Huang∗ . Active Compliance Variable
            Impedance Control of Lower Supernumerary Robotic Limb. 2024 International Conference on Advanced
            Robotics and Mechatronics (ICARM), Tokyo, Japan, 2024, pp. 19-24.
[C.2]       Jun Huo, Bo Yang, Hongge Ru, and Jian Huang∗ . Parametric Design Optimization of a Universal
            Supernumerary Robotic Limb. The 32nd 2021 International Symposium on Micro-NanoMechatronics and
            Human Science, Nagoya, Japan, 2021, pp. 1-6.
[C.3]       Zhiguo Lu∗ , Jun Huo, Yuce Wang, Tongle Xin and Zhengbo Xie. Design and simulation analysis of a lower
            limbs exoskeleton powered by hydraulic drive. 2017 2nd International Conference on Advanced Robotics
            and Mechatronics (ICARM), Hefei and Tai’an, China, 2017, pp. 173-177.
[C.4]       Bo Yang, Jian Huang∗ , Menglin Sun, Jun Huo, Xiaolong Li and Caihua Xiong. Head-free, Human Gaze-driven
            Assistive Robotic System for Reaching and Grasping. 2021 40th Chinese Control Conference (CCC),
            Shanghai, China, 2021, pp. 4138-4143.
[P.1]       Jian Huang, Jun Huo, Mengshi Zhang, Caihua Xiong, Xiling Xiao. (2021). A human motion-assisted dual-use
            supernumerary robotic limb. Chinese invention patent, Patent No. CN201910611936.0.
[P.2]       Jian Huang, Jun Huo, Xikai Tu, Hongge Ru. (2021). A kind of dynamic suspension weight support system.
            Chinese invention patent, Patent No. CN201910694048.X
[P.3]       Jian Huang, Jun Huo, Bo Yang, Hongge Ru. (2022). Parameter optimization method for supernumerary
            robotic limb based on workspace similarity. Chinese invention patent, Patent No. CN202110609611.6
[P.4]       Jian Huang, Hongge Ru, Jun Huo, Xikai Tu, Xiao Li. (2022). A pneumatic driven variable stiffness
            omnidirectional bending drive device. Chinese invention patent, Patent No. CN202110423767.5

H ONORS AND AWARDS
•   Best Paper Finalist Award                                                                                  July 2024
    2024 International Conference on Advanced Robotics and Mechatronics (ICARM)
    ◦   Jun Huo, Xinyi Wang, Bo Yang, Qingyang Yan, Zhaofan Yuan, and Jian Huang∗ . Active Compliance Variable
        Impedance Control of Lower Supernumerary Robotic Limb.
•   The Sixth China Post-Graduate Student Robot Innovation Design Competition, National Second Prize Oct. 2024
    China Association for Science and Technology
    ◦   Jun Huo, Haoyuan Wang, Zhongzheng Fu, Xingjian Chen, Chengyao Li. A universal supernumerary robotic limb
        for motion function assistance
•   The Third China Post-Graduate Student Robot Innovation Design Competition, National Second Prize Aug. 2021
    China Association for Science and Technology
    ◦   Hongge Ru, Runzhe Zhang, Jun Huo, Cheng Chen, Mengshi Zhang. Pneumatic flexible hand for grip assistance in
        hemiplegic patients



S KILLS
• Programming Languages: Python, Matlab
• Control System: Matlab/Simulink, Labview, ROS
• 3D Modeling: SolidWorks, ProE
R EFERENCES
1. Jian Huang
   Professor, School of Artificial Intelligence and Automation
   Huazhong University Of Science And Technology
   Email: <EMAIL>
   Phone: +86-13627206071
   Relationship: [PhD Advisor]
2. Samer Mohammed
   Professor, Laboratoire Images, Signaux et Systèmes Intelligents
   Univ Paris-Est Créteil, LISSI, F-94400, Vitry, France
   Email: <EMAIL>
   Phone: +33 (0)1 41 80 73 18
   Relationship: [Co-author]
3. Yu Cao
   UKRI Research Fellow, School of Electronic & Electrical Engineering
   University of Leeds, UK
   Email: <EMAIL>
   Phone: +44(0)**********
   Relationship: [Co-author]
                                             Abstract
     This research proposes a groundbreaking multi-robot system designed to address the key
challenge of multi-robot manipulation with confined spaces in minimally invasive surgery (MIS).
We will develop a novel hybrid actuation system that strategically combines magnetic guidance
for large-scale navigation with tendon-driven actuation for high-precision manipulation. To sup-
port this design, we will create a unified kinematics and dynamics model based on Cosserat rod
theory, which, for the first time, will accurately capture the coupled effects of both actuation
mechanisms, variable stiffness, and external disturbances. Building on this, we will design a
sophisticated control framework that integrates a Neural Network-based Model Predictive Con-
trol (NN-MPC) strategy with a Learning-Based Collaborative Control scheme. This framework
will not only ensure high-precision trajectory tracking and robust performance by actively com-
pensating for modeling errors and disturbances, but will also enable seamless, autonomous col-
laboration between the two robots. The project culminates in a comprehensive validation phase
within a simulated surgical environment to confirm the system’s collaborative precision, adap-
tive performance, and operational safety, demonstrating its potential as a reliable and effective
tool for future clinical applications.




                                               1
               Project Objectives and Potential Impact of Research Project
   The objective of this research is to design and develop a high-performance soft continuum
robot system, focusing on addressing the following issues:

   1. Hybrid Actuation System for Multiple Flexible Robots: Our primary goal is to develop a
      novel hybrid actuation mechanism tailored for multiple flexible robots in confined space
      surgery. This system will merge the strengths of magnetic guidance and tendon-driven
      actuation to control two distinct groups of flexible robots.
      The first group, acting as the main body, will use magnetic guidance for simplified path
      planning and large-scale movement, allowing it to navigate efficiently to the target area.
      The second group, coiled around the main body, will then be deployed and controlled
      by tendon-driven actuation for fine-grained, precise manipulations once the main body
      reached the designed position.
      This approach capitalizes on the simplicity of magnetic guidance for initial navigation
      along a predetermined path while overcoming its inherent difficulty with precise opera-
      tions in small and constrained spaces. The result is a system that combines the best of
      both: efficient, large-scale movement with the high-precision control necessary for deli-
      cate surgical tasks.

   2. Hybrid Magnetic-Cosserat Rod Model for Precise Modeling: This objective focuses on
      developing a complex and unified hybrid motion model that combines magnetic force
      with Cosserat rod theory. Inspired by the principle of antagonistic muscle pairs, the
      model will leverage the opposing forces of magnetic fields and tendon tension to achieve
      variable stiffness. This will allow the robot to adapt its rigidity to different scenarios
      within the human body, from flexible navigation to stiff, precise manipulation.
      The research will address the significant challenge of accurately modeling the complex
      kinematics and dynamics resulting from the integrated magnetic forces. This requires
      moving beyond traditional static or kinematic models to create a highly accurate and ef-
      ficient framework that is robust to environmental changes and internal variations.

   3. Real-Time Control and Collaborative Strategy for Multiple Robots: This objective fo-
      cuses on developing advanced real-time control algorithms to manage the inherent chal-
      lenges of the multi-robot system. The primary goal is twofold:
      Handling Modeling Errors and Disturbances: We will design a robust control strategy
      that actively compensates for both external disturbances and the significant modeling er-
      rors arising from the complex hybrid magnetic-Cosserat rod model. This approach will
      ensure the system maintains precise trajectory tracking and motion control, even in dy-
      namic and unpredictable environments.
      Developing a Collaborative Control Strategy: We will create a unique collaborative
      control strategy for the two groups of flexible robots. This strategy will enable the magnetic-
      guided main body to efficiently perform path planning and large-scale navigation, while
      the tendon-driven robots execute fine-grained, precise manipulations. The controller will
      ensure seamless coordination and stability between the two groups, allowing for complex,
      cooperative tasks within confined surgical spaces.

   4. Validate System Performance in Minimally Invasive Medical Surgery: The final objective
      is to demonstrate the practical viability of the developed robotic system. This involves
      validating its performance in a highly relevant and challenging application: minimally

                                                1
invasive medical surgery. The research will evaluate the system’s key performance met-
rics, such as its positioning accuracy, adaptability to constrained surgical spaces, and
safety features. This validation process will involve creating realistic surgical scenarios to
rigorously test the robot’s capabilities and confirm its potential as a reliable and effective
tool for clinical use.




                                           2
                  Background of Research, Research Plan and Methodology
Research Background
     Soft continuum robots, inspired by biological organisms like octopus tentacles and elephant
trunks, are a class of robots with infinite degrees of freedom and flexible structures. Unlike tra-
ditional rigid robots, their high flexibility, adaptability, and inherent safety allow them to operate
in complex environments. This makes them highly promising for diverse applications, including
medicine, search and rescue, deep-sea exploration, and industrial manufacturing [Russo et al.,
2023]. In particular, soft continuum robots can perform precise operations through narrow body
cavities in minimally invasive surgery (MIS) [Dupont et al., 2022].
     However, current soft continuum robots face significant challenges in design, modeling,
and control. Issues such as specialized medical robots design, precise motion control, and
multi-robot collaborative strategy limit their performance and widespread adoption [Konda et
al., 2025]. While MIS has revolutionized patient care by reducing trauma and recovery times,
current rigid instruments and conventional single-actuation flexible robots still have limited ma-
neuverability and lack the necessary workspace and precision for complex tasks in confined
anatomical spaces.
     Research into flexible robots for minimally invasive surgery (MIS) has explored both tendon-
driven [Till, Aloi, and Rucker, 2019] and magnetic systems [Dreyfus et al., 2024; Kim et al.,
2019; Mao et al., 2024]. Tendon-driven robots are well-regarded for their high precision and
quick response, but they’re limited by a small workspace and often face issues with friction
and backlash. Conversely, magnetic guidance is excellent for non-contact navigation through
complex, winding paths to reach deep anatomical targets [Yang and Zhang, 2020]. However,
magnetic control typically lacks the precision for delicate tasks and is vulnerable to external mag-
netic fields and the properties of surrounding tissue. Therefore, a major challenge remains: how
to navigate complex, tortuous pathways to deep-seated targets and then perform high-precision,
delicate surgical manipulations.
     Cosserat rod theory is a widely accepted method for modeling the kinematics and dynamics
of continuum robots, as it accurately accounts for bending, shearing, and twisting deformations
[Alessi et al., 2024; Tummers et al., 2023]. However, most existing models based on this theory
are developed for traditional mechanical cable-driven actuation methods [Chitalia et al., 2020;
Wang and Simaan, 2019]. A major challenge lies in integrating magnetic forces into this frame-
work. Magnetic fields exert non-uniform, distributed forces on the robot, which are difficult to
model accurately, especially in the dynamic environment of the human body. Although some
modeling methods for kinematics and dynamics exist [Boyer et al., 2020], variable stiffness un-
der external loads has not been fully explored in a combined magnetic-tendon-driven context
[Oliver-Butler, Till, and Rucker, 2019]. Existing models are not equipped to handle the complex
interplay between magnetic fields and tendon tension.
     Many studies have implemented PID, sliding mode, or data-driven control strategies to han-
dle trajectory tracking [George Thuruthel et al., 2018; Liu et al., 2025]. However, these methods
often struggle with unmodeled dynamics and external disturbances in surgical environments.
While the Disturbance Observer (DO) has been used to address disturbances [Della Santina,
Truby, and Rus, 2020], its application has primarily been limited to single-actuator systems. A
significant challenge remains in the absence of a unified control strategy for multiple, collaborat-
ing flexible robots, as there are no established frameworks for coordinating a magnetic-guided
main body with a tendon-driven robot to perform complex and cooperative tasks.
     This work aims to advance the state-of-the-art in medical robotics by addressing critical
clinical and technical challenges, thereby paving the way for safer and more effective surgical

                                                  1
interventions.

Research Methods
    This research proposes a groundbreaking approach to flexible robotics for MIS. We will de-
velop a novel multi-robot system that seamlessly combines the benefits of large-scale navigation
with the ability to perform fine, delicate manipulations. To achieve this, we will build a hybrid
magnetic and tendon-driven system to overcome the limitations of single-actuation methods.
This system will be supported by a hybrid magnetic-Cosserat rod model, which allows for vari-
able stiffness and enhanced accuracy. We will then design a sophisticated real-time control and
collaborative strategy to manage complex dynamics and coordinate the actions of the multiple
robots. And finally, when conducting real medical experiments, the assessment of the stability
and safety of robots is an inevitable issue. Our tasks will be divided into the following four
points:

     Task 1: Hybrid Actuation System Design and Optimization
     1) System Design:
     Existing approaches to robotic actuation in minimally invasive surgery often rely on a single
modality, such as tendon-driven mechanisms for precision or magnetic guidance for navigation.
However, these systems are fundamentally limited by a trade-off: tendon-driven robots offer
high precision but have a restricted workspace and are prone to friction, while magnetic systems
excel at navigating complex anatomy but lack the fine control required for delicate surgical tasks.
     To overcome this limitation, we propose a novel hybrid actuation system consisting of two
cooperative robots (Fig. 1 in Illustrations). The key innovation is the decoupled yet syn-
ergistic integration of magnetic actuation (Robot 1)—offering a compact form and high
maneuverability—with an antagonistic tendon-driven system (Robot 2) that provides pre-
cisely tunable stiffness. This hybrid approach fundamentally outperforms single-modality sys-
tems by merging large-scale navigation capability with high-precision manipulation. It directly
addresses the clinical challenge of accessing deep-seated targets through complex anatomy and
performing accurate interventions, thereby significantly expanding the potential of soft robots
in surgery.
     2) Design Optimization:
     Existing design optimization for tendon-driven robots primarily focuses on maximizing dex-
terity or minimizing internal friction for a given number of tendons, often treating the robot’s
flexibility and stiffness as fixed properties [Dalvand, Nahavandi, and Howe, 2018]. A common
limitation is the lack of a holistic framework that co-optimizes structural parameters with real-
world biomechanical constraints to dynamically balance navigational flexibility and operational
stiffness.
     To address this, our methodology introduces a simulation-driven optimization loop that
integrates biomechanical performance data (Fig. 2 in Illustrations). The novelty of this ap-
proach lies in its closed-loop, multi-objective optimization strategy that simultaneously
adjusts tendon configuration and structural geometry to achieve situationally-aware stiff-
ness adaptation. This differs significantly from conventional single-objective or sequential op-
timization methods by enabling the same robot to transition seamlessly between high flexibility
during navigation and high stiffness during precise operation. The impact of this optimization is
substantial: it directly enhances the robot’s ability to traverse confined anatomical environments
without sacrificing precision in task execution, thereby increasing the reliability and safety of
the overall hybrid system in surgical applications.


                                                2
     Task 2: Kinematics and Dynamics of the Hybrid Magnetic-Tendon Driven Model
     While Cosserat rod theory is widely used to model continuum robots, existing models are
primarily designed for traditional actuation methods like tendons or pneumatics [Tummers et
al., 2023]. Few have successfully integrated the distributed and posture-dependent forces of
magnetic actuation into the same dynamic framework.
     To overcome this limitation, we will develop a unified kinematics and dynamics model based
on Cosserat rod theory that fully integrates both tendon-driven and magnetic actuation mech-
anisms. The novelty of this model lies in its incorporation of magnetic force distribution
as a continuous function of robot configuration and external field geometry, coupled with
tendon loading effects within a single dynamical system. This approach is distinct from ex-
isting models, and will enable more accurate prediction of robot behavior under simultaneous
magnetic and tendon inputs. The significance of such a model is substantial: it will provide a
critical foundation for model-based control design in Task 3, directly enhancing trajectory accu-
racy, disturbance rejection, and overall performance of the hybrid system in uncertain surgical
environments. The model will be validated through rigorous comparison with experimental data
collected from our physical prototype.

     Task 3: Dexterous Manipulation and Multi-Robot Collaborative Control Strategy
     1) NN-Based Model Prediction Control:
     Conventional control strategies, such as PID or sliding mode control, often struggle with
the unmodeled dynamics, hysteresis, and disturbances common in soft robots. Although some
methods use disturbance observers or adaptive elements, they typically lack real-time learning
capabilities and cannot handle the coupled uncertainties of a hybrid magnetic-tendon system.
     To address these challenges, our control framework introduces a novel Neural Network-
based Model Predictive Control (NN-MPC) strategy, as illustrated in Fig. 3 in Illustrations.
Building upon the unified dynamics model from Task 2, we will develop a Fuzzy Neural Net-
work (FNN) architecture that continuously adapts using the real-time error between the model’s
predictions and the actual system response. The core innovation is the integration of fuzzy
logic with a neural predictive controller, allowing for online learning without the need for
pre-training or extensive datasets. This approach provides the interpretability of fuzzy logic
and the powerful approximation of neural networks, significantly improving trajectory track-
ing and disturbance rejection [Chen et al., 2023]. Ultimately, this controller will ensure safe,
high-precision operation of the hybrid robot in a dynamically uncertain surgical environment.
     2) Learning-Based Collaborative Control:
     Traditional multi-robot collaboration often depends on pre-defined coordination rules or cen-
tralized planners, which lack adaptability to dynamic environments and are difficult to generalize
across tasks. In surgical settings, where anatomical obstacles and tissue interactions introduce
high uncertainty, such methods frequently result in brittle or unsafe behaviors when encounter-
ing unmodeled conditions.
     To overcome these limitations, we will develop a novel learning-based collaborative con-
trol strategy using deep reinforcement learning (DRL). This approach will train the magnetic-
guided and tendon-driven robots to cooperatively perform surgical tasks without explicit ana-
lytical modeling of their interaction. The training will take place in a high-fidelity simulation
environment that captures both robot dynamics and realistic surgical constraints, including de-
formable obstacles and stochastic disturbances. The key novelty of this framework lies in its
end-to-end learning of a shared control policy that directly maps hybrid robot states to


                                                3
complementary actuation commands, enabling adaptive and robust co-manipulation un-
der uncertainty. This is a significant departure from conventional hierarchical or master-slave
control schemes, as the DRL agent learns implicit coordination mechanisms that are optimized
for global task performance and safety. The impact of this capability is profound: it will allow
the system to autonomously negotiate complex surgical scenarios—such as navigating around
sensitive anatomy while maintaining manipulation stability—greatly enhancing both autonomy
and reliability in clinical applications. The resulting policy will be transferred to and validated
on the physical system, demonstrating improved performance in the presence of non-linear dy-
namics and unknown disturbances.

     Task 4: System Integration and Evaluation:
     While previous tasks develop individual components and algorithms, the ultimate measure
of success lies in integrated system performance under realistic conditions. Many innovative
robotic systems fail to transition from theoretical promise to practical viability due to a lack of
rigorous, application-focused evaluation that fully captures performance limits and operational
robustness.
     This task will rigorously evaluate the fully integrated hybrid robotic system to confirm
its clinical viability. We’ll go beyond basic component testing to focus on emergent system-
level capabilities, especially the seamless collaboration between the magnetic and tendon-driven
robots. Our quantitative framework will measure three key metrics in high-fidelity anatomical
simulations: (i) Collaborative Precision: How accurate is the system during coordinated naviga-
tion and manipulation? (ii) Adaptive Performance: How well does it handle dynamic constraints
and uncertainties? and (iii) Operational Safety: Can it maintain stability and recover from unex-
pected interactions? By stress-testing the system across a range of simulated surgical scenarios,
we will provide crucial validation of our research and demonstrate the system’s readiness for
future development in minimally invasive surgery.

Research Plan
                                                    Project Timeline (Months)
                                    1 2 3 4 5 6 7 8 9 101112131415161718192021222324
              T1: System Design
          1.1 Mechanical Design
    1.2 Simulation Optimization
       1.3 Prototype Fabrication
       T2: Dynamics Modeling
         2.1 Model Development
            2.2 Model Validation
     T3: Control Development
           3.1 FNN-MPC Design
                3.2 DRL Training
        T4: System Evaluation
           4.1 System Integration
         4.2 Performance Testing
        4.3 Real Medical Testing


                                                4
                               Illustrations




       Figure 1: Proposed hybrid actuation robotic endoscopic system.




Figure 2: Proposed multi-objective optimization model for optimal mechanism.




          Figure 3: Fuzzy NN-Based Model Prediction Controller.




                                     1
                                 Pathways to Impact Statement
    This research on hybrid continuum robots will deliver a multi-faceted impact across several
key sectors, from fundamental science to real-world patient care.
    1. Advancing the Scientific Community: We will contribute a foundational advancement in
robotics by pioneering a novel hybrid magnetic and tendon-driven actuation system. This work
moves beyond the limitations of single-actuation designs, offering new insights into how to
achieve high-precision motion with an expanded workspace. The new unified modeling frame-
work, based on Cosserat rod theory, will provide a robust and open-source foundation for future
research in soft robotics and medical device development. Furthermore, our development of
real-time control algorithms with a Disturbance Observer will provide a new benchmark for
achieving robust, high-accuracy control in complex, uncertain environments. These contribu-
tions will be shared through peer-reviewed publications and conference presentations to accel-
erate progress within the global robotics community.
    2. Driving Innovation in Industry: Our hybrid actuation technology and advanced control
methods have direct commercial potential. The enhanced precision and maneuverability of our
robotic system will make it highly attractive for medical device manufacturers seeking to de-
velop next-generation tools for minimally invasive surgery. The technology could lead to more
efficient and safer surgical procedures, reducing operational costs for hospitals and improving
patient outcomes. Beyond surgery, the system’s principles could be adapted for a wide range
of industrial applications, including complex assembly tasks in confined spaces or inspection of
sensitive machinery.
    3. Informing Healthcare Policy and Standards: The successful validation of our robot in re-
alistic surgical scenarios will provide crucial data on the performance, safety, and effectiveness
of continuum robots in a clinical setting. This evidence will be vital for informing the develop-
ment of new medical device regulations, clinical guidelines, and safety protocols. Our research
can help regulatory bodies, such as the FDA, establish clearer standards for the approval and
use of advanced robotic tools, ensuring patient safety is the highest priority.
    4. Improving Societal Well-being: Ultimately, the greatest impact of this research is its
potential to improve the lives of patients. By enabling more precise and less invasive surgical
procedures, our technology can reduce tissue damage, shorten recovery times, and lower the risk
of complications. This translates to less pain and stress for patients and a faster return to their
daily lives. The development of a more reliable and adaptable surgical robot holds the promise
of making advanced medical care more accessible and safer for a broader population.




                                                1
                                              References
Alessi, C., Agabiti, C., Caradonna, D., Laschi, C., Renda, F., & Falotico, E. (2024). Rod models
          in continuum and soft robot control: A review. arXiv preprint arXiv:2407.05886.
Boyer, F., Lebastard, V., Candelier, F., & Renda, F. (2020). Dynamics of continuum and soft
          robots: A strain parameterization based approach. IEEE Transactions on Robotics, 37(3),
          847–863.
Chen, C., Cao, Y., Chen, X., Wu, D., Xiong, C., & Huang, J. (2023). A fused deep fuzzy neural
          network controller and its application to pneumatic flexible joint. IEEE/ASME Transac-
          tions on Mechatronics, 28(6), 3214–3225.
Chitalia, Y., Jeong, S., Yamamoto, K. K., Chern, J. J., & Desai, J. P. (2020). Modeling and
          control of a 2-dof meso-scale continuum robotic tool for pediatric neurosurgery. IEEE
          Transactions on Robotics, 37(2), 520–531.
Dalvand, M. M., Nahavandi, S., & Howe, R. D. (2018). An analytical loading model for n-
          tendon continuum robots. IEEE Transactions on Robotics, 34(5), 1215–1225.
Della Santina, C., Truby, R. L., & Rus, D. (2020). Data–driven disturbance observers for esti-
          mating external forces on soft robots. IEEE Robotics and automation letters, 5(4), 5717–
          5724.
Dreyfus, R., Boehler, Q., Lyttle, S., Gruber, P., Lussi, J., Chautems, C., Gervasoni, S., Berberat,
          J., Seibold, D., Ochsenbein-Kölble, N., et al. (2024). Dexterous helical magnetic robot
          for improved endovascular access. Science Robotics, 9(87), eadh0298.
Dupont, P. E., Simaan, N., Choset, H., & Rucker, C. (2022). Continuum robots for medical
          interventions. Proceedings of the IEEE, 110(7), 847–870.
George Thuruthel, T., Ansari, Y., Falotico, E., & Laschi, C. (2018). Control strategies for soft
          robotic manipulators: A survey. Soft robotics, 5(2), 149–163.
Huo, J., Huang, J., Zuo, J., Yang, B., Fu, Z., Li, X., & Mohammed, S. (2025). Innovative design
          of multifunctional supernumerary robotic limbs with ellipsoid workspace optimization.
          IEEE Transactions on Robotics, 41, 4699–4718.
Huo, J., Xu, K., Li, C., Cao, Y., Zuo, J., Chen, X., & Huang, J. (2025). Variable impedance control
          for floating-base supernumerary robotic leg in walking assistance. IEEE Robotics and
          Automation Letters, 10(9), 8698–8705.
Kim, Y., Parada, G. A., Liu, S., & Zhao, X. (2019). Ferromagnetic soft continuum robots. Science
          robotics, 4(33), eaax7329.
Konda, R., Brumfiel, T. A., Bercu, Z. L., Grossberg, J. A., & Desai, J. P. (2025). Robotically
          steerable guidewires—current trends and future directions. Science Robotics, 10(105),
          eadt7461.
Liu, J., Duo, Y., Chen, X., Zuo, Z., Liu, Y., & Wen, L. (2025). Data-driven methods for sensing,
          modeling and control of soft continuum robot: A review. IEEE/ASME Transactions on
          Mechatronics.
Mao, L., Yang, P., Tian, C., Shen, X., Wang, F., Zhang, H., Meng, X., & Xie, H. (2024). Mag-
          netic steering continuum robot for transluminal procedures with programmable shape
          and functionalities. Nature communications, 15(1), 3759.
Oliver-Butler, K., Till, J., & Rucker, C. (2019). Continuum robot stiffness under external loads
          and prescribed tendon displacements. IEEE transactions on robotics, 35(2), 403–419.
Russo, M., Sadati, S. M. H., Dong, X., Mohammad, A., Walker, I. D., Bergeles, C., Xu, K., &
          Axinte, D. A. (2023). Continuum robots: An overview. Advanced Intelligent Systems,
          5(5), 2200367.
Till, J., Aloi, V., & Rucker, C. (2019). Real-time dynamics of soft and continuum robots based on
          cosserat rod models. The International Journal of Robotics Research, 38(6), 723–746.


                                                1
Tummers, M., Lebastard, V., Boyer, F., Troccaz, J., Rosa, B., & Chikhaoui, M. T. (2023).
        Cosserat rod modeling of continuum robots from newtonian and lagrangian perspec-
        tives. IEEE Transactions on Robotics, 39(3), 2360–2378.
Wang, L., & Simaan, N. (2019). Geometric calibration of continuum robots: Joint space and
        equilibrium shape deviations. IEEE Transactions on Robotics, 35(2), 387–402.
Yang, Z., & Zhang, L. (2020). Magnetic actuation systems for miniature robots: A review. Ad-
        vanced Intelligent Systems, 2(9), 2000082.
Zuo, J., Huo, J., Xiao, X., Zhang, Y., & Huang, J. (2025). Human–robot coordination control
        for sit-to-stand assistance in hemiparetic patients with supernumerary robotic leg. IEEE
        Transactions on Automation Science and Engineering, 22, 16591–16602.




                                               2
                   Related experience or participation in research projects
    1. Soft Actuators
    I have participated in a research project on a gas-driven bending continuum robot, referred
to as Pneumatic Muscle Actuators (PMA). The designed and fabricated pneumatic grid-type
bending pneumatic muscle is made by casting room-temperature vulcanizing silicone rubber.
This type of pneumatic muscle’s air cavity consists of multiple rectangular air chambers. When
inflated with high-pressure gas, the rectangular air chambers undergo significant expansion,
while the bottom of the mechanism, due to the embedded glass fiber layer, has greater stiffness
and thus experiences smaller deformation, resulting in the pneumatic muscle bending towards
the bottom of the mechanism. Multi-segment PMAs for grasping experiments are designed,
with the configuration shown in Figure 1. The grasping effect is illustrated in Figure 2.




                           Figure 1: Illustration of PMA bending.




                              Figure 2: PMA for grasping task.

    2. Design and Modeling
    A set of supernumerary robotic limbs (SRLs) was designed with a focus on universal func-
tionality for both upper and lower limbs. This work, including the development of its dynamic
model and subsequent optimization, has been published in the prestigious journal, IEEE Trans-
actions on Robotics (TRO) [Huo, Huang, et al., 2025].



                                              1
                      Figure 3: Model of supernumerary robotic limbs.

    3. Controller Design
    We designed a novel control strategy based on variable impedance control, which was specif-
ically applied to two distinct motor tasks: walking and sit-to-stand. To ensure adaptability, a
neural network model was implemented to enable online tuning of the controller parameters.
This work has been disseminated through publications in both IEEE Robotics and Automation
Letters [Huo, Xu, et al., 2025] and IEEE Transactions on Automation Science and Engineering
[Zuo et al., 2025].




                         Figure 4: Controller design for walking task.




                       Figure 5: Controller design for sit-to-stand task.




                                               2
                                      LeaderShip Experience
    My experience in academia has provided me with various opportunities to develop and show-
case leadership skills, from managing complex research projects to mentoring junior colleagues.
These experiences have prepared me to lead my proposed research project under the RGC-JRFS.
    1. Project and Team Leadership
    As a core member of the NSFC project ”Basic research on supernumerary robotic limb for
hemiplegia motion assistance” (U1913207), I took on a significant leadership role in shaping the
project’s direction. I was actively involved in developing the project plan and organizing regular
team meetings to ensure progress. My responsibilities included leading the core experiments,
conducting data analysis, and writing manuscripts, which resulted in publications in robotics
journals, including IEEE Transactions on Robotics (TRO). My contributions were crucial to the
project’s success and demonstrated my ability to drive a research project from conception to
publication.
    2. Mentorship and Student Guidance
    Mentoring and guiding students has been a rewarding part of my academic journey. I have
directly supervised a junior Ph.D. student on their research topic and guided over 10 master’s
and undergraduate students through their thesis projects and various technological competitions.
As the team captain of a five-person team in The Sixth China Post-Graduate Student Robot In-
novation Design Competition, I successfully led us to a National Second Prize. This experience
honed my ability to delegate tasks effectively, foster teamwork, and achieve ambitious goals
under pressure.
    3. Academic and Collaborative Leadership
    Beyond the lab, I’ve actively contributed to the academic community by organizing events
and fostering collaborations. I served as an organizer for the IEEE CBS 2022 conference, where
I was responsible for coordinating logistics, including arranging transportation for speakers and
managing parallel session. This experience demonstrated my organizational skills and my com-
mitment to creating a productive environment for academic exchange.
    My future plans include leveraging these leadership skills to successfully execute my RGC-
JRFS project. I aim to mentor new postgraduate students, helping them complete their research
topics and develop their own leadership potential. I also plan to organize academic seminars and
establish collaborations with institutions such as Huazhong University of Science and Technol-
ogy and the University of Leeds, with the goal of expanding the project’s impact and fostering
a strong academic network in Hong Kong.
    My proven track record of leading projects, mentoring students, and organizing academic
activities shows that I possess the necessary skills and vision to be a successful leader in my
field. I am confident that these experiences will allow me to not only achieve my research goals
but also contribute meaningfully to the academic community in Hong Kong.




                                                1
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) HUO
                        (Other name) Jun
Host Faculty / School / Department / Faculty of Engineering, Department of Mechanical and Automation
Division / Unit ^ :                  Engineering

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known the nominee, Jun Huo, for approximately seven years in my capacity as his PhD advisor and
primary thesis supervisor at Huazhong University of Science and Technology. Our extensive working
relationship began when he joined my research group in 2018 and has continued until now.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent      Very Good        Good                Average         No basis
                                (top 5%)       (top 6-20%) (top 21-50%)            or below           for
                                                                                  (lower than      judgment
                                                                                     50%)
Intellectual capacity                              ☐                ☐                 ☐               ☐
                                                    ☐                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                    
                                                    ☐                ☐                 ☐               ☐
Knowledge of proposed
research study
                                    

                                                    ☐                ☐                 ☐               ☐
Analytical power and
reasoning
                                    
Judgment                                           ☐                ☐                 ☐               ☐
                                                    ☐                ☐                 ☐               ☐
Imagination and
originality
                                    

                                                    ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                    

                                    ☐                                ☐                 ☐               ☐
Skills of writing and
argumentation
                                                    

                                                    ☐                ☐                 ☐               ☐
Capacity for independent
work
                                    

                                                    ☐                ☐                 ☐               ☐
Reliability and sense of
responsibility
                                    
Leadership experience                              ☐                ☐                ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
I have known Jun Huo for seven years as his PhD and Master advisor. Throughout this time, I have been
consistently impressed by his exceptional research abilities and intellectual independence.

He demonstrates strong skills in mechanical design, modeling analysis, and successfully applied them to
develop a novel method for multi-functional supernumerary robotic limb, resulting in IEEE TRO.

What distinguishes him is his capacity for independent critical thinking and problem-solving. He proposed and
designed the research project for the JRFS, which is both highly original and feasible. Furthermore, his
adaptability and collaborative nature will enable him to thrive in the international research environment in HK.

Without any reservation, I highly recommend Jun Huo for the JRFS. He possesses outstanding potential to
become a future leader in the field.




                                                        3
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: Jian Huang                                      Title: Prof.
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Huazhong University of Science and Technology
 Position: Luoyu Raod 1037, Wuhan, China
 E-mail Address: <EMAIL>
 Telephone Number#: +86-13627206071
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date: 9.1.2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars            (to be completed by the nominee)
Name of Nominee :       (Surname) HUO
                        (Other name) Jun
Host Faculty / School / Department / Faculty of Engineering, Department of Mechanical and Automation
Division / Unit ^ :                  Engineering

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report             (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

    I have known the nominee since 2019, when we were doctoral students at the School of Artificial Intelligence
    and Automation, Huazhong University of Science and Technology, Wuhan, China. We have maintained close
    academic interactions, including collaborative work as well as frequent discussions on research problems. Until
    2024, we were based at the same institution, after which I moved to the University of Leeds, Leeds, UK, but we
    have continued to stay in regular professional contact.




                                                        2
                                                                  JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                               Excellent     Very Good        Good                Average          No basis
                               (top 5%)      (top 6-20%) (top 21-50%)            or below            for
                                                                                (lower than       judgment
                                                                                   50%)
Intellectual capacity              ☐                               ☐                 ☐               ☐
                                                   ☐                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                   
                                                                    ☐                 ☐
Knowledge of proposed
research study
                                  ☐                                                                  ☐
                                                                    ☐                 ☐               ☐
Analytical power and
reasoning
                                                 ☐
Judgment                                          ☐                ☐                 ☐               ☐
                                   ☐                                ☐                 ☐               ☐
Imagination and
originality
                                                   

                                                   ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                   

                                   ☐                                ☐                 ☐               ☐
Skills of writing and
argumentation
                                                   

                                                   ☐                ☐                 ☐               ☐
Capacity for independent
work
                                   

                                                                    ☐                 ☐               ☐
Reliability and sense of
responsibility
                                                 ☐
Leadership experience             ☐                                ☐                ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
    The nominee has shown outstanding research ability and strong potential for future development. His main
    expertise is in the design and development of wearable supernumerary robotic limbs. He has demonstrated
    solid skills in mechanical design, system integration, and platform building, creating functional prototypes
    that enable effective testing and validation of innovative ideas.

    His research has been published in leading journals such as IEEE Transactions on Robotics (TRO), IEEE
    Robotics and Automation Letters (RAL), and IEEE Transactions on Automation Science and Engineering
    (TASE), reflecting both the originality and impact of his contributions. These achievements highlight his
    ability to combine creativity with rigorous engineering practice.

    He is highly motivated with the capacity to work independently as well as collaboratively. His writing and
    communication skills allow him to clearly present complex ideas, further strengthening his academic profile.
    Overall, his expertise in wearable robots and his strong research record make him an excellent candidate for
    the JRFS.

                                                       3
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: YU CAO                                          Title: Dr
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: University of Leeds
 Position: UKRI Research Fellow
 E-mail Address: <EMAIL>
 Telephone Number#: +44 (0)**********
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature: Yu Cao                                             Date: 31/08/2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
