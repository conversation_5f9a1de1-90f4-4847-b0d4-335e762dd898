<PERSON><PERSON><PERSON><PERSON>, Hubei Province, China

Email: <EMAIL>

Phone: +86 18972921668

Education
Wuhan University, China                                                                      2022/09 - 2025/12
Ph.D. in Computer Science and Technology
Research Focus: Speech Enhancement, Intelligent Speech Signal Processing, Multimodal Learning
Trinity College Dublin, Ireland                                                             2019/09 - 2020/10
M.Sc. in Electronic Information Engineering
Grade: Second Class Honours, Grade 1 (Higher Merit)
Thesis: Voice Anti-spoofing for Automatic Speaker Verification

Vaasa University of Applied Sciences, Finlan d                                              2017/09 - 2019/06
B.Sc. in Information Technology
GPA: 3.5/5.0 (Top 5%)
Thesis: Design and Implementation of IP Video Surveillance

Hubei University of Technology, China                                                       2015/09 - 2017/06
B.Eng. in Electrical Engineering and Automation
GPA: 80/100


Work Experience
Peking University Shenzhen Graduate School                                                    2021/06 – 2022/06
Research Assistant, School of Information Engineering
    •    Developed projects related to speech front-end technologies.
    •    Conducted research on key technologies for mobile phone noise reduction.
Vivo Mobile Communication (Hangzhou) Co., Ltd                                                  2020/07 – 2021/06
Assistant AI Engineer (Intern), AI Division
    •     Participated in the development of vivo's AI voice assistant, Jov.
    •     Researched and developed noise reduction algorithms for mobile phones.

Vaasa University, Finland                                                                        2018/09 - 2019/05
Assistant Researcher (Part-time), Technobothnia Lab,
    •     Conducted research on robotics-related technologies, including embedded systems, intelligent control,
          and speech recognition.
    •     Participated in research and development projects for intelligent robots in collaboration with ABB and
          Wärtsilä.


Research Projects
Nonlinear Level-Based Acoustic Echo Adaptive Cancellation                      2022/06 - Present
Supervisor: Weiping Tu, Wuhan University
   • Explored low signal-to-noise ratio speech enhancement algorithms based on pure speech input.
   • Investigated key algorithms for speech and video front-end enhancement.

Acoustic Scene Depth Analysis for Intelligent Home Service Robots                  2021/06 - 2022/05
Supervisor: Yuexian Zou, Peking University
   • Investigated key algorithms for speech and video front-end enhancement.
   • Investigated key algorithms for speech front-end enhancement.

Multimodal Health Monitoring Big Data System Based on Infant Sounds             2020/10 - 2021/05
Supervisor: Yuexian Zou, Peking University
   • Explored key technologies for a multimodal health monitoring big data system based on infant
        sounds.
   • Investigated key algorithms for speech front-end enhancement


Language Skills

▪   English (IELTS 6.5)
▪   Finnish (Passed Basic Finnish Language Exam)


Awards

▪ 2018: Academic Scholarship of 2000 Euros from Vaasa University of Applied Sciences.
▪ 2023: Second Prize for Graduate Academic Innovation Scholarship (15,000 RMB) from Wuhan
University.
▪ 2024: First Class Academic Excellence Scholarship (6,000 RMB) from Wuhan University, School of
Computer Science.


Publications
Conference Papers：
   • Xinmeng Xu, Yang Wang, Dongxiang Xu, Yiyuan Peng, Cong Zhang, Jie Jia, Binbin Chen, “Multi-stage
       Progressive Speech Enhancement Network”, INTERSPEECH 2021.

    •   Xinmeng Xu, Yang Wang, Dongxiang Xu, Yiyuan Peng, Cong Zhang, Jie Jia, Binbin Chen, “VSEGAN:
        Visual Speech Enhancement Generative Adversarial Network”, ICASSP 2022.

    •   Xinmeng Xu, Rongzhi Gu, Yuexian Zou, “Improving Dual-microphone Speech Enhancement by Learning
        Cross-channel Features with Multi-head Attention”, ICASSP 2022.

    •   Xinmeng Xu, Jianjun Hao, “U-Former: Improving Monaural Speech Enhancement with Multi-head Self
        and Cross Attention”, ICPR 2022.
   •   Xinmeng Xu, Yang Wang, Jie Jia, Binbin Chen, Jianjun Hao, “GLD-Net: Improving Monaural Speech
       Enhancement by Learning Global and Local Dependency Features with GLD Block”, INTERSPEECH
       2022.
   •   Xinmeng Xu, Yang Wang, Jie Jia, Binbin Chen, Dejun Li, “Improving Visual Speech Enhancement
       Network by Learning Audio-visual Affinity with Multi-head Attention”, INTERSPEECH 2022.

   •   Xinmeng Xu, Weiping Tu, Yuhong Yang, “Selector-Enhancer: Learning Dynamic Selection of Local and
       Non-local Attention Operation for Speech Enhancement”. AAAI 2023.

   •   Xinmeng Xu, Weiping Tu, Yuhong Yang, “PCNN: A Lightweight Parallel Conformer Neural Network for
       Efficient Monaural Speech Enhancement”. INTERSPEECH 2023.

   •   Xinmeng Xu, Weiping Tu, Yuhong Yang, “Leveraging Sound Local and Global Features for Language-
       Queried Target Sound Extraction”. ICONIP 2023.

   •   Xinmeng Xu, Weiping Tu, Yuhong Yang, “Curricular Contrastive Regularization for Speech Enhancement
       with Self-supervised Representations”, ICASSP 2024.

   •   Xinmeng Xu, Jizhen Li, Yiqun Zhang, Yong Luo, Weiping Tu, Yuhong Yang, “FIRING-Net: A Filtered
       Feature Recycling Network for Speech Enhancement”, ICLR 2025.

Journal Papers：
       Xinmeng Xu, Weiping Tu, Yuhong Yang, “CASE-Net: Integrating local and non-local attention
        operations for speech enhancement”. Speech Communication.

       Xinmeng Xu, Weiping Tu, Yuhong Yang, “Adaptive selection of local and non-local attention
        mechanisms for speech enhancement”. Neural Networks.

       Xinmeng Xu, Jizhen Li, Yiqun Zhang, Yong Luo, Weiping Tu, Yuhong Yang, “Efficient audio–visual
        information fusion using encoding pace synchronization for Audio–Visual Speech Separation”,
        Information Fusion.

       Xinmeng Xu, Jizhen Li, Yiqun Zhang, Yong Luo, Weiping Tu, Yuhong Yang, “Spatial information aided
        speech and noise feature discrimination for Monaural speech enhancement”, Expert Systems with
        Applications.

Submitted Papers:
   • Xinmeng Xu, Jizhen Li, Yiqun Zhang, Weiping Tu, Yuhong Yang, "Interactive Target Positive and
      Negative Features Modeling for Monaural Speech Enhancement", IEEE Transactions on Audio, Speech,
      and Language Processing.

   •   Xinmeng Xu, Jizhen Li, Yiqun Zhang, Hongyang Chen, Weiping Tu, Yuhong Yang, "Contribution-aware
       Dynamic Multi-modal Balance for Audio-Visual Speech Separation", IEEE Transactions on Multimedia.

   •   Xinmeng Xu, Jizhen Li, Yiqun Zhang, Hongyang Chen, Weiping Tu, Yuhong Yang, "DREAM-Net:
       Detection and Realignment of Mismatched Features for Audio-Visual Speech Separation", ICCV 2025.

   •   Xinmeng Xu, Jizhen Li, Yiqun Zhang, Yong Luo, Weiping Tu, Yuhong Yang, "A Filter-Recycle-
       Interguide Framework for Optimizing Regression Tasks and Its Application in Audio-Visual Speech
       Separation", IEEE Transactions on Pattern Analysis and Machine Intelligence.
Neurogenic dysarthria, commonly caused by stroke or Parkinson’s disease, severely
compromises speech intelligibility due to unstable articulation, blurred consonant
boundaries, and irregular timing. These deficits not only hinder everyday
communication but also slow rehabilitation and increase caregiver burden.
Conventional speech enhancement systems, although effective for healthy speakers in
noisy environments, are built on assumptions of stable articulation and tightly
synchronized audio–visual cues. When applied to pathological speech, they often
oversmooth critical features, distort prosody, or produce outputs that remain
unintelligible.
This project proposes a clinically informed audio–visual speech enhancement (AVSE)
framework tailored to the unique challenges of dysarthria. The research is organized
into three interconnected tasks:
Task 1: Prosody-preserving unimodal backbone. Develop a robust auditory
backbone that can capture spectral clarity and preserve natural rhythm even when
articulation is unstable. By modeling temporal energy contours alongside spectral
features, this backbone provides clinically meaningful unimodal representations. It
serves as the scientific foundation for later multimodal modeling, ensuring that
downstream fusion is not misled by unstable input.
Task 2: Semantic realignment for pathological synchrony. In dysarthria,
articulatory gestures and acoustic signals frequently fall out of sync, causing semantic
drift that conventional AVSE systems, trained only on healthy datasets, cannot correct.
This task introduces a semantic realignment framework that detects and compensates
for audiovisual desynchrony, explicitly contrasting healthy and pathological synchrony
patterns. By restoring prosody-consistent multimodal alignment, it yields more
intelligible and semantically faithful speech reconstructions.
Task 3: Adaptive multimodal fusion under real-world constraints. Practical
deployment requires resilience to noisy environments, fluctuating video quality,
occlusion, and limited computational resources. This task investigates adaptive fusion
strategies that dynamically weight audio and visual contributions according to their
reliability. It further studies lightweight architectures to analyze efficiency–fidelity
trade-offs, evaluates cross-patient generalization, and produces clinically validated
benchmarks to guide reproducibility and integration into rehabilitation practice.
The system will be trained on disorder-specific corpora and evaluated across both
laboratory and real-world conditions, with outcomes assessed in terms of intelligibility,
robustness, and perceptual quality. Clinically, the framework is expected to support
everyday communication and rehabilitation for individuals with neurogenic dysarthria,
reducing barriers to social participation and care. Beyond clinical impact, the project
will advance multimodal speech processing research and create open resources that
foster interdisciplinary training and collaboration across AI, signal processing, and
speech sciences.
1. Proposed Objectives
Conventional speech enhancement systems, designed for healthy speech, fail under the
disorder-specific irregularities of neurogenic dysarthria. This project focuses on three
key issues, each defined by its critical elements and addressed through a dedicated task.
Objective 1: Prosody-preserving unimodal backbone for dysarthric speech
Dysarthric speech is marked by unstable articulation, blurred consonant boundaries,
and irregular timing, which undermine the assumptions of conventional audio-only
enhancement models. Existing unimodal approaches often oversmooth spectral cues or
distort prosodic rhythm, leading to reduced intelligibility. This objective is to develop
a prosody-preserving auditory backbone that explicitly models temporal energy
contours and spectral integrity, ensuring that pathological speech is represented in a
clinically meaningful and technically reliable manner. Such unimodal stability is
critical to provide a solid foundation for subsequent multimodal integration and
synchrony analysis.
Objective 2: Semantic realignment for pathological audiovisual synchrony
In healthy speech, articulatory gestures and acoustic signals are tightly coupled, but in
dysarthria these cues often become asynchronous or weakened, producing semantic
drift that misleads multimodal fusion. Current AVSE systems, trained exclusively on
healthy datasets, implicitly assume intact synchrony and thus fail under pathological
conditions. This objective is to design a semantic realignment framework that detects
and corrects audiovisual desynchrony while contrasting healthy and pathological
synchronization patterns. By aligning audio and visual cues without distorting prosody,
the framework will yield semantically consistent multimodal representations and
advance the scientific understanding of cross-modal interaction in impaired speech.
Objective 3: Adaptive multimodal fusion under real-world constraints
Practical deployment of AVSE requires systems that remain robust under fluctuating
modality quality, diverse environmental noise, visual occlusion, and limited
computational resources. However, most existing models are optimized for controlled
datasets and neglect efficiency and cross-patient generalization, limiting clinical
applicability. This objective is to establish an adaptive fusion mechanism that
dynamically balances contributions from audio and visual modalities according to their
reliability, ensuring resilience when one modality degrades. The study will further
investigate lightweight architectures as a means to explore trade-offs between fidelity
and efficiency, and create clinically validated benchmarks to enable reproducible
evaluation and guide translation into rehabilitation practice.
2. Potential Impact
The project is positioned to advance speech-centric artificial intelligence while
addressing a critical unmet need in neurological rehabilitation. By framing dysarthric
speech enhancement as a multimodal compensation problem grounded in neural-
inspired principles, it bridges advanced signal processing research with real
rehabilitation practice.
Academic Impact：
(1) Establishes a new pathological multimodal enhancement paradigm within
    computer science by shifting the research focus from healthy-speech optimization
    toward neural-compensation models that are explicitly designed for disordered
    speech production, laying theoretical groundwork for future models that target
    brain-injury-induced articulatory deviations.
(2) Creates the first clinically annotated audio–visual dysarthria corpus with
    intelligibility and severity labels, enabling reproducible benchmarking and thereby
    accelerating data-driven innovation in machine learning research on neurogenic
    speech disorders.
(3) Enhances understanding of cross-modal representation learning under pathological
    conditions, contributing to foundational research on how neural networks can align
    semantically mismatched sensor streams.
(4) Provides a suite of evaluation protocols and deployable algorithmic tools that
    promote interdisciplinary research between machine learning scientists, signal
    processing engineers, and rehabilitation researchers, thereby stimulating long-term
    academic collaboration across computing and medical domains.
Societal (Clinical) Impact:
(1) Enables the development of portable, real-time speech enhancement tools that can
    operate in uncontrolled daily environments (background noise, visual occlusion,
    variable lighting), empowering individuals with dysarthria to engage in naturalistic
    conversations at home, work, and in public spaces.
(2) Facilitates home-based and tele-rehabilitation services by improving the clarity and
    intelligibility of patient speech transmitted over low-bandwidth remote systems,
    reducing dependence on in-person therapy, and mitigating geographic barriers to
    rehabilitation access.
(3) Promotes social inclusion and psychological well-being of individuals with
    neurogenic dysarthria, reducing communication-related stigma and isolation
    through improved interaction capability, thereby aligning with broader public
    health goals of equitable access to advanced AI-powered healthcare technologies.
Beyond academic and clinical advances, the proposed work also contributes to
education and professional development. The annotated corpus, evaluation protocols,
and algorithmic tools will provide open resources for training graduate students, early-
career researchers, and clinical practitioners, fostering interdisciplinary capacity
building at the interface of AI, signal processing, and rehabilitation science.
1. Background of Research
Communication breakdown is a debilitating consequence of neurological injury. In
neurogenic dysarthria, a motor speech disorder affecting up to 7 million new stroke
survivors annually [1], patients retain cognitive function but struggle to express
themselves due to slurred articulation, impaired voicing, and disrupted speech rhythm
[2]. These impairments reduce speech intelligibility, hinder social reintegration,
increase caregiver burden, and are strongly associated with depression, poor therapy
adherence, and social withdrawal [3,4]. Traditional speech therapy offers only gradual
improvement and depends on in-person clinical support, leaving patients poorly
equipped to communicate independently in real-world environments [5,6].
In recent years, deep learning–based speech enhancement methods, such as CNNs [7,8],
LSTMs [9,10], and Transformers [11,12], have achieved strong performance on normal
speech by suppressing noise and improving intelligibility. Yet these systems are built
on the assumption of acoustically stable speech distorted only by external noise [15,16],
an assumption violated in dysarthria where neuromotor deficits cause internal
variability. As a result, existing models often misrepresent dysarthric speech,
hallucinating content or erasing subtle articulatory cues essential for intelligibility [17].
To address the limits of audio-only methods, AV speech enhancement (AVSE)
integrates visual cues such as lip motion with acoustic features, and has shown large
gains in noisy conditions. Various fusion strategies, including dual-stream encoders,
cross-modal attention, and semantic integration, have been explored [18-21]. Yet nearly
all AVSE systems are trained solely on healthy speech, assuming tight synchrony and
intact articulation, conditions that do not hold for dysarthria.
Preliminary attempts to extend AV techniques to pathological speech (e.g., Parkinson’s
[22], ALS [23]) show only modest success, and no system has explicitly modeled the
misaligned, asynchronous, and semantically drifting patterns of neurogenic dysarthria
[24]. Dysarthric mouths may move more slowly than acoustic output, exhibit pauses
unrelated to speech boundaries, or produce incomplete closures, conditions under
which standard AVSE systems can further degrade prosody or intelligibility [25]. As a
result, no deployable solution currently operates reliably on dysarthric speech in
everyday environments. To fill these clinical and technical gaps, this project proposes
the first dysarthria-specific AV speech enhancement framework grounded in neural-
inspired compensatory mechanisms and real-world deployment needs, integrating (i)
pathological speech modeling, (ii) multimodal representation learning, and (iii) on-
device computing strategies, organized into three core tasks:
1. Pure-audio backbone to disentangle linguistic content, impairment traits, and
   external noise via energy-consistency constraints and contrastive representation
   learning;
2. Prosody-preserving multimodal enhancement under semantic realignment,
   integrating visual cues only when aligned and trustworthy while retaining rhythm
   and speaker identity;
3. Adaptive cross-modal fusion augmented with reliability monitoring and fast per-
   speaker calibration to support deployment in real-life home, telehealth and public
   conditions.
Building upon these contributions, this project specifically targets neurogenic
dysarthria, addressing its articulatory irregularities and asynchronous patterns. By
aligning with the pathological characteristics of dysarthria and respecting clinical usage
realities, it aims to deliver next-generation intelligibility enhancement tools to support
long-term communication for millions of affected individuals.
2. Research Plan and Methodology
The proposed research adopts a staged approach toward a clinically deployable AVSE
system. Task 1 develops a dysarthria-specific auditory backbone that disentangles
linguistic content, impairment traits, and noise, forming a resilient unimodal foundation.
Task 2 tackles pathology-driven asynchrony through a layer-wise detection–
realignment mechanism that locally corrects semantic drift while preserving prosody.
Task 3 moves to real-world deployment, introducing a reliability-aware fusion strategy
that adaptively balances modalities under noise, occlusion, and fluctuating input quality.
Task 1 (Month 1–10): Pure-Audio Backbone for Content–Impairment–Noise
Disentanglement
Dysarthric speech often shows unstable articulation and irregular timing, making
enhancement difficult when only audio is available. Task 1 addresses this by developing
a robust auditory backbone that preserves prosody and extracts stable spectral features,
providing a reliable unimodal foundation for later multimodal modeling.
Design Strategy. The backbone models each input spectrogram as a structured sum,
                         𝑥(𝑡, 𝑓) ≈ 𝑠̂ (𝑡, 𝑓) + 𝑛̂(𝑡, 𝑓) + 𝑟̂ (𝑡, 𝑓),
where 𝑥(𝑡, 𝑓) is the observed dysarthric spectrogram, 𝑠̂ (𝑡, 𝑓) the enhanced speech,
𝑛̂(𝑡, 𝑓) residual noise, and 𝑟̂ (𝑡, 𝑓) reverberation. The cognitive analogue corresponds
to selectively amplifying target speech patterns while filtering distractors.
•   Convolutional Back-Projection Module (CBPM, as shown in Fig. 1). It first
    extracts coarse speech vs noise pathways. Back‐projection retrieves discarded
    components, guarding against loss of weak dysarthric pronunciations. Two parallel
    refinement streams (convolutional with dilated kernels) iteratively emphasize
    speech-consistent modulations (e.g., vowel resonances) or typical interference
    signatures (e.g., transient bursts). Bidirectional cross-stream attention inhibits co-
    activation, mimicking inhibitory competition in selective attention.
•   Dual-Path Contrastive Transformer (DPCT, as shown in Fig. 2). This module
    applies frequency-wise and temporal attention on top of CBPM outputs. Regions of
    high speech saliency are pulled farther in latent space from interference regions
    through a contrastive embedding loss:
                                           exp(𝐷(𝑧+ , 𝑧− ))
                 ℒ𝑐𝑜𝑛𝑡𝑟𝑎𝑠𝑡 = −log                                       ,
                                    exp(𝐷(𝑧+ , 𝑧− )) + exp(𝐷(𝑧− , 𝑧− ))

    where 𝑧+ denotes the latent embedding of a speech-salient (target-relevant) patch,
    while 𝑧− represents the embedding from a noise-contaminated (interference-
    dominant) patch, sampled from background regions as negative instances. 𝐷(∙,∙) is
    a similarity measure (e.g., cosine distance), and the exponential-logarithmic
    formulation encourages the model to maximize similarity for target-relevant regions.
    Outcome. The backbone will be validated on dysarthric corpora (e.g., TORGO,
    UASpeech) and noisy benchmarks using PESQ, STOI, and SI-SNRi. Its novelty lies
    in disentangling content, impairment, and noise via back-projection and contrastive
    attention, going beyond CNN/Transformer backbones trained only on stable speech.
    Results will provide a robust unimodal foundation for multimodal modeling and be
    disseminated through IEEE/ACM TASLP, ICASSP, and JSLHR.
Task 2 (Month 11–18): Prosody-Preserving Multimodal Speech Enhancement
with Semantic Realignment
Dysarthric speech often exhibits delayed or weakened articulatory gestures, leading to
disrupted synchrony and semantic drift. Task 2 addresses this issue with a Detection–
Realignment Framework that detects layer-wise desynchronization and selectively
corrects inconsistent features. Focusing on local correction rather than global balancing,
it preserves prosody and yields semantically aligned multimodal representations.
Design Strategy. The proposed Detection–Realignment Framework operates in two
stages. At each encoder layer, latent features 𝑧𝑙 are projected into synchronization-
relevant and -irrelevant subspaces, yielding a synchronization-consistency ratio
                                         ‖𝒫 sync (𝑧𝑙 )‖
                        𝑅𝑙 =
                             ‖𝒫 sync (𝑧𝑙 )‖ + ‖𝒫 async (𝑧𝑙 )‖ + 𝜖
where 𝑧𝑙 denotes the latent multimodal representation at the 𝑙-th encoder layer, while
𝒫 sync (∙) and 𝒫 async (∙) are learned operators projecting 𝑧𝑙 into synchronization-
relevant and -irrelevant subspaces, respectively. The operator norm ‖∙‖ measures the
magnitude of each projection, and the small constant 𝜖 ensures numerical stability. A
high value of 𝑅𝑙 corresponds to the tightly coupled synchrony characteristic of healthy
speech, whereas a low value indicates audiovisual drift typical of dysarthria.
    Detection Module (Fig. 3a). This module evaluates the ratio 𝑅𝑙 at each layer.
     When 𝑅𝑙 falls below a threshold 𝜏 , the corresponding features are flagged as
     inconsistent with normative synchrony. In this way, the module provides a drift
     map that localizes dysarthria-specific audiovisual desynchrony across layers.
    Realignment Module (Fig. 3b). For the layers flagged by the Detection Module,
     let 𝑧𝑙𝑎 and 𝑧𝑙𝑣 denote the audio and visual sub-representations within 𝑧𝑙 and let
     ℎ represent the evolving fused latent estimate of clean speech. The module
     measures the similarity of each modality to ℎ , enhances the more reliable one
    through and applies a gated update only to inconsistent components of 𝑧𝑙𝑎 or 𝑧𝑙𝑣 .
Outcome. Evaluation will use audio-visual dysarthric data and pilot tests with 20–30
patients in rehabilitation settings. This framework is the first to explicitly detect and
locally correct dysarthria-induced audiovisual drift while preserving prosody, using
layer-wise cross-modal attention and gated updates. Results will be submitted to IEEE
TMM and AJSLP.
Task 3 (Month 19–24): Adaptive Multimodal Fusion for Real-World Deployment
Building on the stabilized features from Task 2, Task 3 targets real-world robustness
where noise, occlusion, and lighting degrade input quality. An Audio-Visual Balance
Module performs global reliability estimation and adaptive fusion, dynamically
weighting modalities by trustworthiness to ensure consistent performance across
environments and patient populations.
Design Strategy. The Audio-Visual Balance Module (AVBM) aims to enhance
robustness under real-world deployment conditions by performing global reliability-
aware fusion. It consists of two submodules:
   Ratio Calculation Module (Fig. 4b). At each encoder layer, audio features 𝑧𝑙𝑎
    and visual features 𝑧𝑙𝑣 are compared against a normative latent template ℎ̂𝑛𝑜𝑟𝑚
    derived from healthy corpora. A reliability ratio is computed as

                           exp⁡(sim(𝑧𝑙𝑚𝑜𝑑 , ℎ̂𝑛𝑜𝑟𝑚 ))
          𝑟𝑙 =                                                       ,⁡⁡⁡⁡⁡𝑙 ∈ {𝑎, 𝑣}
                 exp⁡(sim(𝑧𝑙𝑎 , ℎ̂𝑛𝑜𝑟𝑚 )) + exp⁡(sim(𝑧𝑙𝑣 , ℎ̂𝑛𝑜𝑟𝑚 ))

    where 𝑧𝑙𝑚𝑜𝑑 is the representation of either modality, sim(∙) is a semantic
    similarity function (e.g., cosine similarity), and the softmax normalization ensures
    𝑟𝑎 + 𝑟𝑣 = 1 . A larger 𝑟𝑙 indicates that the modality is more consistent with
    normative synchrony and thus more reliable under current conditions.
   Modal Balance Module (Fig. 4c). Based on the computed reliability scores
    (𝑟𝑎 , 𝑟𝑣 ), the module adaptively enhances the weaker modality. It constructs a fused
    attention map
                                 𝐴𝑊 = 𝛽𝐴𝑊𝑊 + (1 − 𝛽)𝐴𝑊𝐷 ,
    where 𝐴𝑊𝑊 and 𝐴𝑊𝑊 denote the self- and cross-similarity maps of the weak
    modality, and 𝛽 is dynamically determined from (𝑟𝑎 , 𝑟𝑣 ) , to reflect their
    imbalance. The enhanced weak feature is then obtained as 𝑓𝑊′ = 𝐴𝑊 𝑓𝑊 , which
    preserves intrinsic information while integrating cues from the dominant modality.
Outcome. Robustness will be assessed under noise, occlusion, and lighting variability,
with trials on embedded devices (ARM, Jetson, Edge TPU) and clinical validation on
50–100 patients. Its uniqueness lies in global reliability-aware fusion that dynamically
balances modalities, surpassing static fusion strategies. Outputs will be disseminated
through IEEE TPAMI, AAAI/ICLR, and IEEE JBHI.
Figure 1: The top row presents the architecture of Task 1. The topmost figure shows
the overall diagram that follows the encoder-decoder framework.




                Figure 2: Illustration of the contrastive self-attention.




Figure 3: Detection-Realignment Framework. (a) Detection Stage identifies misaligned
modules via fusion-irrelevant projections. (b) Realignment Stage corrects them through
intra-/inter-modal interactions with aggregated context.




Figure 4: The detailed architecture of (a) audio-visual balance module (AVBM), (b)
ration calculation module, and (c) modal balance module.
Neurogenic dysarthria, arising from stroke, Parkinson’s disease and other neurological
disorders, affects millions worldwide each year, leading to severe communication
barriers, social isolation, and long-term rehabilitation burdens. This project proposes
the first clinically informed AVSE framework tailored to dysarthric speech, enabling
clearer daily communication, supporting therapy and tele-rehabilitation, and reducing
healthcare costs. In the long term, it will contribute to reshaping digital rehabilitation
and improving quality of life for ageing populations.
1. Potential Beneficiaries
   Short term (1–3 years): Patients with dysarthria will gain immediate benefits from
    clearer and more natural speech output in everyday settings. Speech-language
    therapists will have access to more intelligible signals for diagnosis and remote
    therapy, while caregivers will face reduced communication burdens. Researchers
    in speech and clinical AI will benefit from released datasets and pretrained models.
   Medium term (4–10 years): Healthcare providers and rehabilitation centers will
    adopt enhanced tele-practice platforms, reducing travel and appointment
    bottlenecks. Technology companies developing smart microphones, mobile apps,
    or wearable devices will integrate the lightweight enhancement modules. Families
    and caregivers will experience greater independence of patients, improving
    household quality of life.
   Long term (>10 years): National healthcare systems and policymakers will
    benefit from scalable, cost-effective rehabilitation models, particularly as ageing
    populations increase the prevalence of neurological disorders. Society at large will
    gain from improved inclusion of people with speech disabilities, reduced stigma,
    and enhanced workforce participation.
2. Benefits Beyond Academia
The proposed research will generate broad benefits. (1) Patients will gain clearer, more
natural speech, reducing frustration, isolation, and depression while restoring
confidence; intelligibility improvements of 20–30% in standard clinical tests are
anticipated relative to current baselines. (2) Clinicians will access more reliable input
for assessment and therapy, particularly in tele-practice where intelligibility and
prosody are critical. (3) Healthcare systems will reduce costs and expand reach as
fewer in-person visits are needed and services extend to underserved populations, with
potential savings of 10–15% in per-patient rehabilitation expenditure. (4) Industry
will benefit from commercialization opportunities, with lightweight modules
deployable on embedded processors (ARM, Edge TPU, Jetson) for integration into
assistive devices and health platforms. (5) Society will see greater inclusivity of people
with disabilities, reduced stigma, and alignment with global digital health strategies
such as those of the WHO.
3. Actions to Maximize Impact
To ensure translation of outcomes into real-world benefit, the following actions will be
undertaken:
   Scientific pathway. Research outcomes will be published in leading journals
    (IEEE TPAMI, Nature Machine Intelligence) and presented at international
    conferences. Tutorials and workshops will train early-career researchers, while
    non-patented components will be shared as open-source software to accelerate
    academic adoption.
   Clinical pathway. A curated dysarthric audio–visual dataset and pretrained models
    will be released under academic licence. Pilot trials in rehabilitation centers will
    involve 50–100 patients across multiple hospitals, allowing iterative refinement
    based on patient and clinician feedback, with direct integration into tele-
    rehabilitation services to improve therapy effectiveness.
   Industrial pathway. Core modules will be protected by provisional patents and
    transferred via the university’s Technology Transfer Office. Advisory sessions with
    telehealth providers and device manufacturers will ensure technical designs meet
    deployment requirements (latency, energy use, privacy), paving the way for
    commercialization.
   Public pathway. Outreach through public lectures, media coverage, and
    stakeholder briefings will raise awareness of responsible AI for disability support.
    Policy engagement will highlight the societal value of impairment-aware speech
    enhancement, while long-term partnerships will align outcomes with national and
    global digital health strategies.
4. Risks and Mitigation
While the project is designed to deliver broad impact, several potential risks must be
considered and addressed. (1) Limited clinical data may restrict the generalizability of
the models; this will be mitigated by forming collaborations with multiple hospitals and
rehabilitation centers to ensure sufficient and diverse data collection. (2) Patient
variability and compliance could affect system performance, given differences in
severity and articulation; this risk will be managed through personalized calibration
modules and inclusive evaluation protocols covering a wide range of impairment levels.
(3) Hardware deployment constraints, such as latency and energy consumption, may
hinder portability; the project addresses this through lightweight model design and early
validation on embedded devices (e.g., ARM, Jetson, Edge TPU). (4) Privacy and
ethical concerns are inherent in clinical applications; these will be mitigated by strict
anonymization, on-device processing to avoid cloud transfer, and full compliance with
medical ethics approvals.
This project pioneers impairment-aware, clinically grounded speech enhancement.
In the short term, patients and clinicians will benefit directly; in the medium term,
integration into services and devices will expand accessibility; in the long term,
systemic healthcare and societal benefits will emerge. By combining scientific
dissemination, clinical translation, industrial transfer, and public engagement, the
research ensures lasting impact beyond academia.
[1] Wujian, Y., Yingcong, Z., Yuehai, C., Yijun, L., & Zhiwei, M. (2024). Post-Stroke
Dysarthria Voice Recognition based on Fusion Feature MSA and 1D. Computer Methods in
Biomechanics and Biomedical Engineering, 1-11.

[2] Freed, D. B. (2023). Motor speech disorders: Diagnosis and treatment. plural publishing.

[3] Brady, M. C., Clark, A. M., Dickson, S., Paton, G., & Barbour, R. S. (2011). The impact of
stroke-related dysarthria on social participation and implications for rehabilitation. Disability
and rehabilitation, 33(3), 178-186.

[4] Baylor, C., Eadie, T., & Yorkston, K. (2021, June). The Communicative Participation Item
Bank: Evaluating, and reevaluating, its use across communication disorders in adults.
In Seminars in speech and language (Vol. 42, No. 03, pp. 225-239). Thieme Medical Publishers,
Inc..

[5] Mahler, L. A., Ramig, L. O., & Fox, C. (2015). Evidence-based treatment of voice and
speech disorders in Parkinson disease. Current opinion in otolaryngology & head and neck
surgery, 23(3), 209-215.

[6] Hill, A. J., Theodoros, D. G., Russell, T. G., & Ward, E. C. (2009). The redesign and re-
evaluation of an internet-based telerehabilitation system for the assessment of dysarthria in
adults. Telemedicine and e-Health, 15(9), 840-850.

[7] Pandey, A., & Wang, D. (2019). A new framework for CNN-based speech enhancement in
the time domain. IEEE/ACM Transactions on Audio, Speech, and Language Processing, 27(7),
1179-1188.

[8] Fu, S. W., Tsao, Y., Lu, X., & Kawai, H. (2017, December). Raw waveform-based speech
enhancement by fully convolutional networks. In 2017 Asia-Pacific Signal and Information
Processing Association Annual Summit and Conference (APSIPA ASC) (pp. 006-012). IEEE..

[9] Sun, L., Du, J., Dai, L. R., & Lee, C. H. (2017, March). Multiple-target deep learning for
LSTM-RNN based speech enhancement. In 2017 Hands-free Speech Communications and
Microphone Arrays (HSCMA) (pp. 136-140). IEEE.

[10] Gao, T., Du, J., Dai, L. R., & Lee, C. H. (2018, April). Densely connected progressive
learning for lstm-based speech enhancement. In 2018 IEEE International Conference on
Acoustics, Speech and Signal Processing (ICASSP) (pp. 5054-5058). IEEE.

[11] Li, Y., Sun, Y., Wang, W., & Naqvi, S. M. (2023). U-shaped transformer with frequency-
band aware attention for speech enhancement. IEEE/ACM Transactions on Audio, Speech, and
Language Processing, 31, 1511-1521.

[12] Zhang, Q., Ge, M., Zhu, H., Ambikairajah, E., Song, Q., Ni, Z., & Li, H. (2024, April). An
empirical study on the impact of positional encoding in transformer-based monaural speech
enhancement. In ICASSP 2024-2024 IEEE International Conference on Acoustics, Speech and
Signal Processing (ICASSP) (pp. 1001-1005). IEEE.

[13] Zhang, Q., Wang, D., Zhao, R., Yu, Y., & Shen, J. (2021). Sensing to hear: Speech
enhancement for mobile devices using acoustic signals. Proceedings of the ACM on Interactive,
Mobile, Wearable and Ubiquitous Technologies, 5(3), 1-30.

[14] Sun, Z., Li, Y., Jiang, H., Chen, F., Xie, X., & Wang, Z. (2020). A supervised speech
enhancement method for smartphone-based binaural hearing aids. IEEE Transactions on
Biomedical Circuits and Systems, 14(5), 951-960.

[15] Botinhao, C. V., Wang, X., Takaki, S., & Yamagishi, J. (2016, September). Investigating
RNN-based speech enhancement methods for noise-robust text-to-speech. In 9th ISCA speech
synthesis workshop (pp. 159-165).

[16] Dubey, H., Aazami, A., Gopal, V., Naderi, B., Braun, S., Cutler, R., ... & Aichner, R.
(2024). Icassp 2023 deep noise suppression challenge. IEEE Open Journal of Signal
Processing, 5, 725-737.

[17] Loizou, P. C., & Kim, G. (2010). Reasons why current speech-enhancement algorithms do
not improve speech intelligibility and suggested solutions. IEEE transactions on audio, speech,
and language processing, 19(1), 47-56.

[18] Ephrat, A., Mosseri, I., Lang, O., Dekel, T., Wilson, K., Hassidim, A., ... & Rubinstein, M.
(2018). Looking to listen at the cocktail party: a speaker-independent audio-visual model for
speech separation. ACM Transactions on Graphics (TOG), 37(4), 1-11.

[19] Liu, D., Zhang, T., Christensen, M. G., Yi, C., & An, Z. (2024). Audio-visual fusion with
temporal convolutional attention network for speech separation. IEEE/ACM Transactions on
Audio, Speech, and Language Processing.

[20] Xu, X., Tu, W., & Yang, Y. (2025). Efficient audio–visual information fusion using
encoding pace synchronization for Audio–Visual Speech Separation. Information Fusion, 115,
102749.

[21] Li, K., Xie, F., Chen, H., Yuan, K., & Hu, X. (2024). An audio-visual speech separation
model inspired by cortico-thalamo-cortical circuits. IEEE Transactions on Pattern Analysis
and Machine Intelligence, 46(10), 6637-6651.

[22] Bhat, C., & Strik, H. (2025). Speech technology for automatic recognition and assessment
of dysarthric speech: An overview. Journal of Speech, Language, and Hearing Research, 68(2),
547-577.

[23] Pierotti, F., & Bandini, A. (2025). Multimodal Assessment of Speech Impairment in ALS
Using Audio-Visual and Machine Learning Approaches. arXiv preprint arXiv:2505.21093.

[24] Qian, Z., Xiao, K., & Yu, C. (2023). A survey of technologies for automatic Dysarthric
speech recognition. EURASIP Journal on Audio, Speech, and Music Processing, 2023(1), 48.

[25] Borrie, S. A. (2015). Visual speech information: A help or hindrance in perceptual
processing of dysarthric speech. The Journal of the Acoustical Society of America, 137(3),
1473-1480.
My research experience has consistently centered around speech signal processing,
with a sustained focus on improving robustness under degraded acoustic conditions and
expanding enhancement strategies to multimodal contexts. Over the course of
postgraduate training and full-time research appointments, I have built a cohesive
trajectory that bridges fundamental modeling with practical system development.
My early research began with the study of voice anti-spoofing in speaker verification.
The aim was to detect and mitigate playback and synthetic speech attacks that can
deceive biometric systems. I examined the generalization limitations of traditional
acoustic features under unseen channel conditions and explored supervised methods for
detecting adversarial inputs. This initial work offered foundational insights into speech
signal vulnerability and motivated my later research on robust signal recovery.
During my doctoral training, I transitioned to deep learning-based monaural speech
enhancement. My efforts centered on developing end-to-end systems that reconstruct
clean speech from mixtures contaminated by non-stationary noise and reverberation. I
focused on encoder–decoder architectures and attention-based modules to capture both
local and global temporal–spectral dependencies. These systems were evaluated on
standard datasets to assess gains in intelligibility and perceptual quality. This stage
honed my skills in network design, noise modeling, and performance benchmarking
under challenging acoustic conditions.
Building on these foundations, I moved into audio-visual speech enhancement (AVSE)
to address the limitations of audio-only models in real-world applications. Visual cues,
such as lip movements and facial dynamics, offer temporally stable, noise-invariant
information, particularly valuable in low-SNR conditions. My research explored cross-
modal representation learning and addressed challenges such as modality misalignment
and unbalanced signal quality. I contributed to the design of adaptive multimodal fusion
strategies that regulate information flow based on reliability cues, drawing inspiration
from perceptual mechanisms like attention reweighting and semantic prediction. This
work advanced cognitively inspired architectures for robust multimodal speech
enhancement.
In addition to academic research, I have participated in multiple applied projects in full-
time research and engineering settings. These experiences broadened my understanding
of system-level design and deployment under real-time constraints. Three
representative projects include:
1. Nonlinear Level-Based Acoustic Echo Adaptive Cancellation: This project
   focused on speech enhancement under severe acoustic distortion. I contributed to
   the design of a contrastive spectral modeling framework that separates speech-
   relevant and interference-prone regions in the time–frequency domain. In addition,
   I assisted in developing fusion strategies that incorporate visual signals such as lip
   motion to guide reconstruction. These efforts informed later exploration of dual-
   branch encoders and contrastive attention mechanisms for residual decomposition.
2. Acoustic Scene Depth Analysis for Intelligent Service Robots: The objective of
   this project was to enable robust voice interaction in multi-source, dynamic
   environments. I worked on the modeling of spatial information through audio–
   visual representation learning, including inter-channel attention and spatial-aware
   encoding. The system was tested in highly reverberant robotic scenarios. My work
   revealed challenges in modality mismatch and led to the exploration of semantic
   realignment techniques under cross-modal inconsistency.
3. Multimodal Health Monitoring System Based on Infant Sounds: This project
   addressed speech enhancement in healthcare settings, where both audio and visual
   signals tend to be low quality. I developed front-end enhancement modules capable
   of suppressing ambient noise while retaining subtle vocal cues critical for
   downstream health assessment. A key contribution was the co-design of lightweight
   models suitable for embedded deployment. The experience reinforced the
   importance of reliability-aware fusion and dynamic control of multimodal
   information pathways.
Across all stages of this research trajectory, I have developed an integrated expertise in
speech enhancement that spans single-modality processing, multimodal learning, and
cognitively inspired modeling. My work combines theoretical insight with practical
feasibility, contributing toward the development of robust and deployable speech
systems across a variety of application domains.
Throughout my academic and professional journey, I have cultivated a diverse range of
leadership experiences spanning research coordination, student mentorship, academic
service, and community engagement. These roles reflect not only technical expertise
but also organizational acumen and a long-standing commitment to knowledge sharing
and collaborative development.
1. Laboratory Leadership and Research Coordination
In a sustained leadership role within my research laboratory, I was responsible for
overseeing the daily operation and long-term strategic planning of a multidisciplinary
research team. My duties included allocating project resources, monitoring research
progress, organizing internal seminars, and establishing clear task dependencies to
promote efficient collaboration. he laboratory under my coordination was recognized
multiple times with institutional-level Outstanding Research Lab awards, in part due to
the structured work culture, proactive knowledge-sharing mechanisms, and consistent
research deliverables. I also led the laboratory’s technical direction in speech
enhancement. One notable project involved developing a Nonlinear Level-Based
Acoustic Echo Adaptive Cancellation system. I guided the team through the entire
lifecycle of the system, from initial network design and modular decomposition to full
technical implementation and system validation. My role involved high-level technical
supervision, progress troubleshooting, and milestone alignment to ensure that all
deliverables met quality and timeline expectations.
2. Mentorship and Student Development
Over the past several years, I have mentored undergraduate and postgraduate students
involved in speech processing research. This mentorship extended beyond technical
support to include academic writing, conference preparation, and long-term skill
development. conducted regular one-on-one discussions to help students refine their
research hypotheses, interpret experimental results, and articulate contributions clearly.
I also organized mini-tutorials within the group on selected topics such as attention
mechanisms, spectral analysis, and training diagnostics, tailored to student needs.
Through structured guidance and motivational support, two of the students I mentored
received the Outstanding Master's Thesis award at the school level.
3. Community Engagement and Volunteer Service
In addition to academic commitments, I have engaged in multiple volunteer and
outreach activities aimed at promoting technical literacy and building inclusive
academic environments. These include: (1) Volunteering at AI-related outreach events
targeting local high school students, where I helped design and demonstrate real-time
voice denoising systems to spark interest in speech technology and signal processing.
(2) Assisting in organizing internal university coding contests focused on audio-visual
learning, including problem set design, evaluation metrics, and grading automation
setup. (3) Participating in local academic open days, where I explained the societal
relevance of speech enhancement technologies to non-specialist audiences and
answered questions from parents, students, and educators.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars            (to be completed by the nominee)
Name of Nominee :       (Surname) Xu
                        (Other name) Xinmeng
Host Faculty / School / Department / Faculty of Engineering / Department of Systems
Division / Unit ^ :                  Engineering and Engineering Management

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I got to know Mr. Xinmeng Xu in March 2022, when he contacted me about applying for my Ph.D. program.
After intense competition, he was officially admitted to the National Multimedia Software Engineering
Technology Research Center at Wuhan University in September of the same year and became my Ph.D. student.
As his doctoral advisor, I have worked closely with him throughout his doctoral journey, guiding his research on
speech enhancement and mentoring his academic development.




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                               Excellent     Very Good        Good                Average          No basis
                               (top 5%)      (top 6-20%) (top 21-50%)            or below            for
                                                                                (lower than       judgment
                                                                                   50%)
Intellectual capacity                            ☐                 ☐                 ☐               ☐
                                                   ☐                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                   
                                                   ☐                ☐                 ☐               ☐
Knowledge of proposed
research study
                                   

                                                   ☐                ☐                 ☐               ☐
Analytical power and
reasoning
                                   
Judgment                           ☐                               ☐                 ☐               ☐
                                                   ☐                ☐                 ☐               ☐
Imagination and
originality
                                   

                                                                    ☐                 ☐               ☐
Motivation and
perseverance
                                                 ☐
                                                                    ☐                 ☐               ☐
Skills of writing and
argumentation
                                                 ☐
                                                                    ☐                 ☐               ☐
Capacity for independent
work
                                                 ☐
                                                                    ☐                 ☐               ☐
Reliability and sense of
responsibility
                                   ☐               
Leadership experience              ☐                              ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
Throughout his doctoral studies, Mr. Xinmeng Xu has demonstrated outstanding intellectual capacity in pursuing
his research. He has a remarkable ability to understand, quickly grasping even complex concepts and ideas. His
learning capacity is exceptional, as he excels in critical thinking and summarizing, which deepens his
understanding. With a meticulous approach to thought, he displays clear logical reasoning and a sharp focus on
key aspects when analyzing problems. His ability to break down intricate issues with precision and clarity sets
him apart as an insightful and quick-thinking individual.

Xinmeng has built a solid research foundation in the field of speech enhancement, with his work encompassing
both unimodal speech enhancement and audio-visual speech enhancement. He has conducted a systematic
analysis of the key challenges in speech enhancement under complex scenarios, proposing and validating a series
of innovative solutions that advanced both theoretical understanding and practical applicability of speech
enhancement. His research outputs have been published in highly regarded venues, including ICLR, AAAI, IEEE
Transactions on Multimedia, Speech Communication, and Information Fusion. Such accomplishments are a
strong testament to his ability to contribute cutting-edge work at the international level.


                                                       3
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Equally impressive is Xinmeng’s ability to work creatively and autonomously. He consistently identifies
meaningful research problems, develops rigorous solutions, and validates them through thorough
experimentation. His problem-driven mindset, combined with technical mastery, continuously make progress in
his field of expertise. Importantly, he is not only technically strong but also highly collaborative. He has actively
engaged in joint projects, mentored junior peers in the group, and contributed to building a strong academic culture
within our laboratory.

As his doctoral advisor, I have had the privilege of observing his growth from a motivated student into a confident
and capable independent researcher. I am fully confident that Mr. Xu will thrive as a Junior Research Fellow and
make significant contributions to the academic community.

I therefore strongly and without reservation recommend Mr. Xinmeng Xu for the Junior Research Fellow Scheme.
He is an exceptional candidate with the qualities, skills, and ambition required to excel, and I am certain he will
prove to be a valuable asset to any host institution.




                                                         4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: WEIPING TU                                      Title: Prof
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Wuhan University
 Position: Professor
 E-mail Address: <EMAIL>
 Telephone Number#: +86-13971500069
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date:        8/27/2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      5
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
l    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
l    The nominee should complete Part A of the referee’s report.
l    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
l    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
l    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A     Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Xu
                        (Other name) Xinmeng
Host Faculty / School / Department / Faculty of Engineering, Dept of Systems Engineering and
Division / Unit ^ :                  Engineering Management

^ Please do not mention the name of the supporting university


Part B     Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
l   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
l   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
l   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
l   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
l   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
l   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
l   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?
I have known Mr. Xinmeng Xu since September 2022, when he joined our research group as a Ph.D. student in
Computer Science and Technology at Wuhan University. In my capacity as a faculty member of the group, I have
had the opportunity to closely observe his academic development, research progress, and overall professional
growth. Over the past three years, I have interacted with him regularly through research discussions, supervision
of projects, and academic seminars, which has given me a clear understanding of his abilities, commitment, and
potential as an independent researcher.




                                                        2
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent      Very Good        Good                 Average          No basis
                                (top 5%)       (top 6-20%) (top 21-50%)             or below            for
                                                                                   (lower than       judgment
                                                                                      50%)
Intellectual capacity                R               ☐                 ☐                 ☐               ☐
Ability for conducting
scholastic research
                                     R               ☐                 ☐                 ☐               ☐
Knowledge of proposed
research study
                                     R               ☐                 ☐                 ☐               ☐
Analytical power and
reasoning
                                     R               ☐                 ☐                 ☐               ☐
Judgment                             ☐               R                 ☐                 ☐               ☐
Imagination and
originality
                                     R               ☐                 ☐                 ☐               ☐
Motivation and
perseverance
                                     R               ☐                 ☐                 ☐               ☐
Skills of writing and
argumentation
                                     R               ☐                 ☐                 ☐               ☐
Capacity for independent
work                                 ☐               R                 ☐                 ☐               ☐
Reliability and sense of
responsibility
                                     R               ☐                 ☐                 ☐               ☐
Leadership experience               ☐                R                ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
I have been working with Mr. Xinmeng Xu since he began his doctoral study in 2022, and I have had frequent
opportunities to guide him in research discussions, project design, and manuscript preparation. From this
experience, I can confirm that he has developed strong research abilities and is steadily growing into an
independent scholar.

Mr. Xu demonstrates a solid command of both the theoretical foundations of signal processing and the practical
implementation of deep learning methods. Under my guidance, he has taken the lead in formulating new
approaches for audio-visual speech enhancement, with an emphasis on robustness under degraded input
conditions and efficiency for real-world use. For example, I have observed how he refined model designs through
careful ablation studies, and how he paid attention not only to performance metrics but also to interpretability and
computational cost. This balance between innovation and practicality is one of his notable strengths.

He has also shown promising potential in carrying research projects to completion. He is able to take initial ideas,
develop them into concrete algorithms, and then present the results clearly in publications. His papers accepted at
international conferences and journals demonstrate that he can meet the standards of the broader academic
                                                         3
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
community. Importantly, I have seen that he is open to feedback, willing to revise his ideas, and capable of
integrating suggestions into improved solutions. This attitude has allowed him to make steady progress during his
doctoral training.

Beyond technical skills, Mr. Xu is diligent, self-motivated, and communicates well with colleagues. In group
meetings, he can explain his work clearly and respond thoughtfully to questions. His earlier study and work
experience abroad have also given him confidence in engaging with international peers, which will be valuable
for his future career. In my view, Mr. Xu is a capable and promising young researcher. He already has a track
record of meaningful contributions, and with continued experience, I believe he will be able to establish himself
as an independent academic in his field. These qualities make him a strong candidate for the JRFS.




                                                        4
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                  R      Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: YUHONG YANG                                     Title: Dr
                             (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*
     University / Organisation: Wuhan University
     Position: Associate Professor
     E-mail Address: <EMAIL>
     Telephone Number#: +86-18971461943
     (# Please provide country code and area code, e.g. +86-10-1234567)
l     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
    R I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
    R I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:
                     Ynhong Yang                                 Date:      27        August          ,
                                                                                                          2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          5
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
