Peng <PERSON> | 雷鹏
Tele: +852 91428804 | Email: <EMAIL> | Date of birth: 1998.4
Department: Department of Electronics Engineering, The Chinese University of Hong Kong
Research direction: Integrated Photonics, Integrated Acousto-optic Interaction
                                                                   Education
BEIJING UNIVERSITY OF POST AND TELECOMMUNICATIONS                                                                         Beijing
School of Information and Communication Engineering | Bachelor        Supervisor: Prof<PERSON> <PERSON>           2015.09 – 2019.07
                                                                                                           Rank:8/181
PEKING UNIVERSITY                       State Key Laboratory of Advanced Optical Communication Systems and Networks       Beijing
School of Electronics | PhD                   Supervisor: Prof. <PERSON>                                 2019.09 – 2024.07
                                                                   Overview
 • During my doctoral studies, I have participated in several key research and development projects, contributing to the development
   of two patents. I have published a total of 15 papers, with 5 as the first author, including publications in SCI journals such as Nature
   Communications, Optics Letters, and Photonics Journal.
 • My expertise extends to the construction of communication systems, microwave photonics wireless systems, and proficiency in
   cleanroom fabrication techniques. Additionally, I possess adeptness in utilizing diverse simulation software, including Matlab,
   Comsol, and Lumerical.
                                                              Research Project
Design and Integration of High-Speed Low-Jitter Light Sources and Wideband High-Linearity Optoelectronic Device Arrays
2020.01-2022.12
Exploration of Chip-scale Ultra-low Phase Noise Microwave Signal Extraction Based on Optical Frequency Comb
2020.11-2022.11
High-Frequency Wideband Photonic ADC Based on Ultra-Low-Noise Optical Sampling
2019.09-2021.12
                                                                     Papers
I have authored a total of 15 publications, including 5 as the first author.
Journal papers
[1] Lei, P., Xu, M., Bai, Y., Chen, Z., & Xie, X. (2024). Anti-resonant acoustic waveguides enabled tailorable Brillouin scattering on chip. Nature
     Communications, 15(1), 3877.
[2] Lei, P., Xu, M., Bai, Y., Chen, Z., & Xie, X. (2024). Loading-effect-based three-dimensional microfabrication empowers on-chip Brillouin
     optomechanics. Optics Letters, 49(6), 1465-1468.
[3] Lei, P., Zhang, C., Xu, M., He, B., Peng, H., Xie, X., & Chen, Z. (2021). Manipulating Stimulated Brillouin Scattering Produced Gain Near Its
     Threshold via Slow Intensity Modulation. IEEE Photonics Journal, 13(5), 1-5.
[4] Xu, M., Lei, P., Bai, Y., Chen, Z., & Xie, X. (2024). Enhancement of Brillouin nonlinearities with a coupled resonator optical waveguide.
     Optics Letters, 49(16), 4541-4544.
[5] Xu, M., Lei, P., Bai, Y., Chen, Z., & Xie, X. (2024). Slow-light-enhanced Brillouin scattering with integrated Bragg grating. Optics Letters,
     49(8), 2177-2180.
[6] Peng, H., Lei, P., Xie, X., & Chen, Z. (2022). Photonic RF Synthesizer Based on a Phase-Locked Optoelectronic Oscillator Using Anti-Stokes
     Loss Spectrum of Stimulated Brillouin Scattering. IEEE Photonics Journal, 14(3), 1-8.
[7] Peng, H., Lei, P., Xie, X., & Chen, Z. (2021). Dynamics and timing-jitter of regenerative RF feedback assisted resonant electro-optic frequency
     comb. Optics Express, 29(26), 42435-42456.
[8] Zhang, C., Lei, P., Liu, R., He, B., Chen, Z., Xie, X., & Hu, W. (2021). Large-scale true-time-delay remote beamforming with EO frequency
     combs and multicore fiber. Optics Letters, 46(15), 3793-3796.
[9] Zhang, C., Gao, Y., Zuo, M., Lei, P., Liu, R., He, B., ... & Hu, W. (2022). Using ASE sources in remote beamforming system with Space-
     Division-Multiplex fiber. Optics Communications, 504, 127477.
[10] Guo, R., Li, Y., Zhang, C., Lei, P., Yin, Q., Xie, X., & Chen, Z. (2020). Broadband photonic down-conversion preprocessor for ADC with
     arcsine equalization. IEEE Photonics Journal, 12(3), 1-16.

Conference papers
[1] Lei, P., Xu, M., Bai, Y., Chen, Z., & Xie, X. (2024, May). Manipulating Brillouin Scattering on Chip Using Suspended Anti-resonant Acoustic
    Waveguides. In CLEO: Science and Innovations (pp. SF2O-4). Optica Publishing Group.
[2] Lei, P., Xu, M., Liu, R., He, B., Chen, Z., & Xie, X. (2022, November). Optimization of Suspended Phononic Crystal Brillouin Waveguides
    Using Genetic Algorithm. In 2022 Asia Communications and Photonics Conference (ACP) (pp. 1629-1632). IEEE.
[3] Xu, M., Lei, P., Bai, Y., Chen, Z., & Xie, X. (2024, May). Slow Light Enhanced On-chip Stimulated Brillouin Scattering. In CLEO:
    Fundamental Science (pp. JTh2A-30). Optica Publishing Group.
[4] Peng, H., Lei, P., Xie, X., & Chen, Z. (2021, July). Low Phase Noise X-Band Frequency Synthesizer Based on a Phase-Locked Brillouin
    Optoelectronic Oscillator. In Optoelectronics and Communications Conference (pp. JS3C-4). Optica Publishing Group.
[5] He, B., Zhang, C., Liu, R., Lei, P., Chen, Z., & Xie, X. (2022, November). High Resolution Parallel Coherent Laser Ranging Using an EO
    Frequency Comb. In 2022 Asia Communications and Photonics Conference (ACP) (pp. 1698-1700). IEEE.

                                                                    Skills
Integrated Photonics Advanced Fabrication
 • Proficient in cleanroom fabrication techniques, regularly conducting chip fabrication at facilities including the Semiconductor
   Research Institute of the Chinese Academy of Sciences, the Micro/Nano Technology Laboratory of Tsinghua University, and
   the Ministry of Education Key Laboratory of Nanodevice and Nanochemistry at Peking University.
 • Experienced in subcontracting tasks involving ultraviolet photolithography, reactive ion etching, magnetron sputtering, metal
   evaporation coating, chemical vapor deposition, and other equipment. Skilled in operating equipment such as electron beam
   lithography machines, inductively coupled plasma etching systems, scanning electron microscopes, atomic force microscopes, and
   step profilers.
 • Proficient in using layout design processes for silicon-based optical devices based on Ruby and Python


Microwave Photonics Experimental System
 • Proficient in independently assembling optical cavity PDH locking systems, coherent optical detection systems, optoelectronic
   hybrid oscillator systems, and integrated photonics testing platforms.
 • Skilled in utilizing various Measurement Instruments such as arbitrary waveform generators, oscilloscopes, spectrometers,
   frequency analyzers, noise analyzers, high-frequency signal sources, vector network analyzers, etc.
 • Proficient in constructing Automated Measurement Systems for controlling various measurement devices based on software such
   as MATLAB, LABVIEW, etc.
Multi-Physics Simulation and Numerical Computation
 • Proficient in utilizing MATLAB combined with COMSOL, FDTD Lumerical, and other physics simulation software to simulate
   multidimensional material structures, encompassing optical, acoustic, electrical, and thermal fields.
 • Skilled in employing optimization and fitting algorithms such as Particle Swarm Optimization (PSO), Genetic Algorithm (GA),
   Simulated Annealing, and Neural Network algorithms.
 • Capable of proficiently solving numerical methods including Finite Element Method (FEM) and Finite Difference Method (FDM)
   using software like COMSOL, MATLAB, and Mathematica.
Microcontroller Interfaces and Robotics Automation
 • Participated in electronics design and robotics automation competitions during undergraduate studies, gaining proficiency in
   programming Microcontrollers (STM32, MSP430), FPGAs (Xilinx), and various sensors, digital signal processors, and drive
   modules (brushless and brushed motors). Skilled in programming for interconnection and control among these components using
   various communication protocols (SPI, Usart, I2C, DMA, etc.).
 • Experienced in controlling robotic motors, cylinders, and other transmission devices to maintain stability, as well as in building
   optical cavity PDH locking systems during graduate studies. Proficient in tuning PID algorithm parameters and Kalman filter
   weighting parameters.
Auxiliary Skills
 • Proficient in image processing and video production, with extensive experience in editing and post-production tools such as
   Premiere Pro (PR), After Effects (AE), and Photoshop (PS).
 • Skilled in scientific research project application, follow-up, acceptance, and conclusion related tasks.
                                                                   Awards
    Excellent Research Award, Peking University (2020)
    Outstanding Graduate of Beijing (2019)
    Meritorious Student, Beijing University of Posts and Telecommunications (2016, 2017)
    First-Class Scholarship, Beijing University of Posts and Telecommunications (2019,2016)
Title: Advancing Phononic Integrated Circuits with Novel Phoxonic Devices
Abstract of Research
Modern technological infrastructure, including communication systems and consumer
electronics, is increasingly dependent on integrated circuits (ICs) and, more recently, photonic
integrated circuits (PICs). ICs are fundamental to electronic devices, yet they face growing
challenges in power efficiency and thermal management as telecommunications technologies
evolve and data-intensive applications proliferate. On the other hand, PICs, which manipulate
light rather than electrons, provide superior speed and bandwidth. However, they encounter
difficulties with integration density and flexibility in signal manipulation at smaller scales.
This research introduces phononic integrated circuits (PnICs), which utilize acoustic waves to
potentially overcome some of the key limitations faced by both ICs and PICs. Phononic circuits,
leveraging the significantly slower velocities of acoustic waves compared to electromagnetic
waves, present a promising avenue for precise temporal control and extended interaction times,
which are crucial for high-speed signal processing and emerging quantum technologies.
Our project aims to develop innovative "phoxonic" devices capable of manipulating both
photons and phonons on the same chip. This dual functionality simplifies integration and
enhances performance by confining both acoustic and optical waves within novel phoxonic
waveguides, thus boosting photon-phonon interactions. Additionally, we plan to develop key
phononic devices to further advance the field of PnICs, such as a narrow-linewidth integrated
phonon laser that utilizes resonant structures within phononic circuits to generate and amplify
coherent phonons, offering an acoustic counterpart to optical lasers. The research also explores
a CMOS-compatible microfabrication technique that employs loading effects to achieve a
three-dimensional structure with fewer fabrication steps, potentially simplifying the
manufacturing process and reducing costs. We are also designing novel measurement and
characterization methods to accurately assess the performance of these phononic devices,
focusing on phonon frequency, photon-phonon coupling, and mechanical quality factors.
This project not only seeks fundamental advancements in the field of phononic circuits but also
aims to pave the way for their practical implementation across various applications, including
advanced telecommunications and hybrid quantum systems. By integrating phononic and
photonic elements, this research promises to address significant challenges currently facing
electronics and photonics technologies, marking a substantial step forward in the development
of next-generation integrated circuits.
Proposed Objectives
1. To design and optimize a novel phoxonic waveguide.
2. To develop a comprehensive theoretical framework for an integrated phonon laser.
3. To fabricate and demonstrate novel phoxonic devices on-chip.
4. To design and implement characterization methods for phoxonic devices.

Reasons for undertaking the research
 Phononic circuits feature slower propagation speeds and strong interactions with matter.
  These characteristics facilitate more manageable manipulation and processing of on-chip
  signals.
 Current phononic waveguides face significant challenges related to material compatibility,
  fabrication complexity, and optomechanical performance.
 Phononic devices lag in maturity and variety compared to electronic and photonic devices.
  There is a clear need to develop a broader range of phononic devices, including phononic
  source, waveguides, and scalable couplers, to build fully functional phononic circuits.
 The inherent strong interaction of acoustic waves with matter and light makes them ideal for
  integrating with electrical and optical circuits. This integration could lead to new types of
  hybrid devices and systems that leverage the strengths of both phononic and
  photonic/electronic technologies.

Key issues and problems being addressed
 Acoustic waves in integrated circuits tend to leak into the silica substrate due to the higher
  propagation speed of common waveguide materials compared to the wafer insulator. Current
  confinement methods often require complex structures or materials that are incompatible
  with standard integrated photonic platforms, limiting their practical implementation.
 Gigahertz acoustic waves in integrated waveguides exhibit high sensitivity to sidewall
  roughness and shape distortion. Existing fabrication techniques struggle to achieve the
  precision required to minimize these losses, necessitating significant improvements in
  manufacturing processes.
 This project aims to develop a novel phoxonic waveguide capable of confining both acoustic
  and optical waves simultaneously. This innovative approach will serve as the foundation for
  designing and optimizing integrated phonon lasers based on photon-phonon interaction,
  addressing the need for efficient phonon sources in phononic circuits.
 To enhance device quality and yield, this research will explore innovative fabrication
  methods, such as loading-effect-based 3D fabrication. These techniques aim to overcome
  current limitations in manufacturing precision and efficiency

Potential impacts in terms of academic development and/or practical applications
 Develop low-loss, customizable phoxonic waveguides to enhance integrated phononic
  circuit technology. Utilize these innovative waveguides to pioneer new methods for phonon
  lasing and improve noise analysis in phononic systems.
 Innovate micro- and nano-scale integration techniques for phoxonic devices to boost
  fabrication efficiency and increase yield rates.
 Facilitate the development of more efficient and compact systems for hybrid photonic-
  phononic integrated circuits.
 Create high-performance devices for advanced signal processing and post-5G
  communication systems, such as acoustic wave filters and phase shifters.
Background of Research
In recent decades, large-scale integrated circuits have become fundamental to numerous facets
of human society, including communication systems and various consumer electronics.
However, as we progress further into the era of big data, artificial intelligence, and advanced
telecommunications beyond 5G, the limitations of current semiconductor chips are becoming
evident. These constraints include micro-fabrication precision, power efficiency, and chip
thermal management [1-2].
To overcome these barriers, the last decade has witnessed the rise of photonic integrated
circuits (PICs). These circuits exploit the inherent benefits of light, such as minimal loss,
rapid propagation speeds, high operational frequencies, and extensive bandwidth capabilities.
Optoelectronic chips, particularly, have demonstrated their potential for facilitating
high-speed parallel signal processing and computations while minimizing power consumption
and overheating risks [3-5].
Despite significant advances, both electronic and photonic integrated circuits encounter
specific challenges. First, the rapid propagation speeds of both electrical and optical signals
complicate the precise temporal control over chip-scale processes across extended durations.
Certain signal processing applications, such as high-frequency narrow-bandwidth filters,
true-time-delay units, phase shifters, and optical storage for photonic computing, necessitate
time delays on the order of tens of nanoseconds. Achieving such delays is challenging for
both electrical and optical signals on centimeter-scale chips [6]. Second, although electrical
signals propagate at speeds comparable to those of light, their frequency ranges are
considerably lower (by approximately 1000-10,000 times), leading to a significant mismatch
in wavelength and wave-vector when compared to optical waves.
Phononic circuits present an effective solution and complement to optoelectrical chips.
Acoustic waves travel at velocities approximately 100,000 times slower than electromagnetic
waves, enabling more manageable on-chip signal manipulation and processing, facilitating the
applications such as micromechanical sensors [7-8], optical storage [9-10], and acoustic wave
filters [11]. Additionally, acoustic waves can reach frequencies similar to those of electrical
signals (MHz to GHz range) while maintaining significantly slower velocities. This unique
attribute allows for the realization of wave-vector and wavelength characteristics similar to
those of optical waves. With their inherent strong interaction with matter and physical fields,
acoustic waves can effectively bridge electrical and optical circuits [12]. Furthermore,
phonons are emerging as potent candidates for processing and transducing quantum
information. Their capability to serve as versatile quantum transducers enhances their
potential to link various quantum modalities within a quantum network [13].
Previous studies on phononic circuits in integrated platforms have predominantly focused on
localized resonant phonons [7-8, 14-15]. However, this research focuses on hypersound
propagating waves operating at gigahertz frequencies [12-13]. This frequency range is critical
for high-speed signal processing, advanced post-5G communication systems, and maintaining
long decoherence times in phonons for hybrid quantum systems [12-13,16]. Additionally, the
high-frequency longitudinal acoustic waves exhibit extended propagation lengths, meeting the
fundamental requirements of integrated phononic circuits [12-13].
The development of these phononic waveguides encompasses several methodologies: a)
Acoustic total internal reflection. This method selects waveguide materials, such as
chalcogenides [17] and gallium nitride [12], that have lower stiffness than the substrate
materials. In this situation, the acoustic wave can be confined in the lower stiffness
waveguides. Despite their effectiveness, these materials often show compatibility issues with
conventional integrated photonic platforms. b) Impedance mismatch. This strategy involves
the use of suspension designs to minimize acoustic leakage, which has been implemented in
silicon platforms [18-19]. However, it complicates the selection of acoustic frequencies and
requires intricate fabrication techniques. c) Phononic crystals and topological structures
[13,20]. These designs leverage frequency bandgaps to inhibit the propagation of acoustic
waves and offer significant design flexibility. Nonetheless, they introduce complex structures
that challenge fabrication precision and impact yield rates. d) Anti-resonant acoustic
waveguides [21-22]. Inspired by optical anti-resonance observed in hollow-core fibers, our
previous work has led to the creation of anti-resonant acoustic waveguides. These waveguides
provide superior confinement and high selectivity of acoustic modes, capable of confining
both acoustic and optical waves simultaneously. This dual confinement facilitates the
advancement of phoxonic devices (‘phoxonic’ refers to a system or structure that can
manipulate both photons and phonons). This architecture also simplifies the design and
fabrication processes.
Despite these advancements, phononic integrated circuits face several significant challenges:
a) Confining acoustic waves on-chip is challenging due to the faster propagation speed of
common waveguide materials (silicon, lithium niobate, silicon nitride, and silicon carbide)
compared to their wafer insulator (silica). This leads to acoustic wave leakage into the silica
substrate. b) The propagation loss of gigahertz acoustic waves in integrated waveguides is
highly sensitive to sidewall roughness and shape distortion induced by the integrated
fabrication process [21]. Current fabrication techniques require further improvement to
minimize these scattering losses. c) To accomplish complex tasks such as communication
chips and hybrid quantum systems, electric, photonic, and phononic circuits must be
integrated on the same chip. Ideally, the integrated waveguide should confine both acoustic
and optical waves simultaneously to reduce fabrication complexity and failure rates. d)
Compared to integrated electronic and photonic circuits, the maturity and variety of phononic
devices need further improvement. Full-function phononic circuits require the development of
more devices, such as phononic sources (phonon lasers), phononic waveguides, and scalable
couplers/transducers [12].
Research Plan and Methodology
To address the challenges faced by phononic integrated circuits, this research aims to design
novel phoxonic devices based on on-chip photon-phonon interaction to advance the phononic
integrated circuit platform (Figure 1). The research will encompass the following parts:
a) Design of High-Performance Phoxonic Waveguides
The foundation of phononic circuits lies in phoxonic waveguides. This research aims to
design phoxonic waveguides with low loss, tailorability, and ease of fabrication. We propose
to design a novel anti-resonant acoustic waveguide to confine both acoustic and optical waves
simultaneously, thereby increasing photon-phonon interaction. The basic principle of the
anti-resonant waveguide can be illustrated as shown in Fig 2(a). To confine the acoustic field
within the central layer, slower acoustic velocity side layers act as anti-resonant reflecting
layers, conceptually forming a Fabry-Pérot cavity. By controlling the cavity thickness, an
anti-resonant state can be achieved, preventing acoustic waves from propagating through the
side layers and reflecting them back into the central layer. Based on this principle, our novel
approach involves using a suspended solid membrane with etched slots of width t on both
sides of the central waveguide (Figure 2(a), bottom). In the slot regions, the air-solid
boundaries effectively soften the structural response of the solid membrane [23], reducing its
effective acoustic velocity and forming the desired anti-resonant reflecting layers. Based on
this approach, we plan to design anti-resonant acoustic waveguides as shown in Figures 2(b).
Each device consists of a central waveguide flanked by several etched slots, with the entire
suspended structure supported by a series of tethers. These slots can be engineered to function
at anti-resonant conditions, effectively reflecting acoustic waves back into the central
waveguide. This design can simultaneously confine both acoustic and optical waves within
the central waveguide.
b) Theory Establishment and Design of Integrated Phonon Laser
A phonon laser generates coherent acoustic waves, or phonons, much like a traditional laser
produces coherent light waves. Instead of utilizing an optical cavity, a phonon laser employs a
resonant structure within the phononic circuit to confine and amplify phonons. This research
aims to design a narrow-linewidth integrated phonon laser to provide a pure phonon source
for phononic circuits.
Leveraging phoxonic waveguides, this research intends to realize a phonon laser through
photon-phonon interactions, where photons within the cavity serve as the gain medium. This
study will delve into the physical mechanisms of phonon lasers based on these interactions,
identifying the sources of noise and their transfer mechanisms. Additionally, the research will
explore the design of integrated phonon lasers, matching the eigenfrequencies of the acoustic
and cavity modes, optimizing the coupling region between the cavity and the bus waveguide,
and enhancing the photon-phonon overlap within the phoxonic waveguide.
c) Design and Fabrication of Novel Phoxonic Devices on Chip
This research plans to develop a CMOS-compatible microfabrication technique for novel
phoxonic devices. To improve fabrication precision, decrease fabrication costs, and increase
yield rates, the research will explore a loading-effect-based 3D fabrication technique to
simplify the fabrication process and enhance performance. The loading effect [24], where the
etch rate depends on pattern width in etching processes as shown in Figure 3, generally acts as
a barrier to achieving depth uniformity. Yet, this property provides the opportunity to attain
different etching depths in a single etching step. This research requires access to cleanroom
and microfabrication facilities.
d) Design of Characterization Methods for Novel Phoxonic Devices on Chip
This research aims to develop measurement methodologies for characterizing key parameters
of phononic circuit, including phonon frequency, photon-phonon coupling ratio, mechanical
quality factor. Particularly, the measurement techniques for narrow-linewidth phonon lasers
remain underdeveloped. This study seeks to address this gap by analyzing and optimizing
noise factors to achieve narrow-linewidth phonon lasers. Essential to this endeavor are
advanced signal measuring instruments, such as optical and electrical spectrum analyzers,
frequency synthesizers, oscilloscopes, and vector network analyzers. These tools will be
integral in accurately assessing the performance metrics and enhancing the design of phonon
lasers.
Three-Year Research Schedule
As illustrated in the Gantt Chart (Figure 4), our project is structured over a three-year period
with clearly defined objectives for each phase.
In the first year, we will focus on completing the literature review and the design concepts for
phoxonic waveguides. This stage will also include the preliminary designs and simulations of
anti-resonant acoustic waveguides, followed by the detailed design and simulation of
integrated phonon lasers. Additionally, we will begin developing CMOS-compatible
microfabrication techniques.
Moving into the second year, our efforts will shift towards fabricating the initial prototypes of
phoxonic waveguides and refining the measurement setup. We will also iterate on the
waveguide designs based on the feedback from the phonon laser designs, particularly
optimizing photon-phonon interactions.
In the final year, we plan to finalize the fabrication techniques and produce optimized
phoxonic devices. This phase will culminate in the analysis and evaluation of our research
findings, ensuring that we achieve a comprehensive understanding and application of the
developed technologies.
Uniqueness and Justification
This research proposal addresses critical challenges in the field of phononic integrated circuits
by: a) Introducing a novel anti-resonant acoustic waveguide design that simplifies fabrication
while maintaining high performance. b) Developing an integrated phonon laser based on
photon-phonon interaction, advancing the field of coherent phonon generation. c) Exploring
innovative fabrication techniques to improve device quality and yield rate. d) Designing new
measurement and characterization methods for phononic devices, particularly for narrow
linewidth phonon lasers.
By focusing on these areas, this research aims to significantly advance the field of phononic
integrated circuits, potentially enabling new applications in quantum information processing,
high-frequency signal processing, and next-generation communication systems.
Illustrations




                        Fig. 1. The proposed phononic integrated circuits.




   Fig. 2. Anti-resnonant acoustic waveguide. (a) The principle of anti-resonant reflection.
   (b) Schematic of the anti-resnonant acoustic waveguide.




                                Fig. 3. Diagram of loading effect.


                                                 Year 1              Year 2        Year 3
           Research schedule
                                             Q1,2     Q3,4      Q1,2      Q3,4   Q1,2   Q3,4
 Literature review and phoxonic
 waveguides design
 Design and simulation of integrated
 phonon lasers
 Development of microfabrication
 techniques
 Design the measurement setup
 Finalize the fabrication technique and
 produce optimized phoxonic devices
 Summarize the research findings
Fig. 4. Gantt Chart
Pathways to Impact Statement
The development of high-performance phoxonic devices and phononic integrated circuit may
enhance post-5G communication systems and quantum information processing. These
innovative technologies are expected to revolutionize data transmission speeds, enhance
security, and expand computing capabilities.
a) Potential Beneficiaries
This research may impact several sectors in the medium term (4-10 years), including:
 Quantum Technologies: It may enable the development of more advanced hybrid quantum
  systems.
 Consumer Electronics Manufacturers: Enhancements in processing frequency and
  bandwidth are expected.
 High-Performance Computing Industries: Industries that rely on data processing will see
  improvements in efficiency and capabilities.
 Environmental Monitoring and Healthcare Sectors: The adoption of advanced phononic
  circuits is anticipated to enhance sensing technologies, resulting in improved data accuracy
  and sensitivity.

b) Demonstrable Benefits Beyond Academia
 Advancement in Post-5G Communication Systems: The novel high-performance acoustic
  wave filters developed from our phoxonic devices may significantly enhance spectrum
  utilization, increase data rates, and improve signal quality in post-5G communication
  systems.
 Optical Storage for Photonic Computation: Research into phonon-based optical storage is
  expected to contribute to next-generation photonic computers, potentially transforming data
  processing capabilities.


c) Actions to Increase Likelihood of Achieving Benefits
 Industry Collaboration: Form partnerships with leading semiconductor manufacturers to
  facilitate the transition from laboratory to fabrication. Collaborate with telecommunications
  equipment providers to ensure system compatibility, and engage with quantum technology
  startups for potential applications.
 Fabrication Process Optimization: Develop and refine CMOS-compatible fabrication
  processes to lower costs and improve yield rates. Investigate novel 3D fabrication techniques
  to enhance device performance and streamline manufacturing processes.

d) Potential Risks and Mitigation Strategies
 Fabrication Equipment Failure:
    Risk: Breakdown of cleanroom equipment due to unforeseen events, leading to project
      delays.
    Mitigation: Collaborate with other university cleanrooms for backup facilities, partner
      with commercial foundries for contingency options, and implement regular maintenance
      schedules.
 Adoption Barriers:
    Risk: Industry resistance to adopting new phononic technologies due to high initial costs.
    Mitigation: Develop detailed cost-benefit analyses to showcase long-term benefits and
      refine micro-fabrication techniques to reduce costs.
 Scaling Challenges:
    Risk: Difficulties in scaling fabrication processes for mass production.
    Mitigation: Collaborate early with semiconductor foundries and focus on designs
      compatible with existing manufacturing techniques.
Reference
[1] Wu, Y., & Xiao, Z. (2022). The recent progress of lithography machine and the State-of-
     art facilities. Highlights in Science, Engineering and Technology, 5, 155-165.
[2] Ferry, D. K., Weinbub, J., Nedjalkov, M., & Selberherr, S. (2022). A review of quantum
     transport in field-effect transistors. Semiconductor Science and Technology, 37(4), 043001.
[3] Xie, C., & Zhang, B. (2022). Scaling optical interconnects for hyperscale data center
     networks. Proceedings of the IEEE, 110(11), 1699-1713.
[4] Thraskias, C. A., Lallas, E. N., Neumann, N., Schares, L., Offrein, B. J., Henker, R.,
     Plettemeier, D., Ellinger, J., Leuthold, J., & Tomkos, I. (2018). Survey of photonic and
     plasmonic interconnect technologies for intra-datacenter and high-performance computing
     communications. IEEE Communications Surveys & Tutorials, 20(4), 2758-2783.
[5] Bai, B., Yang, Q., Shu, H., Chang, L., Yang, F., Shen, B., Tao, Z., Wang, X., Xu, S., Xie,
     W., & Zou, W. (2023). Microcomb-based integrated photonic processing unit. Nature
     Communications, 14(1), 66.
[6] Munk, D., Katzman, M., Hen, M., Priel, M., Feldberg, M., Sharabani, T., Levy, S.,
     Bergman, A., & Zadok, A. (2019). Surface acoustic wave photonic devices in silicon on
     insulator. Nature communications, 10(1), 4214.
[7] Li, B. B., Ou, L., Lei, Y., & Liu, Y. C. (2021). Cavity optomechanical sensing.
     Nanophotonics, 10(11), 2799-2832.
[8] Xia, Y., Agrawal, A. R., Pluchar, C. M., Brady, A. J., Liu, Z., Zhuang, Q., Wilson, D. J., &
     Zhang, Z. (2023). Entanglement-enhanced optomechanical sensing. Nature Photonics,
     17(6), 470-477.
[9] Dong, C. H., Shen, Z., Zou, C. L., Zhang, Y. L., Fu, W., & Guo, G. C. (2015). Brillouin-
     scattering-induced transparency and non-reciprocal light storage. Nature communications,
     6(1), 6193.
[10] Kim, J., Kuzyk, M. C., Han, K., Wang, H., & Bahl, G. (2015). Non-reciprocal Brillouin
     scattering induced transparency. Nature Physics, 11(3), 275-280.
[11] Ruppel, C. C. (2017). Acoustic wave filter technology–a review. IEEE transactions on
     ultrasonics, ferroelectrics, and frequency control, 64(9), 1390-1400.
[12] Fu, W., Shen, Z., Xu, Y., Zou, C. L., Cheng, R., Han, X., & Tang, H. X. (2019). Phononic
     integrated circuitry and spin–orbit interaction of phonons. Nature communications, 10(1),
     2743.
[13] Taylor, J. C., Chatterjee, E., Kindel, W. F., Soh, D., & Eichenfield, M. (2022).
     Reconfigurable quantum phononic circuits via piezo-acoustomechanical interactions. npj
     Quantum Information, 8(1), 19.
[14] Massel, F., Cho, S. U., Pirkkalainen, J. M., Hakonen, P. J., Heikkilä, T. T., & Sillanpää, M.
     A. (2012). Multimode circuit optomechanics near the quantum limit. Nature
     communications, 3(1), 987.
[15] Barzanjeh, S., Xuereb, A., Gröblacher, S., Paternostro, M., Regal, C. A., & Weig, E. M.
     (2022). Optomechanics for quantum technologies. Nature Physics, 18(1), 15-24.
[16] Marpaung, D., Yao, J., & Capmany, J. (2019). Integrated microwave photonics. Nature
     photonics, 13(2), 80-90.
[17] Pant, R., Poulton, C. G., Choi, D. Y., Mcfarlane, H., Hile, S., Li, E., Thevenaz, L., Luther-
     Davies, B., Madden, S.J., & Eggleton, B. J. (2011). On-chip stimulated Brillouin scattering.
     Optics express, 19(9), 8285-8290.
[18] Van Laer, R., Kuyken, B., Van Thourhout, D., & Baets, R. (2015). Interaction between light
     and highly confined hypersound in a silicon photonic nanowire. Nature Photonics, 9(3),
     199-203.
[19] Kittlaus, E. A., Shin, H., & Rakich, P. T. (2016). Large Brillouin amplification in silicon.
     Nature Photonics, 10(7), 463-467.
[20] Ren, H., Shah, T., Pfeifer, H., Brendel, C., Peano, V., Marquardt, F., & Painter, O. (2022).
     Topological phonon transport in an optomechanical system. Nature Communications,
     13(1), 3476.
[21] Schmidt, M. K., O’Brien, M. C., Steel, M. J., & Poulton, C. G. (2020). ARRAW: anti-
     resonant reflecting acoustic waveguides. New Journal of Physics, 22(5), 053011.
[22] Lei, P., Xu, M., Bai, Y., Chen, Z., & Xie, X. (2024). Anti-resonant acoustic waveguides
     enabled tailorable Brillouin scattering on chip. Nature Communications, 15(1), 3877.
[23] Safavi-Naeini, A. H., Van Thourhout, D., Baets, R., & Van Laer, R. (2019). Controlling
     phonons and photons at the wavelength scale: integrated photonics meets integrated
     phononics. Optica, 6(2), 213-232.
[24] Yeom, J., Wu, Y., Selby, J. C., & Shannon, M. A. (2005). Maximum achievable aspect ratio
     in deep reactive ion etching of silicon due to aspect ratio dependent transport and the
     microloading effect. Journal of Vacuum Science & Technology B: Microelectronics and
     Nanometer Structures Processing, Measurement, and Phenomena, 23(6), 2319-2329.
PAST RESEARCH EXPERIENCE
Novel Phononic Integrated Circuits (Fund of State Key Laboratory of Photonics and
Communications, P. R. China)
   Duration: January 2025 - December 2026
   Project Description: This project focuses on developing innovative phoxonic devices that
    integrate both light and sound wave control on a single chip. As traditional electronic and
    photonic circuits encounter limitations in speed, power efficiency, and scalability,
    phononic circuits offer a unique solution by enabling precise handling of acoustic waves
    for advanced signal processing and storage.
   Main Responsibilities: As the project leader, I oversee the development of innovative
    phononic devices that integrate both light and sound wave control on a single chip. My
    responsibilities include designing and fabricating waveguides that confine photons and
    phonons for strong light-sound interactions, conducting simulations and experiments to
    optimize device performance, and managing collaboration among team members. I also
    prepare research reports and publications, present our findings at conferences, and mentor
    junior researchers throughout the project.

Design and Integration of High-Speed, Low-Jitter Light Sources and Wideband
High-Linearity Photonic Devices Array (National Key R&D Program of the Ministry of
Science and Technology)
   Duration: January 2020 - December 2022
   Project Description: The project aimed to achieve the development of high-speed,
    low-jitter, high signal-to-noise ratio ultrashort pulse sources, and establish wideband
    high-linearity electro-optic modulation, photodetection physical model construction, and
    integrated device development.
   Main Responsibilities: I took charge of part of the fabrication process of the lithium
    niobate modulator in the on-chip ultrashort pulse light source. By employing a combined
    dry and wet etching process, I successfully developed low-loss lithium niobate ridge
    waveguides, which supported the subsequent design of electro-optic modulation combs.
    This project achieved a repetition rate of 20 GHz, pulse width of 2.437 ps, and a timing
    jitter of 14.6 fs in the ultrashort pulse light source based on the lithium niobate on-chip
    electro-optic modulation comb.

Exploration of Chip-based Ultra-Low Phase Noise Microwave Signal Extraction Using
Optical Frequency Combs (National Key R&D Program of the Ministry of Science and
Technology)
   Duration: November 2020 - November 2022
   Project Description: The project focused on developing a scheme for extracting ultra-low
    phase noise microwave signals based on photonic chips, combined with a Brillouin laser
    supporting a fundamental linewidth of 100 Hz. After division by Kerr optical frequency
    combs, the phase noise of the 25 GHz signal reached -100 dBc/Hz at 10 kHz.
   Main Responsibilities: I was responsible for designing on-chip Brillouin lasers to support
    the 100 Hz fundamental linewidth. The project innovatively utilized a thermal
    self-locking scheme in Kerr optical frequency combs to generate microwave signals at 25
    GHz with phase noise reaching -113 dBc/Hz at 10 kHz.

Photonic ADC Based on Ultra-Low Noise, High-Frequency Wideband Optical Sampling
(Major Project of the National Natural Science Foundation of China)
   Duration: September 2019 - December 2021
   Project Description: This project leveraged an optical sampling pulse source with
    extremely low phase noise to achieve high-precision photonic ADC of high-frequency
    wideband RF signals.
   Main Responsibilities: I led the design and assembly of a high repetition rate, extremely
    low clock jitter, high signal-to-noise ratio optical sampling clock source, providing a
    foundation for the backend optical sampling of electrical signals. The final experiments
    successfully completed the first photonic ADC system trial verification with a 40 GHz
    carrier frequency, 10 GHz bandwidth, and 7.0-bit ENOB
LEADERSHIP EXPERIENCE


      I have demonstrated strong leadership and technical expertise through my involvement
in various high-impact research projects. As a project leader, I successfully managed the
development of innovative photonic and phononic devices, including tasks like device design,
fabrication, and performance optimization. My responsibilities also encompassed mentoring
junior researchers, managing interdisciplinary collaborations, and presenting research
outcomes at conferences. Notable achievements include breakthroughs in ultrashort pulse
light sources, on-chip Brillouin lasers, and photonic ADC systems, which have significantly
contributed to advancements in photonics and integrated circuits.
      Outside academia, I have excelled in leadership roles through extracurricular activities.
As the captain of my undergraduate university’s robotics team, I led the team to win the China
National Robot Competition Prize. Additionally, I served as a department supervisor in the
student council, where I organized events and mentored junior members. These experiences
have strengthened my ability to lead diverse teams, promote collaboration, and deliver
innovative outcomes in academic and non-academic domains alike.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Lei
                        (Other name) Peng
Host Faculty / School / Department / Faculty of Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

   I have known Dr. Lei Peng since 2019, when he began his PhD studies at the School of
 Electronics, Peking University. As his supervisor, I have closely supervised his research
 activities and academic development throughout his doctoral program.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent    Very Good        Good              Average          No basis
                           (top 5%)     (top 6-20%) (top 21-50%)          or below            for
                                                                         (lower than       judgment
                                                                            50%)
Intellectual capacity                       ☐               ☐                 ☐               ☐
                                             ☐               ☐                 ☐               ☐
Ability for conducting
scholastic research
                               
                                             ☐               ☐                 ☐               ☐
Knowledge of proposed
research study
                               

                                             ☐               ☐                 ☐               ☐
Analytical power and
reasoning
                               
Judgment                                    ☐               ☐                 ☐               ☐
                                             ☐               ☐                 ☐               ☐
Imagination and
originality
                               

                                             ☐               ☐                 ☐               ☐
Motivation and
perseverance
                               

                                             ☐               ☐                 ☐               ☐
Skills of writing and
argumentation
                               

                                             ☐               ☐                 ☐               ☐
Capacity for independent
work
                               

                                             ☐               ☐                 ☐               ☐
Reliability and sense of
responsibility
                               
Leadership experience                       ☐              ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
    Dr. Lei Peng has consistently demonstrated exceptional research abilities and potential in
 his field. His dedication to science and technology is evident through his leadership in
 photon-phonon interaction research within our laboratory. By focusing on challenges
 associated with integrated phononic platforms, Dr. Lei proposed and demonstrated a novel
 anti-resonant acoustic waveguide, significantly advancing our understanding and capabilities
 in this area.
    Moreover, Dr. Lei has successfully completed several key research projects related to
 communication systems and microwave photonics. He has also played a crucial role in
 adjusting and maintaining the integration instruments in our laboratory’s cleanroom. These
 experiences are vital for advancing technology in these fields and are critical for his proposed
 work during the JRFS fellowship. CHanDr. Lei’s passion for research, combined with his
 technical expertise and proven track record, make him an outstanding candidate for the JRFS.
 I am confident that he will continue to excel and make significant contributions to his field.
                                                 3
                                                         JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: ZHANGYUAN CHEN                                  Title: Prof
                         (in BLOCK letters)                                 (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Peking University
 Position: The director of State Key Laboratory of Advanced Optical Communication Systems &
 Networks (Beijing division)
 E-mail Address: <EMAIL>
 Telephone Number#:+86- 13301026032
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date:




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Lei
                        (Other name) Peng
Host Faculty / School / Department / Faculty of Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

    I have known Dr. Lei Peng over five years in my capacity as his PhD co-supervisor. During
 this time, I have closely observed his academic and research development, and I can
 confidently attest to his diligence, passion for science and technology, and exceptional research
 capabilities.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent   Very Good        Good              Average          No basis
                           (top 5%)    (top 6-20%) (top 21-50%)          or below            for
                                                                        (lower than       judgment
                                                                           50%)
Intellectual capacity                      ☐               ☐                 ☐               ☐
                                            ☐               ☐                 ☐               ☐
Ability for conducting
scholastic research
                              
                                            ☐               ☐                 ☐               ☐
Knowledge of proposed
research study
                              

                                            ☐               ☐                 ☐               ☐
Analytical power and
reasoning
                              
Judgment                                   ☐               ☐                 ☐               ☐
                                            ☐               ☐                 ☐               ☐
Imagination and
originality
                              

                                            ☐               ☐                 ☐               ☐
Motivation and
perseverance
                              

                                            ☐               ☐                 ☐               ☐
Skills of writing and
argumentation
                              

                                            ☐               ☐                 ☐               ☐
Capacity for independent
work
                              

                                            ☐               ☐                 ☐               ☐
Reliability and sense of
responsibility
                              
Leadership experience                      ☐              ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
    Throughout his PhD studies, Dr. Lei Peng has showcased exceptional research abilities
 and potential. He has led our research group in the area of photon-phonon interaction,
 successfully completing several key research projects. His work has resulted in multiple
 patents and publications in prestigious SCI journals, including Nature Communications,
 Optics Letters, and Optics Express. These accomplishments underscore his capacity to
 conduct high-impact research and make contributions to the scientific community.
    In addition to his research achievements, Dr. Lei has extensive experience in constructing
 communication systems and microwave photonics systems. He is proficient in integrated
 fabrication techniques, which are crucial for advancing technology in these fields. His
 technical skills, combined with his innovative mindset, and passion for innovation, make
 him an ideal candidate for the JRFS. I strongly recommend Dr. Lei Peng for the RGC
 Junior Research Fellow Scheme. And, I am confident that he will continue to excel and
 make contributions to the field of electronics and photonics.
                                                3
                                                        JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: XIAOPENG XIE                                    Title: Prof
                         (in BLOCK letters)                                 (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Peking University
 Position: Associate professor with the School of electronics, Peking University

 E-mail Address: <EMAIL>
 Telephone Number#:+86-13488698660
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date: September 1th, 2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
