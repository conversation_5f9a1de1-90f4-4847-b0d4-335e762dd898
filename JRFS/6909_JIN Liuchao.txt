<PERSON><PERSON><PERSON> (<PERSON>) <PERSON>
� (+852) 9570 5790 � <EMAIL> � linkedin.com/in/liuchaojin
� ERB201, The Chinese University of Hong Kong, Hong Kong SAR, 999077, China
� jin-liuchao.github.io � 20 Nov 1999, Shaoxing, Zhejiang, China




� Education
  08/2022-Present     Chinese University of Hong Kong (CUHK), Doctor of Philosophy - Ph.D.
 Hong Kong, China     Major : Mechanical and Automation Engineering Supervisor : Prof. <PERSON>-<PERSON><PERSON>
                      Research Direction : 3D/4D Printing, Smart Materials & Adaptive Structures, Soft Robotics.
                      Award : Hong Kong PhD Fellowship Scheme (HKPFS)

  09/2018-06/2022     Sichuan University - Pittsburgh Institute (SCUPI), Bachelor of Engineering - BE
   Chengdu, China     GPA : 4.0/4.0 Weighted Average Mark : 96.29/100 Major : Mechanical Engineering           Ranking : 1/79

� Experiences
  ∠ 09/2025 - Present, California Institute of Technology (CalTech), Visiting Graduate Researcher.
    Supervisor : Prof. <PERSON><PERSON>. Program : CUHK Overseas Research Attachment Programme.
  ∠ 08/2024 - 09/2025, Shenzhen University, Visiting Student.
    Cooperative Mentors : Prof. <PERSON><PERSON> and <PERSON><PERSON><PERSON>.
  ∠ 08/2023 - 09/2025, Southern University of Science and Technology (SUSTech), Visiting Student.
    Cooperative Mentor : Prof. <PERSON>.
  ∠ 08/2022 - 07/2025, The Chinese University of Hong Kong, Teaching Assistant.
  ∠ 06/2021 - 09/2021, McGill University, Research Assistant.
    Supervisor : Prof. Abdolhamid Akbarzadeh Shafaroudi. Program : Mitacs Globalink Research Internship.
  ∠ 07/2021 - 08/2021, Westlake University, Research Assistant.
    Supervisor : Prof. Weicheng Cui. Program : Westlake University Summer Graduate Research Internship.
  ∠ 02/2020 - 06/2022, Sichuan University - Pittsburgh Institute, Teaching Assistant.


� Honors & Awards
 05/2025    CUHK PhD International Mobility for Partnerships and Collaborations Award (PhD IMPAC Award)
 02/2025    Outstanding Students Award at The Chinese University of Hong Kong
 11/2022    Outstanding Senior Project Award in Sichuan University
 10/2022    Best Paper Award in 2022 IEEE International Conference on Unmanned Systems (ICUS)
 06/2022    Outstanding Senior Project Award in Sichuan University-Pittsburgh Institute
 05/2022    First Prize in The 7th National Academic English Vocabulary Competition (NAEV)
 05/2022    Outstanding Senior Project Poster Award in Sichuan University-Pittsburgh Institute
 03/2022    Outstanding Graduate of Sichuan Province
 01/2022    A-level Certificate in Comprehensive Quality of University Students in Sichuan Province
 12/2021    2020-2021 Academic Year National Scholarship (China) (¥8,000)
 10/2021    Outstanding Graduate of Sichuan University
 10/2021    2020-2021 Academic Year Outstanding Student of Sichuan University
 10/2021    2020-2021 Academic Year Sichuan University-Pittsburgh Institute Best Academic Achiever Award (¥60,000)
 09/2021    First Prize in Chinese Tale - English Talk
 06/2021    Second Prize in Videos and Voices Short Video Contest
 05/2021    Second Prize in The 6th National Academic English Vocabulary Competition (NAEV)
 04/2021    Honorable Mention in 2021 Mathematical Contest in Modeling (MCM)
 12/2020    2019-2020 Academic Year National Scholarship (China) (¥8,000)
 10/2020    2019-2020 Academic Year Outstanding Student of Sichuan University
 09/2020    Excellent Teaching Assistant Award for Sichuan University-Pittsburgh Institute
 09/2020    2019-2020 Academic Year Sichuan University-Pittsburgh Institute Academic Star (¥10,000)
 12/2019    2018-2019 Academic Year National Scholarship (China) (¥8,000)
 10/2019    2018-2019 Academic Year Outstanding Student of Sichuan University
 09/2019    2018-2019 Academic Year Sichuan University-Pittsburgh Institute Best Academic Achiever Award (¥60,000)
 07/2019    First Prize in the Special Category of the 9th ”Video Friends Cup” China University TV Awards
 02/2019    Student Ambassador of Sichuan University-Pittsburgh Institute
� Publications & Patents
 ∠ Liuchao Jin, Shouyi Yu, Jianxiang Cheng, Zhigang Liu, Kang Zhang, Sicong Zhou, Xiangnan He, Guoquan Xie, Mahdi Bodaghi,
   Qi Ge, Wei-Hsin Liao. (2025). Machine learning powered inverse design of strain fields of hierarchical architectures. Composites
   Part B : Engineering, 299, 112372.
 ∠ Liuchao Jin, Xiaoya Zhai, Wenbo Xue, Kang Zhang, Jingchao Jiang, Mahdi Bodaghi, Wei-Hsin Liao. (2025). Finite element
   analysis, machine learning, and digital twins for soft robots : State-of-arts and perspectives. Smart Materials and Structures,
   34(3), 033002.
 ∠ Liuchao Jin, Kang Zhang, Sicong Zhou, Guoquan Xie, Wei-Hsin Liao. (2025). Modulus tunability in hierarchical architectures :
   A machine learning-enabled approach. In Multifunctional Materials and Structures 2025, 13433, 133-143. SPIE.
 ∠ Wenpeng Xu, Mengyu Zhang, Hao Xu, Liuchao Jin*, Xiaoya Zhai*, Jingchao Jiang. (2025). INPR-connector : Interlocking ne-
   gative Poisson’s ratio connectors design for deployable energy absorption structures. Composites Part B : Engineering, 297,
   112243.
 ∠ Wenpeng Xu, Ning Zhang, Hao Xu, Liuchao Jin*, Jingchao Jiang*. (2025). Stress-guided lightweight design and optimization
   for 3D printing sacrificial molds. Materials & Design, 255, 114161.
 ∠ Liuchao Jin, Shouyi Yu, Jianxiang Cheng, Haitao Ye, Xiaoya Zhai, Jingchao Jiang, Kang Zhang, Bingcong Jian, Mahdi Boda-
   ghi, Qi Ge, Wei-Hsin Liao. (2024). Machine learning-driven forward prediction and inverse design for 4D printed hierarchical
   architecture with arbitrary shapes. Applied Materials Today, 40, 102373.
 ∠ Liuchao Jin, Xiaoya Zhai, Kang Wang, Kang Zhang, Dazhong Wu, Aamer Nazir, Jingchao Jiang, Wei-Hsin Liao. (2024). Big
   data, machine learning, and digital twin assisted additive manufacturing : A review. Materials & Design, 244, 113086. (ESI
   Highly Cited Paper, Cover Paper, Top Cited, Top Downloaded)
 ∠ Liuchao Jin, Xiaoya Zhai, Jingchao Jiang, Kang Zhang, Wei-Hsin Liao. (2024). Optimizing stimuli-based 4D printed structures :
   A paradigm shift in programmable material response. In Sensors and Smart Structures Technologies for Civil, Mechanical, and
   Aerospace Systems 2024, 12949, 321-332. SPIE.
 ∠ Liuchao Jin, Xiaoya Zhai, Kang Zhang, Jingchao Jiang. (2024). Unlocking the potential of low-melting-point alloys integra-
   ted extrusion additive manufacturing : Insights into mechanical behavior, energy absorption, and electrical conductivity.
   Progress in Additive Manufacturing, 10(4), 2733–2745.
 ∠ Liuchao Jin, Xiaoya Zhai, Kang Zhang, Jingchao Jiang, Wei-Hsin Liao. (2024). 3D printing low-melting-point alloys integrated
   soft robots. Materials Science in Additive Manufacturing, 3(3), 4144. (Top Cited, Most Downloaded)
 ∠ Liuchao Jin, Xiaoya Zhai, Kang Zhang, Jingchao Jiang, Wei-Hsin Liao. (2024). Spider web-inspired additive manufacturing :
   Unleashing the potential of lightweight support structures. In 21st International Conference on Manufacturing Research.
 ∠ Liuchao Jin, Weicheng Cui. (2024). On technical issues for underwater charging of robotic fish schools using ocean renewable
   energy. Ships and Offshore Structures, 19(9), 1465-1475.
 ∠ Liuchao Jin, Yuchen Lou, Lu-An Chen, Qi Lu. (2022). 6 Degree of freedom unified tracking controller for tilt-rotor multi-rotor
   unmanned aerial vehicles based on unit dual quaternion. In 2022 5th IEEE International Conference on Unmanned Systems
   (ICUS), 1356-1363. IEEE.
 ∠ Wenbo Xue, Liuchao Jin, Bingcong Jian, Qi Ge. (2025). Origami-based flexible robotic grippers via hard-soft coupled multi-
   material 3D printing for soft robotics. Soft Robotics.
 ∠ Kiandokht Mirasadi, M Amin Yousefi, Liuchao Jin, Davood Rahmatabadi, Majid Baniassadi, Wei-Hsin Liao, Mahdi Bodaghi,
   Mostafa Baghani. (2025). 4D printing of magnetically responsive shape memory polymers : Toward sustainable solutions in
   soft robotics, wearables, and biomedical devices. Advanced Science, e13091.
 ∠ Kang Zhang, Jingchao Jiang, Liuchao Jin, Qiang Gao, Xiaoya Zhai, Sicong Zhou, Zhenhong Li, Jifan Li and Wei-Hsin Liao.
   (2025). Low-melting-point alloy / polyurethane auxetic composite foam for outstanding impact protection with favorable
   shape memory effect. Smart Materials and Structures, 34(4), 045025.
 ∠ Sicong Zhou, Kang Zhang, Liuchao Jin, Qiang Gao, Wei-Hsin Liao. (2025). Efficient data driven optimization framework for
   designing B-spline honeycombs with excellent energy absorption. Thin-Walled Structures, 209, 112941.
 ∠ Wenbo Xue, Bingcong Jian, Liuchao Jin, Rong Wang, Qi Ge. (2025). Origami robots : Design, actuation, and 3D printing me-
   thods. Advanced Materials Technologies, e00278.
 ∠ Changyue Liu, Liuchao Jin, Wei-Hsin Liao, Zhijian Wang, Qiguang He. (2025). Achieving rapid actuation in liquid crystal elas-
   tomers. National Science Open, 4(2), 20240013.
 ∠ Tielin Dai, Liuchao Jin, Chen Shang, Xiaoya Zhai, Xiao-Ming Fu, Ligang Liu. (2025). Advances in Intelligent Design of Metama-
   terials. Journal of Computer-Aided Design & Computer Graphics, 37(1), 1-27.
 ∠ Linlin Wang, Jingchao Jiang, Yanyan Tian, Yicheng Dong, Haobing Li, Liuchao Jin, Voicu Ion Sucala. (2025). Prediction of pro-
   cess parameters based on stress-strain behaviour in 3D printing using deep neural network. In 2025 International Conference
   on Intelligent Digitization of Systems and Services (IDSS). IEEE.
 ∠ Kang Zhang, Qiang Gao, Jingchao Jiang, Meishan Chan, Xiaoya Zhai, Liuchao Jin, Jiangfan Zhang, Jifan Li, Wei-Hsin Liao.
   (2024). High energy dissipation and self-healing auxetic foam by integrating shear thickening gel. Composites Science and
   Technology, 249, 110475.
 ∠ Jingchao Jiang, Liuchao Jin, Xiaoya Zhai, Kang Zhang, Jun Chen, Wei-Hsin Liao. (2023). A novel strategy to fabricate low-
   melting-point alloy and its composite parts using extrusion additive manufacturing. In The 50th International Conference on
   Computers and Industrial Engineering.
 ∠ Xiaoya Zhai, Yundong Gai, Liuchao Jin, Wei-Hsin Liao, Falai Chen, Ping Hu. (2023). Isogeometric topology optimization of
   auxetic materials based on moving morphable method. In Materials Research Proceedings, 31, 172-186.
  ∠ Jingchao Jiang, Xiaoya Zhai, Kang Zhang, Liuchao Jin, Qitao Lu, Zhichao Shen, Wei-Hsin Liao. (2023). Low-melting-point
    alloys integrated extrusion additive manufacturing. Additive Manufacturing, 72, 103633.
  ∠ Jingchao Jiang, Xiaoya Zhai, Liuchao Jin, Kang Zhang, Jun Chen, Qitao Lu, Wei-Hsin Liao. (2023). Design for reversed additive
    manufacturing low-melting-point alloys. Journal of Engineering Design, 1-14.
  ∠ Xiaoya Zhai, Liuchao Jin, Jingchao Jiang. (2022). A survey of additive manufacturing reviews. Materials Science in Additive
    Manufacturing, 1(4), 21. (Most Downloaded)
  ∠ China Invention Grant Patent : Tracking Control Method for Tilt-Rotor Multi-Rotor UAV Based on Dual Quaternion. Patent
    number : ZL 2022 1 0739442.2.
  ∠ China Utility Model Patent : Air Purification Device. Patent number : ZL 2021 2 2679101.4.
� Conferences & Talks
  ∠ 2025/10/12–10/15. 2025 SES Annual Technical Meeting. Atlanta, Georgia, United States.
  ∠ 2025/09/08–09/10. ASME’s Premier Conference on Smart Materials, Adaptive Structures, and Intelligent Systems. St. Louis,
    Missouri, United States.
  ∠ 2025/07/08–07/10. 4D Materials Design and Additive Manufacturing Conference. Singapore.
  ∠ 2025/03/17–03/20. SPIE Smart Structures + NDE 2025. Vancouver, Canada.
  ∠ 2025/01/06–01/10. 13th edition of the Global Young Scientists Summit (GYSS). Singapore.
  ∠ 2024/09/25. SCUPI Alumni Sharing Session. Sichuan University-Pittsburgh Institute, Chengdu, China.
  ∠ 2024/07/04–07/05. 4D Materials Design and Additive Manufacturing Conference 2024. Ajaccio, Corsica, France.
  ∠ 2024/03/25–03/28. SPIE Smart Structures + NDE 2024. Long Beach, California, United States.
  ∠ 2023/07/08. Seminar on Frontiers of Mathematics and Interdisciplinarity. University of Science and Technology of China,
    Hefei, China.
  ∠ 2022/10/28–10/30. 2022 5th IEEE International Conference on Unmanned Systems. Guangzhou, China.

� Leadership & Extracurricular Activities
  ∠ 06/2024 - 06/2025, Sichuan University - Pittsburgh Institute Alumni Association, Alumni Mentor.
  ∠ 09/2022 - 09/2023, The Postgraduate Halls Residents’ Association, General Administrator.
  ∠ 03/2022 - 03/2023, Mitacs Globalink, Ambassador.
  ∠ 09/2019 - 09/2020, Sichuan University The 31st Student Congress, Representative.
  ∠ 03/2019 - 06/2020, Sichuan University Student Television Station, Deputy Director.
  ∠ 09/2018 - 06/2019, Sichuan University-Pittsburgh Institute Student Council, Member.


� Community Contributions
  ∠ 05/2021 & 11/2019, Chengdu Blood Center, Blood Donor.
  ∠ 02/2021, UNESCO, Disaster Risk Management at UNESCO Designated Sites, Volunteer.
  ∠ 07/2020-08/2020, GREENPEACE, Blue Planet Rescue Plan, Volunteer.
  ∠ 02/2019, SCU – Return to Alma Mater, Social Practice of Winter Vacation, Participant.

(last update : 9 September 2025)
Abstract of Research

Soft robots are emerging machines designed to be compliant, adaptable, and safe when inter-
acting with people. Unlike traditionally rigid robots, they can pass through narrow openings,
wrap around odd shapes, and carefully handle fragile items. Such distinct capabilities make
soft robots very promising solutions where they can be used in medicine, disaster recovery, and
co-robotics with people. While their promise remains high, the majority of current soft robots
suffer significant shortcomings: they are too weak to lift heavy payloads, too slow to react, or
too complicated and expensive to build and control. Breaking such limitations is a precondition
needed before they can emerge from lab rooms into practical uses.
This work focuses on designing a new soft-rigid coupled pneumatic actuator based on the
segmented abdomen of crayfish. Crayfish are capable of producing fast and steady tail-bending
motions through the interaction of rigid exoskeleton pieces with compliant joints. Basing our
design on such a biological principle, we plan to design modular actuators that combine 3D-
printed rigid pieces to provide structural integrity with soft elastic compartments allowing for
adjustability. The proposed design is expected to overcome the conventional trade-offs between
strength and deformability, enabling actuators that are lightweight, modular, and scalable.
The project will pursue three goals that directly target the long-standing barriers of soft robotics.
First, we will design actuator geometries and material architectures that achieve unprecedented
load-to-weight ratios, with the explicit aim of creating the world’s strongest lightweight soft
actuators—capable of lifting payloads hundreds of times their own weight, rivaling or even
surpassing the relative strength seen in biological organisms such as ants or beetles. Second, we
will pioneer pneumatic actuation schemes with millisecond-scale responsiveness, establishing
the fastest dynamic actuation ever reported in soft robotics. Such performance will open new
domains of agility, including aerial platforms and untethered micro–unmanned aerial vehicles
(UAVs) that demand rapid response and high-frequency control. Third, we will develop precision
control strategies that endow the actuators with human-level dexterity, enabling them to reliably
manipulate delicate and irregular objects—ranging from surgical tools to fragile biomedical
samples—while also scaling up to high-force industrial tasks.
A distinguishing feature of our approach is its modularity and versatility. The actuator modules
are conceived as universal building blocks that can be rapidly assembled into a wide spectrum of
robotic systems—from quadrupeds and underwater vehicles to aerial robots, dexterous grippers,
and reconfigurable swarms. In particular, we envision their deployment in soft-rigid hybrid
drones with unparalleled load capacity and agility, enabling new classes of bioinspired aerial and
terrestrial machines. Because the rigid shell components are 3D-printable and can be fabricated
at low cost within minutes, this design pathway ensures scalability from benchtop prototypes to
large-scale, field-deployable systems. By lowering fabrication barriers while delivering record-
breaking performance, the proposed technology is positioned as a transformative platform for
next-generation soft robotics, enabling adoption across land, sea, and air.
The expected outcomes of this work are high-capacity, fast-actuation, precisely-controllable
actuators, a combination of abilities seldom reached simultaneously in today’s soft robots.
Such actuators will be the building blocks of future high-level applications, such as wearable
assistive robots for rehabilitation, search-and-rescue robots for disaster scenarios, and robots
suitable for collision-unsafe co-manipulation with people. Through the integration of biological
inspiration and recent advances in fabrication and modeling, this work will not only contribute
significantly towards fundamental knowledge of soft-rigid actuation, but also propel Hong Kong
into a leadership role in bioinspired robot development.

                                                 1
Proposed Objectives and Potential Impact of Research Project / Activities

Proposed Objectives

We will establish a new paradigm for crayfish-inspired modular soft robots along three strategic
axes–extreme performance, Lego-style scalability, and radical affordability. The objectives be-
low are deliberately ambitious and accompanied by measurable targets and validation protocols.
A. Performance Objectives (strength, speed, precision)
The first axis emphasizes raw performance. Current soft robots often face the perception of
being weak, sluggish, and difficult to control. We aim to decisively break this stereotype:
   1. High load capacity. We target a force-to-weight ratio ≥ 200× at the module level, redefin-
      ing the strength frontier of soft robots and surpassing nature’s “power benchmark”—ants
      that carry ∼50× their own weight.
   2. Ultrafast response. We pursue millisecond-level command-to-actuation latency, a regime
      rarely attained by soft pneumatic systems, to unlock highly dynamic behaviors such as
      jumping, aerial stabilization, or rapid grasping of moving targets.
   3. Precision manipulation. We aim for fine, damage-free manipulation of small/fragile
      objects, exemplified by reliably grasping and placing a coin. This will be benchmarked
      by standardized pick-and-place tasks on irregular, soft, or brittle samples, paving the way
      for biomedical handling or surgical applications.
Together, these targets will demonstrate that modular soft actuators can simultaneously achieve
strength, speed, and precision — a triad seldom achieved even in rigid robotic systems.
B. Scalability and Modularity Objectives (Lego-style reconfiguration)
The second axis focuses on adaptability and reconfiguration. Inspired by the crayfish abdomen,
which derives versatile motion from repeating segments, we aim to develop a universal design
language for modular soft robotics:
    • Segmented, bioinspired building blocks. Develop standardized crayfish-like segments
      as plug-and-play modules with unified mechanical and pneumatic interfaces; programmable
      inter-segment locking angles enable curvature composition and load routing.
    • System-level reconfigurability. Realize rapid assembly into diverse robot forms —
      quadruped “robotic dog”, robotic fish, dexterous hand, flapping-wing robot — by recom-
      bining a shared parts library.
    • Quantitative scalability targets.
         1. reconfiguration time from one morphology to another ≤30 min without specialized
            tools;
         2. module reuse rate ≥80% across the four demonstrator platforms;
         3. number of unique part types ≤12 for the entire ecosystem;
         4. control stack supporting ≥32 addressable segments on a shared bus with real-time
            synchronization.
This axis ensures that scalability is not just theoretical, but quantitatively defined and experi-
mentally validated, thereby proving that one modular ecosystem can give rise to an entire family
of robots.
C. Economic Objectives (radical affordability and manufacturability)
The third axis targets democratization through affordability. High cost has historically hindered
soft robotics from leaving the lab. We will overturn this trend by:
    • Low-cost fabrication. Use widely accessible standard 3D printing to achieve per-module

                                                1
      cost at the level of only a few HKD, enabling broad adoption in industry and daily life.
    • Design-for-manufacture at scale. Minimize post-processing, supports, and assembly
      steps; enable print-farm parallelization; target yield ≥95% and per-module fabrication
      cycle ≤60 min.
By jointly delivering 200× strength, ms-level responsiveness, and sub-millimetre precision within
a Lego-like, reconfigurable and ultra-low-cost framework, the project sets a new benchmark for
soft robotics and establishes a scalable pathway from laboratory prototypes to pervasive real-
world deployment.

Potential Impact

This proposed study can generate transformative influence at all levels of science, technology,
and society.
Scientific Impact. The developed soft robotic modules will surpass the performance limits of
existing soft robots and even compete with many conventional rigid robotic systems. The robots
with an unprecedented high load capacity (up to 200-fold force-to-weight ratio, significantly
exceeding the 50-fold threshold of ants in nature) would set new bounds on lightweight actuation.
The ultrafast response (attainable at the millisecond level) will put these systems among the
fastest demonstrated soft actuators and eliminate the existing gap between compliance and
swiftness. Additionally, the feature of high-precision control to small scales (down to ordering
small objects as coins) will transfer soft robotics to fields historically reserved for rigid high-
precision manipulators. Beyond individual performance metrics, the incorporation of modularity
(discrete crayfish-inspired segments as LEGO brick reconfigurable building blocks) opens a new
paradigm on soft robotics with an unprecedented level of robot morphological and functional
variability. Overall, these breakthroughs will find new scientific directions on bio-inspired
robotics, programmable matter, and adaptive systems.
Technological Impact. Technologically, this work holds the promise of revolutionizing soft
robot design and deployment by applying to rapid design prototyping and robot reconfigurability
to suit various uses, ranging from quadruped walking to dexterous manipulation to flapping-
wing aerobots. The simplicity and accessibility of manufacture by low-cost 3D printing reduce
the entry barrier to broad usage even further, allowing researchers, educators, and industry to
quickly iterate design without specialized infrastructure. Additionally, high payload capacity
combined with fast actuation and high-precision manipulation presents the possibility of hybrid
robot systems where soft modules replace or complement conventional stiff mechanisms to yield
lighter, safer, and more adaptive machines. The strategy holds promise to give birth to a new
class of multifunctional robotic platforms that coexist smoothly with human settings.
Societal and Economic Impact. At a societal and economic level, this proposed study could
potentially redetermine the future of soft robotics and their applications. The never-before-seen
performance and price efficiency (with modules getting printed at a few HKD per piece) reveal
mass adoption opportunities on both industrial and domestic contexts. On factory floors, they
could coexist with human labour safely and combine strength with flexibility to ensure more
productivity with reduced workplace risk. On household fronts, low-cost and adaptable soft
robots could be multi-usage assistants—in daily domestic chores as well as with the elderly
or playtime with children safely and reliably. Through this democratizing of high-performing
robotics, this proposed study could not only accelerate industrial automation but bring intelligent
machines with adaptability to daily lives and ensure a more inclusive and human-oriented
technology future.

                                                2
Background of Research, Research Plan and Methodology

Background of Research

The field of soft robotics has rapidly expanded over the past decade, driven by the demand for
machines that can safely interact with humans and adapt to unstructured environments. Various
actuation strategies have been explored, including stimuli-responsive materials powered by light,
heat, magnetic fields, or high voltage. While these approaches have demonstrated intriguing
capabilities, their practical utility has been limited by complex fabrication requirements, high
production costs, and stringent control conditions, all of which hinder scalability and widespread
adoption in engineering and industrial contexts [1–5].
Among the many explored strategies, pneumatic actuators have emerged as one of the most
promising candidates for building versatile soft robots due to their structural simplicity, control-
lability, and cost-effectiveness [6–9]. Their basic operating principle—inducing deformation
through inflation and deflation of sealed chambers—provides compliance and adaptability un-
matched by rigid counterparts. However, the development of pneumatic actuators has historically
faced a persistent performance dilemma. Early fully soft actuators offered excellent deforma-
bility but lacked load-bearing capacity and fast response speed [10–13]. Later efforts that
incorporated stiffer materials improved mechanical strength but inevitably reduced flexibility
and responsiveness, thus introducing new trade-offs [14, 15].
To mitigate these trade-offs, researchers have investigated constrained designs. Fiber-reinforced
actuators, for example, suppress overexpansion under pressure and enhance force output, but
their performance depends sensitively on fiber density: sparse reinforcement risks material
rupture, while overly dense reinforcement restricts motion [16–18]. More recently, textile-based
constraints have been employed, embedding pneumatic chambers in woven fabrics to balance
deformability and strength [19–21]. Although promising, these approaches introduce challenges
in fabrication complexity, geometric accuracy, and controllability of deformation.
As a result, three key challenges continue to limit the advancement and practical application of
pneumatic soft robots:
   1. Performance Integration: It remains difficult to achieve actuator designs that combine
      high load-bearing capacity, rapid response speed, large deformation range, and precise
      controllability in a single, simple architecture. Existing solutions typically optimize one
      or two performance metrics at the expense of others, leaving a gap for a truly integrated
      design [22–25].
   2. Modularity and Scalability: Most current pneumatic actuators are task-specific and
      lack generality. Their limited compatibility and non-standardized architectures make it
      challenging to assemble them into multifunctional, reconfigurable, or scalable robotic
      systems that can adapt across application domains.
   3. Fabrication and Cost: Although pneumatic actuators are simpler than many stimuli-
      responsive alternatives, advanced reinforcement or textile-based designs often require
      labor-intensive or specialized fabrication techniques. These processes increase production
      costs and reduce accessibility, particularly when aiming for mass production or deployment
      in cost-sensitive applications such as household robotics or healthcare devices.
These challenges reveal a critical gap: current pneumatic actuators still struggle to achieve
high performance, modularity, and manufacturability in a unified design. Addressing these
limitations motivates the proposed research plan, which seeks to establish a new framework for
next-generation pneumatic soft actuators and robotic modules.

                                                 1
To tackle this gap, we propose a novel soft-rigid-coupled pneumatic actuator inspired by the
anatomical features of crayfish, which exhibit a characteristic “rigid outside, soft inside” structure
(Figure 1a-1d) enabling efficient bending and propulsion [26, 27]. Motivated by this biological
model, we designed a new type of rigid shell constraint structure (Figure 1e-1f) that serves
as the outer layer of a flexible air chamber based on two key geometric features observed in
crayfish exoskeletons, which suppresses excessive deformation of the flexible chamber under
high pressure to prevent mechanical failure and guides motion effectively to ensure stable and
controllable angular output. The actuator consists of modular, pin-connected rigid segments
that offer structural reinforcement and precise joint rotation, while internal inflatable latex tubes
provide actuation upon pressurization. This architectural separation between actuation and
support allows for independent optimization of mechanical strength and flexibility. Moreover,
the rigid shell components can be rapidly fabricated through low-cost 3D printing and assembled
within 3–5 minutes (Figure 1g), significantly improving manufacturability and scalability.

Research Plan

Building upon this bio-inspired actuator architecture, the research will be carried out over
24 months, divided into three major thrusts and structured milestones. The plan follows a
progression from fundamental actuator design, to modular robotic architectures, and finally to
scalable manufacturing and application demonstrations. Each thrust is designed to build upon
the prior results, ensuring coherence and steady advancement toward the final objectives.
Thrust I: High-Performance Actuator Design and Optimization (Month 1–8). The initial
stage will establish the foundational principles of soft–rigid coupled pneumatic actuators, draw-
ing inspiration from crayfish locomotion. Analytical models and finite element simulations will
be developed to predict nonlinear interactions between rigid skeletons and compliant chambers
(Figure 1h). Three key performance targets will guide optimization:
    • High load capacity: achieving force-to-weight ratios exceeding 200, significantly beyond
      biological exemplars such as ants (50× self-weight).
    • Fast response: reducing actuation latency to the microsecond regime, enabling real-time
      adaptability.
    • Precise control: manipulating delicate objects (e.g., coins) to demonstrate fine dexterity.
This thrust will also explore alternative chamber geometries, skeleton embedding strategies,
and material combinations to push the boundaries of performance. Iterative prototyping will
validate simulation predictions, and experimental setups will be designed to benchmark against
state-of-the-art soft actuators.
Milestones:
    • Month 3: Complete analytical and FEM models for soft–rigid coupled actuators, estab-
      lishing validated predictive capability.
    • Month 6: Fabricate first-generation prototypes and achieve >100× force-to-weight ratio
      in benchtop testing.
    • Month 8: Deliver refined actuator prototypes that meet or exceed all three performance
      targets in controlled laboratory environments.
Thrust II: Modular Assembly and Robotic Architectures (Month 6–16). The second thrust
builds upon the optimized actuators to create modular robotic systems. Each actuator will func-
tion as a reconfigurable “unit cell” that can be assembled into diverse morphologies. Inspired by
the segmentation of crayfish bodies and the versatility of LEGO-like modularity, a standardized

                                                  2
interface design will be developed to ensure robust mechanical coupling and reliable pneumatic
connectivity. Simulation libraries and system-level design rules will be created to guide the
assembly process, enabling rapid prototyping of new robot designs.
Both digital simulations and experimental validation will be pursued in parallel. Simulation-
based exploration will allow rapid evaluation of various robotic configurations—including
quadrupedal walking robots, biomimetic fish-like swimmers, dexterous manipulators, and bio-
inspired flapping-wing robots—before proceeding to physical fabrication. Modular adaptability
will be a key criterion, ensuring that modules can be reused and reconfigured across multiple
prototypes without major redesign.
Milestones:
    • Month 10: Establish standardized module-to-module interfaces, covering both mechanical
      locking mechanisms and pneumatic distribution.
    • Month 12: Demonstrate proof-of-concept modular assembly into a functional quadruped
      robot with validated walking performance.
    • Month 14: Extend modular assembly to at least two additional prototypes, including a
      dexterous gripper and a biomimetic swimming robot.
    • Month 16: Create a small but versatile “library” of modules, showcasing reconfiguration
      into at least four distinct robotic types.
Thrust III: Scalable Fabrication and Application Demonstrations (Month 14–24). The
final thrust focuses on scaling up fabrication processes and validating real-world applications.
Actuator fabrication will leverage low-cost, high-throughput 3D printing, aiming to achieve per-
unit costs of only a few HKD, thus ensuring accessibility and scalability. Materials and printing
parameters will be systematically optimized for durability, reproducibility, and performance
consistency across large batches.
To demonstrate societal relevance, two representative application domains will be targeted:
    • Industrial setting: collaborative robots capable of lifting significant loads while main-
      taining inherent compliance, enabling safe human–robot interaction on factory floors.
    • Household setting: affordable domestic robots that perform common manipulation tasks
      such as sorting items, assisting with cooking preparation, or tidying, emphasizing safety
      and user-friendliness.
Demonstration robots will undergo benchmarking against existing state-of-the-art soft robots,
focusing on criteria such as load capacity, speed, dexterity, modular adaptability, and long-
term reliability. Feedback from these demonstrations will guide refinements to the fabrication
pipeline and modular framework.
Milestones:
    • Month 18: Establish a robust low-cost 3D printing pipeline with proven reproducibility
      across multiple actuator units.
    • Month 20: Demonstrate an industrial prototype (collaborative factory co-worker robot)
      with validated load-bearing performance and safe human interaction.
    • Month 22: Deliver a household prototype capable of performing daily tasks under realistic
      conditions.
    • Month 24: Conduct comprehensive benchmarking of all prototypes, evaluating against
      academic and industrial benchmarks, and assess broader societal/economic impact.
Overall, this research plan systematically integrates actuator-level innovation, modular robotic
design, and scalable real-world demonstrations. The 24-month roadmap is structured with

                                               3
explicit milestones to ensure steady progress, accountability, and alignment with project goals.
By the conclusion of the project, outcomes will not only advance the fundamental science of
soft robotics but also establish a practical foundation for industrial and household adoption of
modular, high-performance soft robots.

Methodology

The proposed research is to be executed using a synergistic approach that combines theoretical
modeling, computational optimization, advanced fabrication, and experimental verification. The
multi-faceted strategy guarantees strong scientific research while keeping real applications in
full view at all times.
Modeling and Simulation. Analytical models will be established to capture the nonlinear
mechanics of soft–rigid coupled pneumatic actuators. These models will describe chamber
pressurization, material deformation, and load transfer through rigid reinforcements. Comple-
mentary finite element simulations will be performed to investigate the dynamic response and
optimize actuator geometries. Parameter sweeps and sensitivity analysis will identify key design
variables influencing force output, strain capacity, and response speed.
Computational Design and Optimization. A computational pipeline will be developed to
guide actuator and modular system design. Gradient-based optimization and heuristic search
methods will be employed to explore high-dimensional design spaces. When appropriate,
machine learning–assisted surrogate models will be integrated to accelerate design iterations
and capture nonlinear interactions beyond analytical tractability. Optimization will be conducted
with explicit performance targets (e.g., maximizing force-to-weight ratio, minimizing actuation
latency).
Fabrication Techniques. To ensure both high performance and economic scalability, actuators
will be fabricated using accessible techniques including multi-material 3D printing. Hybrid
fabrication protocols will be explored to embed rigid reinforcements into compliant chambers
without compromising integrity. Standardized connectors will be designed to enable rapid
assembly of modular robotic systems. Cost analysis will accompany fabrication, with the goal
of achieving unit costs as low as a few HKD.
Experimental Characterization. A systematic experimental framework will be established
to validate actuator and robotic performance. Key metrics—including load capacity, force-
to-weight ratio, response time, and positional accuracy—will be quantified using mechanical
testing rigs, high-speed imaging, and pressure control systems. For modular assemblies, task-
based demonstrations (e.g., locomotion, manipulation, swimming) will be performed to evaluate
versatility and robustness. Experimental data will also provide feedback to refine computational
models, forming an iterative design-validation loop.
Application Demonstrations. Finally, prototypes will be deployed in representative industrial
and household scenarios. Collaborative robots will be tested in controlled factory-like envi-
ronments to demonstrate safe human–robot interaction under load-bearing tasks. In parallel,
household prototypes will be designed to execute common domestic functions such as picking
up objects or assisting with daily chores. These demonstrations will provide a proof-of-concept
of the societal and economic potential of the proposed research.
Overall, the strategy emphasizes tight integration of theory, computation, fabrication, and
experiment, so that the intended research is both scientifically sound and practically useful.



                                               4
Illustrations


       a                                   b                      c                          d        A        B

                                                                                                                                  A---A                B---B
                                                                                                       A       B                   df                  db



                                                           = 0°                                           Rf       Rb
                                       =                                                                                                               10 mm


          e                   Design of pneumatic actuator module                                                       f
                                                                                             ′
                                               ′
                               ′

                                                                                                                                              =    ′



                                                                                                 ′


                                   ′               ′



       g                                               3D printed unit segments
                       STL
                       File
                                                                                                     back cap               middle unit           front cap

                                                                                                                        plug A     plug B

                                                                   Assembly                                                      latex tube

             tube


                                                                                  0+
                                                        pins
                                                                      prestretched latex tube                                             0




                                                                                         i
        Cross-Section View




                                                                         Strain

                                                                  0.00            1.20
                                                                                                                         Precision
                                                                                                      Speed                             Modularity
                                                                                                                     Crayfish
                                                                   Stress (MPa)                                      Inspired
                                                                                         Strength                   Pneumatic                     Affordability
        Side View




                                                                  0.00            7.50                                 Robot


       h                                                                                 Household                                                     Industry


Figure 1. Bionic design, fabrication, and performance of the pneumatic actuator module.
(a) Frontal view of the relaxed state of the crayfish body. (b) Frontal view of the contracted
state of the crayfish body. (c) 3D reconstruction of the crayfish body from CT scans. (d) Cross-
sectional views of the crayfish body, including one transverse and two longitudinal sections. (e)
Design concept of the pneumatic actuator unit, inspired by the cross-sectional anatomy shown
in d. (f) Motion demonstration of two pneumatic actuator units transitioning from an initial
straight state to a fully bent state. (g) Fabrication and assembly process of the crayfish-inspired
soft-rigid-coupled pneumatic actuator. (h) Finite element simulation of the actuator showing
both cross-sectional and side views. (i) Advantages and application scenario of crayfish-inspired
pneumatic soft robots.


                                                                           1
Pathways to Impact Statement

The proposed research on crayfish-inspired soft–rigid coupled pneumatic actuators has potential
to generate wide-ranging benefits that extend well beyond academic advances. By enabling soft
robotic systems that combine strength, speed, precision, modularity, and affordability, this work
addresses pressing needs in healthcare, industry, daily life, and disaster response (Figure 1i).
The following statement outlines the expected beneficiaries, the timeframe and forms of impact,
the actions that will be taken to realize these impacts, and the risks and mitigation strategies.

(a) Potential Beneficiaries

Short-term (1–3 years):
    • Medical and rehabilitation professionals who require safe robotic assistance for patient
      care (e.g., wearable rehabilitation devices, physiotherapy tools).
    • Industrial engineers and manufacturers seeking lightweight, reconfigurable, and affordable
      robotic systems for rapid prototyping and safe human–robot collaboration.
    • Educators and students who will gain access to low-cost, easy-to-build robotic modules
      for STEM training.
Medium-term (4–10 years):
    • Healthcare systems and patients who benefit from wearable assistive robots that enhance
      mobility, support rehabilitation, and reduce caregiver burden.
    • Disaster response agencies using highly maneuverable robots for search-and-rescue in
      confined or hazardous environments.
    • SMEs and startups leveraging affordable, customizable soft robotic solutions in logistics,
      service, and consumer robotics.
Long-term (10+ years):
    • Society at large, internationally, through the widespread adoption of safe, adaptive, and
      affordable robots in daily life (household robots, elderly care, education, entertainment).
    • The environment, through more sustainable manufacturing of robotic systems (3D-print-
      based fabrication reduces waste and energy use compared with conventional machining).
    • The economy, by creating new industries in bioinspired soft robotics and strengthening
      Hong Kong’s global competitiveness in advanced robotics.

(b) Timeline, Modes of Benefit, and Measurable Outcomes

    • Short-term: Beneficiaries will gain from open-access publications, prototyped devices,
      and demonstration platforms. Measurable outcomes include the fabrication of low-cost,
      modular actuator kits distributed to partner universities and industries, enabling wider
      engagement.
    • Medium-term: Hospitals and rehabilitation centers will begin adopting assistive devices
      powered by actuators, with measurable outcomes such as clinical pilot studies showing
      improvements in patient mobility and safety. Industrial adoption can be measured by
      reduced workplace accidents and improved productivity in human–robot collaborative
      tasks.
    • Long-term: Societal benefits will become visible through the commercialization and
      mass deployment of soft robots. Objective indicators include:

                                               1
        – the emergence of spinoff companies and licensing agreements;
        – measurable cost reductions in assistive and service robotics (target: devices at 1/10th
          of current market cost);
        – improved quality of life for elderly populations measured through care-assistance
          studies;
        – contribution to UN Sustainable Development Goals (e.g., Goal 3: Good Health and
          Well-being; Goal 9: Industry, Innovation and Infrastructure).

(c) Pathways and Actions to Enhance Impact

To maximize the likelihood of achieving the identified impacts, the research team and host
university will implement the following measures:
  1. Industry engagement: Establish partnerships with local and regional robotics companies,
     healthcare providers, and rehabilitation centers to co-develop pilot applications.
  2. Technology transfer: File patents for key actuator designs and modular integration
     schemes; collaborate with the university’s Technology Transfer Office to explore licensing
     and commercialization opportunities.
  3. Public engagement: Disseminate outcomes through outreach events, media coverage,
     and exhibitions to raise societal awareness of safe and affordable robotics.
  4. Policy and standards contributions: Engage with policymakers and standards organiza-
     tions to provide expert input on safe deployment of collaborative soft robots in industrial
     and domestic settings.
  5. International collaboration: Build links with overseas research labs and NGOs in dis-
     aster management and elderly care, ensuring knowledge transfer beyond Hong Kong.

(d) Risks and Mitigation Strategies

   • Risk: Limited adoption due to skepticism about robustness of soft robots.
     Mitigation: Conduct benchmarking against rigid robots, provide standardized perfor-
     mance metrics, and publish open validation data to build trust among users and industry.
   • Risk: Barriers to commercialization (e.g., regulatory approval for medical devices).
     Mitigation: Engage early with regulatory agencies and medical partners to align prototypes
     with safety and compliance standards; target non-medical assistive devices as initial
     commercialization route.
   • Risk: High cost of scaling beyond laboratory demonstrations.
     Mitigation: Leverage radical affordability of 3D printing and establish partnerships with
     local manufacturers to streamline production pipelines.
   • Risk: Ethical concerns about robots replacing human jobs.
     Mitigation: Emphasize collaborative roles of soft robots that enhance human safety and
     productivity rather than substitution; highlight applications in healthcare, education, and
     disaster recovery where human capacity is limited.
This research delivers affordable, reconfigurable, high-performance soft robots with benefits
spanning education, healthcare, industry, and society. Short-term impacts include prototypes
and kits for educators and engineers; medium- and long-term impacts include safer workplaces,
rehabilitation advances, accessible domestic robotics, and new industries. Through collabora-
tion, technology transfer, and policy engagement—with clear risk-mitigation measures—Hong
Kong will be positioned as a leader in the next wave of bioinspired robotics innovation.

                                               2
References

 [1] Shuo Zhang, Xingxing Ke, Qin Jiang, Zhiping Chai, Zhigang Wu, and Han Ding. Fab-
     rication and functionality integration technologies for small-scale soft robots. Advanced
     Materials, 34(52):2200671, 2022.

 [2] Gianni Stano and Gianluca Percoco. Additive manufacturing aimed to soft robots fabrica-
     tion: A review. Extreme Mechanics Letters, 42:101079, 2021.

 [3] Florian Hartmann, Melanie Baumgartner, and Martin Kaltenbrunner. Becoming sustain-
     able, the new frontier in soft robotics. Advanced Materials, 33(19):2004413, 2021.

 [4] Liuchao Jin, Xiaoya Zhai, Wenbo Xue, Kang Zhang, Jingchao Jiang, Mahdi Bodaghi, and
     Wei-Hsin Liao. Finite element analysis, machine learning, and digital twins for soft robots:
     state-of-arts and perspectives. Smart Materials and Structures, 34(3), 2025.

 [5] Zixi Chen, Federico Renda, Alexia Le Gall, Lorenzo Mocellin, Matteo Bernabei, Théo
     Dangel, Gastone Ciuti, Matteo Cianchetti, and Cesare Stefanini. Data-driven methods
     applied to soft robot modeling and control: A review. IEEE Transactions on Automation
     Science and Engineering, 2024.

 [6] Charbel Tawk and Gursel Alici. A review of 3D-printable soft pneumatic actuators and sen-
     sors: research challenges and opportunities. Advanced Intelligent Systems, 3(6):2000223,
     2021.

 [7] Matheus S Xavier, Charbel D Tawk, Ali Zolfagharian, Joshua Pinskier, David Howard,
     Taylor Young, Jiewen Lai, Simon M Harrison, Yuen K Yong, Mahdi Bodaghi, et al. Soft
     pneumatic actuators: A review of design, fabrication, modeling, sensing, control and
     applications. IEEE Access, 10:59442–59485, 2022.

 [8] Hong Kai Yap, Hui Yong Ng, and Chen-Hua Yeow. High-force soft printable pneumatics
     for soft robotic applications. Soft Robotics, 3(3):144–158, 2016.

 [9] Yang Yang, Yonghua Chen, Yingtian Li, Michael ZQ Chen, and Ying Wei. Bioinspired
     robotic fingers based on pneumatic actuator and 3d printing of smart material. Soft Robotics,
     4(2):147–162, 2017.

[10] Bobak Mosadegh, Panagiotis Polygerinos, Christoph Keplinger, Sophia Wennstedt,
     Robert F Shepherd, Unmukt Gupta, Jongmin Shim, Katia Bertoldi, Conor J Walsh, and
     George M Whitesides. Pneumatic networks for soft robotics that actuate rapidly. Advanced
     Functional Materials, 24(15):2163–2170, 2014.

[11] Yunquan Li, Yonghua Chen, Tao Ren, Yingtian Li, and Shiu Hong Choi. Precharged
     pneumatic soft actuators and their applications to untethered soft robots. Soft Robotics,
     5(5):567–575, 2018.

[12] Seppe Terryn, Joost Brancart, Dirk Lefeber, Guy Van Assche, and Bram Vanderborght.
     Self-healing soft pneumatic robots. Science Robotics, 2(9):eaan4268, 2017.

[13] Yichen Zhai, Albert De Boer, Jiayao Yan, Benjamin Shih, Martin Faber, Joshua Speros,
     Rohini Gupta, and Michael T Tolley. Desktop fabrication of monolithic soft robotic devices
     with embedded fluidic control circuits. Science Robotics, 8(79):eadg3792, 2023.

                                                1
[14] Wenbo Xue, Liuchao Jin, Bingcong Jian, and Qi Ge. Origami-based flexible robotic
     grippers via hard-soft coupled multimaterial 3D printing. Soft Robotics, 2025.

[15] Jinhua Zhang, Tao Wang, Jin Wang, Michael Yu Wang, Baotong Li, John XJ Zhang,
     and Jun Hong. Geometric confined pneumatic soft–rigid hybrid actuators. Soft Robotics,
     7(5):574–582, 2020.

[16] Jiaqing Xiong, Jian Chen, and Pooi See Lee. Functional fibers and fabrics for soft robotics,
     wearables, and human–robot interface. Advanced Materials, 33(19):2002640, 2021.

[17] Yi Sun, Hui Feng, Ian R Manchester, Raye Chen Hua Yeow, and Peng Qi. Static modeling
     of the fiber-reinforced soft pneumatic actuators including inner compression: Bending in
     free space, block force, and deflection upon block force. Soft robotics, 9(3):451–472, 2022.

[18] Huxiao Yang, Xiaofeng Yin, Chao Zhang, Baihong Chen, Peng Sun, and Yan Xu. Weaving
     liquid crystal elastomer fiber actuators for multifunctional soft robotics. Science Advances,
     11(8):eads3058, 2025.

[19] Miao Feng, Dezhi Yang, Carmel Majidi, and Guoying Gu. High-speed and low-energy
     actuation for pneumatic soft robots with internal exhaust air recirculation. Advanced
     Intelligent Systems, 5(4):2200257, 2023.

[20] Dezhi Yang, Miao Feng, Jianing Sun, Yexun Wei, Jiang Zou, Xiangyang Zhu, and Guoying
     Gu. Soft multifunctional bistable fabric mechanism for electronics-free autonomous robots.
     Science Advances, 11(5):eads8734, 2025.

[21] Genliang Chen, Yongzhou Long, Siyue Yao, Shujie Tang, Junjie Luo, Hao Wang, Zhuang
     Zhang, and Hanqing Jiang. A non-electrical pneumatic hybrid oscillator for high-frequency
     multimodal robotic locomotion. Nature Communications, 16(1):1449, 2025.

[22] Dezhi Yang, Miao Feng, and Guoying Gu. High-stroke, high-output-force, fabric-lattice
     artificial muscles for soft robots. Advanced Materials, 36(2):2306928, 2024.

[23] Lishuai Jin, Yueying Yang, Bryan O Torres Maldonado, Sebastian David Lee, Nadia
     Figueroa, Robert J Full, and Shu Yang. Ultrafast, programmable, and electronics-free soft
     robots enabled by snapping metacaps. Advanced Intelligent Systems, 5(6):2300039, 2023.

[24] Jue Wang and Alex Chortos. Control strategies for soft robot systems. Advanced Intelligent
     Systems, 4(5):2100165, 2022.

[25] Xinjia Huang, Zihao Yuan, Xinyu Yang, and Guoying Gu. Precise control of soft robots
     amidst uncertain environmental contacts and forces. IEEE Transactions on Robotics, 2024.

[26] Binodhya Wijerathne, Ting Liao, Kostya Ostrikov, and Ziqi Sun. Bioinspired robust
     mechanical properties for advanced materials. Small Structures, 3(9):2100228, 2022.

[27] Yaohui Chen, Sing Le, Qiao Chu Tan, Oscar Lau, Fang Wan, and Chaoyang Song. A
     reconfigurable hybrid actuator with rigid and soft components. In 2017 IEEE International
     Conference on Robotics and Automation (ICRA), pages 58–63. IEEE, 2017.




                                                2
Related Experience or Participation in Research Projects

My academic training and research trajectory have been closely aligned with the fields of 3D/4D
printing, smart materials, metamaterials, and soft robotics. Over the past five years, I have
participated in multiple interdisciplinary research projects that integrate mechanics, materials
science, robotics, and computational methods. These projects span both fundamental inves-
tigations and application-oriented developments, with outcomes including peer-reviewed pub-
lications, patents, international collaborations, and open-source dissemination. The following
sections summarize my related experiences.

1. Research in Soft Robotics and Origami-Inspired Systems

A central part of my research has focused on advancing the design and fabrication of soft
robotic systems through bioinspired and origami-based strategies. In collaboration with South-
ern University of Science and Technology (Prof. Qi Ge) and Tongji University, I co-developed
a novel class of origami-based flexible robotic grippers fabricated via hard–soft coupled mul-
timaterial 3D printing. The project systematically examined how geometric design parameters
influence kinematic behavior, force–displacement characteristics, and stiffness. By combining
parametric modeling with experimental validation, we established clear correlations between
hinge thickness, crease patterns, and actuation performance. This study not only demonstrated
the feasibility of multimaterial 3D printing in producing highly adaptive robotic grippers but
also provided a generalizable design framework for origami-inspired soft robotics.
Building on this effort, I further contributed to a comprehensive review of origami robots that
surveyed design methodologies, actuation mechanisms, and fabrication technologies. My con-
tribution centered on evaluating the role of additive manufacturing, including fused deposition
modeling, direct ink writing, and multi-material printing, in overcoming the limitations of tradi-
tional origami fabrication. The work provided a critical perspective on how advances in digital
fabrication can drive the scalability and integration of origami robots into practical applications,
from surgical tools to reconfigurable structures. Collectively, these experiences equipped me
with expertise in coupling geometry, materials, and digital fabrication to design deployable and
adaptive robotic systems.

2. Computational Modeling, Machine Learning, and Digital Twins for Soft Robots

Another significant research direction involves integrating computational mechanics and artifi-
cial intelligence into the modeling and control of soft robots. I co-authored a topical review on
finite element analysis, machine learning, and digital twins for soft robotics, which has since
received broad recognition. This work analyzed the challenges posed by nonlinear material
behavior and large deformations in soft robotics, and proposed computational approaches for
material modeling, structural optimization, and real-time control.

3. Research in 3D/4D Printing and Metamaterials

During my doctoral research, I have devoted substantial efforts to advancing additive manufac-
turing and programmable materials, which form the technological foundation for my current
exploration of soft robotics. Specifically, my work focuses on 3D/4D printing of smart struc-
tures, metamaterials, and multifunctional composites, aiming to endow artificial systems with


                                                 1
adaptability, responsiveness, and mechanical tunability.
A central line of my research investigates 4D printing of stimuli-responsive structures, where
printed architectures evolve shapes or functions under external stimuli. I have developed ma-
chine learning-assisted inverse design methods to achieve precise control of strain fields and
programmable deformation in hierarchical architectures. These approaches allow printed com-
ponents to exhibit user-defined actuation pathways, laying the groundwork for soft actuators with
tailored motion profiles. In parallel, I proposed optimization strategies to enhance the efficiency
of forward prediction and inverse design, significantly improving design-to-fabrication fidelity.
Another direction of my work explores the integration of unconventional materials into extrusion-
based additive manufacturing. In particular, I studied the co-printing of low-melting-point alloys
and polymers, resulting in multifunctional composites with tunable stiffness, high energy ab-
sorption, and embedded electrical conductivity. These printed structures demonstrated potential
for self-healing, impact protection, and embedded sensing—functions highly relevant for devel-
oping soft robots that require both compliance and robustness.
Beyond material integration, I also led collaborative projects on metamaterial design and sac-
rificial mold printing. For example, I participated in the design of lightweight, auxetic, and
energy-absorbing structures that provide programmable mechanical response. By leveraging
stress-guided topology optimization and modular interlocking mechanisms, these studies con-
tributed new methodologies for constructing scalable and deployable structures with high energy
dissipation capacity. Such work not only enriches the library of 3D-printable metamaterials but
also informs the modularity and safety of soft robotic architectures.
Through these projects, I have built strong expertise in multi-material 3D/4D printing, struc-
tural characterization, and mechanical optimization. More importantly, this research provides a
critical foundation for the proposed project: the programmable architectures and hybrid fabrica-
tion strategies developed in my PhD research directly enable the creation of soft–rigid coupled
pneumatic actuators with enhanced controllability, durability, and scalability. In this way, my
work in 3D/4D printing and metamaterials naturally evolves into the current proposal, bridging
fundamental material innovation with application-driven soft robotics.

4. International and Interdisciplinary Research Engagement

My participation in international research internships and visiting scholar programs has broad-
ened the scope of my academic training. At McGill University (Mitacs Globalink) and Westlake
University, I worked on projects spanning underwater robotics and energy systems. More
recently, I have been engaged as visiting scholar at Southern University of Science and Technol-
ogy, Shenzhen University, and (upcoming) California Institute of Technology with Prof. Chiara
Daraio. These experiences have fostered an interdisciplinary perspective, allowing me to connect
robotics with materials science, computational modeling, and manufacturing technologies.
Overall, my research experience integrates three synergistic areas: (i) design and fabrication of
origami-inspired and bioinspired soft robotic systems, (ii) computational modeling and machine
learning for performance prediction and control, and (iii) advanced additive manufacturing
and 4D printing of smart materials and metamaterials. These experiences have resulted in
high-impact publications, competitive awards, and collaborative networks across Asia, North
America, and Europe. Collectively, they provide a strong foundation for pursuing innovative
research on soft–rigid coupled pneumatic actuators and modular robotic systems under the
current proposal.


                                                2
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
⚫    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
⚫    The nominee should complete Part A of the referee’s report.
⚫    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
⚫    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
⚫    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A Nominee’s Particulars              (to be completed by the nominee)
Name of Nominee :       Jin
                        Liuchao
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^:
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
⚫   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
⚫   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
⚫   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
⚫   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
⚫   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
⚫   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
⚫   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known Mr. Liuchao Jin for more than two years. Our first contact was established when
he reached out to me to explore potential collaboration opportunities in the fields of intelligent
materials and AI-driven design. Since then, we have engaged in sustained academic
collaboration, working together on research that integrates advanced manufacturing, architected
metamaterials, and computational modeling. Through this collaboration, I have had the
opportunity to observe his research skills, creativity, and scholarly independence in an academic
context.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                            Excellent   Very Good        Good           Average          No basis
                            (top 5%)    (top 6-20%) (top 21-50%)       or below            for
                                                                      (lower than       judgment
                                                                         50%)
Intellectual capacity                     ☐             ☐                  ☐               ☐
Ability for conducting
scholastic research
                                           ☐             ☐                 ☐               ☐
Knowledge of proposed
research study
                                           ☐             ☐                 ☐               ☐
Analytical power and
reasoning
                                           ☐             ☐                 ☐               ☐
Judgment                                   ☐             ☐                 ☐               ☐
Imagination and
originality
                                           ☐             ☐                 ☐               ☐
Motivation and
perseverance
                                           ☐             ☐                 ☐               ☐
Skills of writing and
argumentation
                                           ☐             ☐                 ☐               ☐
Capacity for independent
work
                                           ☐             ☐                 ☐               ☐
Reliability and sense of
responsibility
                                           ☐             ☐                 ☐               ☐
Leadership experience                     ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

Please refer to the attached sheet.




                                                3
                                                      JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                        Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: QI GE                                   Title: Prof
     University / Organisation: Southern University of Science and Technology
     Position: Professor/Associate Dean
     E-mail Address: <EMAIL>
     Telephone Number#: +86-139 2348 5190
     (# Please provide country code and area code, e.g. +86-10-1234567)
⚫     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
     I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date: 2025.8.26




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
Response to Question 3: Research Ability and Potential of the Nominee

To Whom It May Concern,
I am happy to highly recommend Mr. Liuchao Jin. Based on my direct interaction with him, I
have been consistently impressed by his intellectual drive, independence, and ability to tackle
challenging research questions. This is a strong combination of technical depth, creativity, and
determination which makes him exceptionally fit for the Junior Research Fellow Scheme (JRFS).
Liuchao has even shown a level of research maturity well beyond that of the ordinary doctoral
student. His research spans some emerging areas—3D/4D printing, adaptive and programmable
materials, metamaterials, and machine learning—and, remarkably, can combine them in a way
which reveals both creativity and a clear direction. Rather than base his work on established
practices, he himself identifies important gaps in our knowledge, develops his own research
agendas, and advances them with pure rigor and clarity. As a quick example, work on the
machine-learning-based inverse design of programmable structures demonstrates him skilled in
joining high-level computational tools with detailed fundamental knowledge of materials
behavior. This has already produced articles in quality journals and bodes a research career of
important influence.
What sets him apart even more is his dual competence in physical systems and computational
modeling. While there are a lot of bright young researchers who regard machine learning as a
technical afterthought, Liuchao bases his work on mechanics, constraints of fabrication, and
design principles of functionality. This makes him build not only highly accurate but physically
informative models. Interpreting computational results in terms of real material systems is a rare
and incredibly valuable ability, and it prepares him to make unique contributions in
interdisciplinary work.
Besides technical skills, Liuchao is a quick learner and a reliable team player. He learns quickly
from new challenges, develops ideas which propel projects ahead, and summarizes findings
clearly and authoritatively. I have witnessed him beginning projects, performing with a minimum
of supervision, and making a constructive input in groups. Such skills, aided by a notable
publishing record and creative ideas for research, bear witness both to his capabilities at hand and
future prospects as a solo researcher.
In conclusion, I have no hesitation in giving Mr. Liuchao Jin my strongest possible
recommendation. His creativity, intellectual breadth, and proven ability to deliver high-quality
research make him an outstanding candidate for the JRFS. I am confident that he will make
significant and lasting contributions to his field and grow into a leader in academic research.
Sincerely,




Qi Ge
Professor
Department of Mechanical and Energy Engineering
Southern University of Science and Technology,
1088 Xueyuan Avenue, Shenzhen, China, 518055
                                                  5
                                                          JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
⚫    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
⚫    The nominee should complete Part A of the referee’s report.
⚫    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
⚫    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
⚫    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A Nominee’s Particulars              (to be completed by the nominee)
Name of Nominee :       Jin
                        Liuchao
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^:
^ Please do not mention the name of the supporting university


Part B Academic Referee’s Report               (to be completed by the referee)
Notes to Referee:
⚫   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
⚫   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
⚫   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
⚫   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
⚫   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
⚫   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
⚫   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known Mr. Liuchao Jin for more than three years, beginning when I was a postdoctoral
researcher at CUHK. Since then, we have maintained close collaboration on numerous research
projects, which have led to 16 co-authored publications. Through this long-term and productive
collaboration, I have had extensive opportunities to observe his research skills, intellectual
independence, and professional dedication.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                            Excellent   Very Good        Good           Average          No basis
                            (top 5%)    (top 6-20%) (top 21-50%)       or below            for
                                                                      (lower than       judgment
                                                                         50%)
Intellectual capacity                     ☐              ☐                 ☐               ☐
Ability for conducting
scholastic research
                                           ☐             ☐                 ☐               ☐
Knowledge of proposed
research study
                                           ☐             ☐                 ☐               ☐
Analytical power and
reasoning
                                           ☐             ☐                 ☐               ☐
Judgment                                   ☐             ☐                 ☐               ☐
Imagination and
originality
                                           ☐             ☐                 ☐               ☐
Motivation and
perseverance
                                           ☐             ☐                 ☐               ☐
Skills of writing and
argumentation
                                           ☐             ☐                 ☐               ☐
Capacity for independent
work
                                           ☐             ☐                 ☐               ☐
Reliability and sense of
responsibility
                                           ☐             ☐                 ☐               ☐
Leadership experience                     ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

Please refer to the attached sheet.




                                                3
                                                      JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                        Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: JINGCHAO JIANG                                  Title: Dr
     University / Organisation: University of Exeter
     Position: Lecturer
     E-mail Address: <EMAIL>
     Telephone Number#: +44-7587434550
     (# Please provide country code and area code, e.g. +86-10-1234567)
⚫     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
     I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date: 23/8/2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
Response to Question 3: Research Ability and Potential of the Nominee

To Whom It May Concern,
It is my distinct honour and privilege to strongly recommend Mr. Liuchao Jin for the Junior
Research Fellow Scheme. I have known Liuchao for over three years, both as my laboratory
colleague when I held a postdoctoral position, and subsequently, a long-term co-author after I
joined the University of Exeter. During the past 3 years, we collectively co-authored over fifteen
research articles. Through close co-operation, I personally have developed a deep familiarity with
his great research potential, exemplary work ethic, and superb personal character.
Among the many young researchers I have worked with, Liuchao truly stands out. One of his most
striking qualities is his meticulousness. Whether in experimental design, data analysis, or
manuscript preparation, he demonstrates a level of rigour and precision that far exceeds the norm
for his career stage. I could always rely on him to identify subtle issues others might overlook,
ensuring that our work withstands the highest scientific scrutiny. This depth of care is rare and
invaluable.
Liuchao is also an extraordinary idea generator. I vividly recall the many late-night discussions we
had in the lab—he consistently brought forward original, bold, yet well-reasoned concepts, which
he then refined into concrete research questions and feasible methodologies. This good ability to
bridge imagination and practicality is precisely what makes a successful independent researcher.
What’s every bit as intimidating is his flawless execution. Liuchao possesses a great ability to
manage a stream of projects simultaneously without ever compromising on quality. Not only does
he deliver on schedule, but quite frequently ahead of schedule, and produces work which is both
rigorous and influential. This combination of creativity, celerity, and reliability is downright
unheard of, and I’ve scarcely seen, even among seasoned researchers. His eagerness to learn even
further solidifies his portfolio. Liuchao voluntarily gets out of the box of what’s known to him—
be it in machine learning, soft robotics, or entirely different areas—and quickly develops a
professional-level mastery. This versatility earns him a distinctive interdisciplinary advantage,
where he could examine things from diverse viewpoints and devise innovative solutions.
Besides his potential for research, Liuchao is an excellent team player and leader. Friendly,
unifying, and willing to contribute time, he easily earns people’s trust and friendship. He
significantly contributes to the intellectual and social life of whatever research team into which he
is assimilated, uplifting individuals around him with his zest and dedication.
In sum, Liuchao is among the very best early-career researchers I have ever encountered. He
combines the precision of a careful scholar, the creativity of an innovator, the drive of a highly
effective executor, and the charisma of a natural leader. I am fully confident that he will not only
excel as a Junior Research Fellow but also emerge as a leading figure in his field on the international
stage.
I recommend him to you in the strongest possible terms, without the slightest reservation.


Sincerely,
Jingchao Jiang



                                                   5
                                                            JRFS-3 Academic Referee’s Report Form (Aug 2025)
