SUN Deyong （孙德永）
Mobile: (+86) 13261161587 | Email: <EMAIL> | Google Scholar
Address: The Chinese University of Hong Kong, Hong Kong, China
Interests: Computational Mechanics, Composites Manufacturing, Multi-physics Simulations, Multiscale
          Simulation, Isogeometric Analysis

EDUCATION
08/2022-Present       The Chinese University of Hong Kong     PhD in Mechanical and Automation Engineering
                      * GPA: 3.88/4
                      Core modulus: Computational Mechanics (A), Design for Additive Manufacturing (A),
                      Advanced Heat Transfer and Fluid Mechanics (A-), Thesis Research (A)
09/2018-06/2021       Beijing Institute of Technology (BIT)            MSc in Computational Mechanics
                      * Excellent Graduate Student (in Beijing and BIT)
                      * GPA: 3.53/4 (89/100)       Range: 1/44
                      Core modules: Computational Solid Mechanics (96), Mechanics of Composite Materials (90), Design
                      and Applications of Advanced Materials (91), Numerical Analysis (86), Process of Modern Mechanics
                      (88), English Level A for Master Students (92)
09/2014-06/2018       China University of Petroleum (CUP)           BSc in Petroleum Engineering (elite class)
                      * Postgraduate Candidates Exempt from Admission Exam
                      * GPA: 4.10/5 (93/100)     Range: 13/194
                      Core modules: Theoretical Mechanics (99), Material Mechanics (91), Fluid Mechanics (91),
                      Advanced Mathematics (97), Linear algebra (90), College Physics (99)

PUBLICATION
1.   Sun Deyong, Meiyu Liu, Chongrui Tang, et al. A mesoscopic modeling scheme for 3D virtual testing of woven prepregs
     during forming processes. Composite Science and Technology, 271 (2025) 111364.
2.   Sun Deyong, Zou Jianchao, Xiong Yifeng, et al. A coupled 3D hyper-viscoelastic constitutive model for thin woven
     composite prepregs in preforming and consolidation. Composite Part B: Engineering, 301 (2025) 112514.
3.   Sun Deyong, Zhang Wanrui, Zou Jianchao, et al. Characterizing and modeling viscoelastic compaction of thin woven
     fabric prepregs with various yarn angles. Compos Part a-Appl S, 197 (2025) 109030.
4.   Sun Deyong, Zhang Wangrui, Zou Jianchao, et al. Coupled 3D non-orthogonal constitutive model for woven
     composites in preforming and compaction processes. Manufacturing Letters, 2023.
5. Sun Deyong, Dai Rui, Dong Chunying, et al. RI-IGABEM for 2D viscoelastic problems and its application to solid
   propellant grains. Comput. Methods Appl. Mech. Engrg , 378 (2021) 113737.
6. Sun Deyong, Dong Chunying. Shape optimization of heterogeneous materials based on isogeometric boundary
   element method. Comput. Methods Appl. Mech. Engrg, 370 (2020) 113279.
7. Sun Deyong, Dong Chunying. Isogeometric analysis of the new integral formula for elastic energy change of
   heterogeneous materials. J. Comput. Appl. Math, 382 (2021) 113106.
8.   Zou Jianchao, Xiong Yifeng, Tang Chongrui, Zhang Wanrui, Li Rui, Sun Deyong, Zhang Weizhao. Process-
     performance integrated modeling to virtually optimize parameters for preforming of multi-layer woven fabric
     composite prepregs. Compos Part a-Appl S. 197 (2025) 109032.
9.   Tang Chongrui, Sun Deyong, et al, Lay-up defects inspection for automated fiber placement (AFP) with structural
     light scanning and deep learning. Polymer Composites, 2025.
10. Liu Meiyu, Feng Yuncong, Sun Deyong, et al, Modeling-based characterization for thermal expansion and chemical
    shrinkage of highly viscous epoxy resin with thermoplastic additives under the curing condition. Polymer
    Composites, 2024.
11. Dai Rui, Dong Chunying, Xu Chuang, Sun Deyong, IGABEM of 2D and 3D liquid inclusions. Eng. Anal. Bound. Elem, 132
    (2021) 33-49.
12. Sun Deyong, Zhang Weizhao. Integrated mesoscopic modeling scheme for 2D woven prepregs during compression
    molding. The 24th International Conference on Composite Materials (ICCM24).
13. Sun Deyong, Zhang Weizhao, et al. Numerical modeling for forming of woven composites based on 3D strain gradient
    elasticity. The 28th International ESAFORM Conference (ESAFORM 2025).
14. Sun Deyong, Zhang Weizhao. A hyper-viscoelastic continuum model for preforming and consolidation of woven
    carbon fabric reinforced polymers (CFRPs). ASPEN, 2023.
15. Sun Deyong, Dong Chunying, Isogeometric Boundary Element Application based on new elastic energy Increment
    integral formula. Chinese Congress of Theoretical and Applied Mechanics, (2019).
16. Su Guandong, Zhao Lanling, Zhang Peng, Sun Deyong, Gu Xun, Han Beiyu. Numerical Simulation of Electric Field in
    Hydroelectric Simulation Experiment Based on Matlab PDETOOL. Research and Exploration in Laboratory, 36(10):123-
    126,144. DOI:10.3969/j.issn.1006-7167.2017.10.029. (In Chinese)
17. Su Guandong, Gu Xun, Zhao Lanling, Sun Deyong, Han Beiyu, Chen Linhui. Visual Teaching and Computer-based
    Experiment of Fluid Flow in Porous Media with PDETOOL of Matlab. Research and Exploration in Laboratory, 36(8):137-
    142,160. DOI:10.3969/j.issn.1006-7167.2017.08.033. (In Chinese)

TECHNICAL SKILLS
Software                  3D Modelling: Rhino, Altair inspire, Geomagic, Hypermesh.
                          Programming Software: MATLAB, Fortran, Visual Studio, Python, C++.
                          Post-processing: Tecplot.
                          FE Modelling Software: Abaqus, Ansys.
                          Parallel Computing Software: ParaCloud
                          Operating System: Linux and Ubuntu, Windows
                          Others: Photoshop, Solidworks, CAD
Language                  Chinese, English (IELTS - 7.0)

AWARDS
06/2021               Beijing Excellent Graduate Awards, Beijing Municipal Education Commission
05/2021               BIT Excellent Graduate Awards (Rank 1st); BIT Outstanding Master Thesis Award
12/2020               BIT Outstanding Graduate Model
12/2017               CUP Advanced Individual in Science and Technology
06/2017               WUYI Mathematical Modelling League (3rd Prize), JSIAM
11/2015               National Mathematical Competition (3rd Prize); CUP Physical Competition (1st Prize)
09/2015               CUP Mathematical Competition (2rd Prize)

SCHOLARSHIPS
2024                  CUHK PhD IMPAC award
2022                  CUHK Vice-Chancellor's PhD Scholarship
2020                  National Scholarship for Graduates; BIT Academy Scholarship of Best Graduate
2018                  Outstanding Freshman Scholarship (Grade 1)
2017                  CUP 1st Scholarship
2016                  CUP 1st Scholarship
2015                  ENNGroup Scholarship

TEACHING ASSISTANT
2023-2024             Geometry Computing for Design and Manufacturing
2023-2024             Manufacturing Technology
2022-2023             Finite Element Modelling and Analysis
Numerical modeling for 3D strain gradient effect in multiscale analysis for woven
prepreg compression molding process

Abstract
This research aims to numerically predict complex 3D behaviors such as bending, wrinkling
and thickness varying of woven carbon fiber reinforced plastic (CFRP) prepregs during
preforming. To achieve this goal, a unified constitutive law for both mesoscopic yarns and
macroscopic prepregs will be developed based on the strain gradient theory and implemented
into numerical modeling through a bespoke strategy with special high-order elements.
Customized techniques will also be established to correctly characterize the soft and tacky
prepregs and their unidirectional yarns.

CFRPs are lightweight composites with great environmental and economic benefits.
Preforming of woven prepregs is a promising technique to manufacture CFRPs into 3D
geometry. Due to complex deformation of the prepregs during preforming, finite element
analysis (FEA) modeling methods for this process are highly desirable. Previous modeling
works in this area have mostly utilized conventional 2D continuum elements to discretized
prepregs containing separate fibers/yarns and semi-liquid resin. However, it was noticed that
this strategy cannot capture bending, wrinkling and thickness variation of the discontinuous
fabric composites, all of which are critical to geometrical quality of the parts.

The major challenge to model the above deformation modes is the lack of approaches that
consider the important 3D strain-gradient effect of the composites being preformed, which
holds true for both meso- and macro-scales. For instance, transverse cross-section of yarns is
easy to compress but resistive to buckle, as fibers can redistribute within the semi-liquid resin.
Stiffness of woven prepregs is small for uniform shear but large for non-uniform shear, as
yarn bending is harder than woven fabric shear. As a solution, a unified method is proposed
for multiscale preforming modeling, and the novelty lies in (1) introducing the strain gradient
theory to build the 3D deformation-dependent anisotropic constitutive law for both prepregs
and their yarns; (2) developing the high-order solid elements for implementation of the
constitutive law into the FEA solver; (3) customizing techniques to properly test transverse
compression and bending of the soft and tacky prepregs and their yarns; and (4) validating the
part-level preforming modeling for critical 3D deformation features throughout the parts.

Upon completion, the proposed method can replace expensive experiments for analyzing and
optimizing prepreg preforming, alleviating the main technical obstacles including bending,
wrinkles and thickness variations in applying CFRPs to construct lightweight structural
components, reducing greenhouse gas emission, and increasing sustainability of global
ecosystem.
Proposed Objectives:
1. Develop a unified 3D constitutive law based on the strain gradient theory so as to
    correctly utilize the continuum mechanics to predict complex local strain gradient
    behaviors, such as bending, wrinkling and thickness varying, of both the macroscopic
    and discontinuous woven CFRP prepregs made of separate yarns and the mesoscopic and
    nonhomogeneous unidirectional yarns containing disconnected fibers during preforming.

2.   Establish a strategy to implement the constitutive law to be developed in Objective #1
     into the finite element analysis (FEA) solver for both mesoscopic unit-cell modeling and
     macroscopic part modeling under the preforming condition. This FEA modeling strategy
     mainly includes innovative user-defined high-order solid elements that can describe the
     local strain gradient.

3.   Systematically characterize mechanical properties of woven CFRP prepregs and their
     yarns, which can be represented by the unidirectional prepregs, under various preforming
     conditions through (1) innovative experiment-modeling integrated creep tests, based on
     the applicant’s previous work, for thickness- and transverse-compression of the thin but
     soft woven and unidirectional prepregs, respectively, and (2) a customized tester for
     prepreg bending, especially transverse bending of unidirectional prepregs with extremely
     small resistance.

4.   Virtually identify parameters in the constitutive law developed in Objective #1 for woven
     prepregs through mesoscopic unit-cell modeling with (1) the fabric architecture obtained
     from micro-CT scanning based on the applicant’s previous work, (2) the yarn constitutive
     law developed in Objective #1 with properties characterized in Objective #3, (3) the FEA
     modeling strategy established in Objective #2, and (4) the specially designed boundary
     conditions for both in-plane and out-of-plane deformation. Accuracy of the virtual
     characterization will be validated with woven prepreg properties identified in Objective
     #3.

5.   Realize 3D solid macroscale modeling for preforming of woven prepregs, aiming to
     predict complex local strain gradient behaviors, as well as in-plane yarn bending and
     blank draw-in, of the composites. The modeling will be supported by the prepreg
     constitutive law developed in Objective #1 with parameters identified in Objective #3
     and Objective #4, and FEA modeling strategy established in Objective#2. Accuracy of
     the preforming modeling will be experimentally validated in the aspects of yarn
     directions, 3D part geometry and wrinkling morphology based on the applicant’s
     previous work.

Potential Impact of Research Project/Activities
By developing the constitutive law based on the 3D strain gradient theory and implementing it
into the finite element analysis (FEA) solver with assistance of user-defined high-order
elements, the proposed method will, for the first time ever, realize unified and complete
modeling for complex 3D behaviors, such as bending, wrinkling and thickness varying, of the
woven CFRP prepregs during preforming at both meso- and macro-scales, benefiting the
fields of process optimization, mold design, etc. In the short-term, successful establishment of
this multiscale modeling method can provide efficient and detailed analysis of CFRP prepreg
preforming, especially for the wrinkles and thickness variation, which has seldomly been
achieved by existing works. In the medium to long term, this proposed method can facilitate
the generation of reliable data to correlate preforming conditions with part quality, avoiding
human and equipment errors in real process experiments that can be also far more expensive
and time-consuming. The obtained high-quality big data can be employed to train advanced
optimization tools for efficient design of manufacturing processes for CFRP parts with
complex 3D geometries, enabling development of lightweight CFRP structures with excellent
performance and manufacturability and facilitating invention of new multi-functional
composite parts.
(i)-Background of Research
The proposed work aims to develop an innovative 3D multiscale modeling method for
preforming of woven carbon fiber reinforced plastic (CFRP) prepregs to support virtual analysis
and optimization of this complex process, facilitating the efficient production of high-quality
and lightweight 3D CFRP components. Currently, the transportation sector is dominated by
fossil-fuel-driven vehicles, accounting for over 15% of global greenhouse gas (GHG) emission
from human activities [1]. One of the most effective strategies to reduce fuel consumption and
GHG emission of the vehicles is weight reduction [2, 3].
      With their superb performance-to-weight ratios, design flexibility and dimensional
stability [4, 5], CFRP composites are ideal lightweight alternatives to conventional metals for
construction of lightweight vehicles. The global CFRP market is projected to reach over 60
billion USD by 2028, with major applications in automotive, aerospace and marine [6]. Among
various advanced manufacturing technologies for CFRP components, prepreg compression
molding (PCM) shows greatest potential for large-scale production and complex 3D part
geometry, as raw materials can be efficiently formed and solidified in metal molds attached to
press machines [7]. PCM is a multi-step process chain including (1) preforming [8], (2)
consolidation [9-11], (3) curing [12], and (4) minor post-process machining [13]. Preforming is
the key step in PCM to determine part geometry and is the fundamental step that will greatly
affect subsequent behavior of the part [14, 15]. However, design and optimization of the prepreg
preforming face significant challenges as the large number of process parameters, intricate
composition and structure of composite materials, high degree of freedom in part geometry and
prepreg stacking sequence, and non-uniform yarn re-orientation especially for double curvature
features all make experimental optimization costly and time-consuming. To tackle these issues,
various numerical preforming and consolidation modeling methods have been developed by
researchers around the world, including the applicant [9-11].
      The mainstream of these methods are embedded in the fast and reliable finite element
analysis (FEA), but most focus solely on the in-plane deformation of the prepregs and rely on
traditional continuum mechanics theory. Within this framework, material deformation
properties are linked to strains and characterized via corresponding experiments including
uniaxial tension, bias-extension, picture-frame and consolidation tests [16]. The intrinsic
constituents of composites are composed of bundles of fibers and semi-liquid resin, whick give
rise to phenomena that contradict with traditional continuum mechanics theory during
manufacturing, such as resin flow and fiber sliding. Ignoring these phenomena results in the
inaccurate capture of key material features, such as bending, wrinkling and thickness varying,
thereby hindering their application to industrial-scale analysis. Figure 1 demonstrates that the
spurious mesh of macroscale simulation due to the ignorance of in-plane bending stiffness and
Figure 2 shows that the unrealistic wrinkles in macroscale simulation due to the ignorance of
out-plane bending stiffness. Therefore, the strain gradient theory is introduced herein as the
supplement of traditional continuum theory to capture these special phenomena, summarized
as strain gradient effects, while preserving the strain-dependent deformation properties.
      In addition, some of the existing preforming modeling methods, such as those reported in
[17], rely on multiscale analysis to virtually identify coupled mechanical properties of the
prepregs at mesoscale, but these mesoscopic modeling methods ignore the strain gradient
effects in yarns with separate fibers and semi-liquid resin, so deformation of the transversely
isotropic yarns, such as transverse compression and in-plane bending, cannot be precisely
modeled and usually requires extensive material parameter calibration. Therefore, as schemed
in Figure 3(a), longitudinal shear modulus of yarns is often orders of magnitude smaller than
bending modulus, leading to spurious mesh deformation (see Figure 3(b)) similar to that
presented in Figure 1(b) when directly input to FEA without counting the strain gradient effects.
For FEA implementation, these strain-gradient-related issues will lead to artificial buckling [18]
and spurious deformation mode [19] at both meso- and macroscales.
      As a solution, the proposed project will focus on numerical modeling for 3D strain gradient
effects in multiscale analysis for woven prepreg compression molding process.
(ii)-Research Plan
The proposed research will be conducted through the integration method of numerical
modelling, experiment characterization and virtual validation, which has been summarized as
follows:
(ii)-1 Numerical Modelling based on 3D constitutive law for woven prepregs and their
yarns
      Underlying deformation mechanism of both mesoscopic yarns and macroscopic woven
prepregs share the same constituents and deformation mechanisms, which can lead to the same
way to extend their 3D anisotropic constitutive law based on strain gradient theory. Nonetheless,
few researchers have explored this concept. Implementation of strain gradient calculation into
general FEA solvers to predict large deformation of composite prepregs during preforming has
not been realized before, either.
(ii)-2 Experimental characterization for thin prepregs with high softness and tackiness
      The woven prepregs and their yarns, which can be represented with unidirectional prepregs,
are soft and tacky under preforming condition. Reliable clamping of the materials for
experimental characterization with high loading is thus challenging. Meanwhile, the control
and measurement accuracy of temperature, force and displacement in experiments should be
taken carefully. The force-control testing with displacement measurement assisted by digital
image correlation (DIC) demonstrated in Figure 4, has been adopted and designed by the
applicant [11, 19] as an innovative solution to measurement the bending stiffness of woven
prepregs and independent yarns, and the consolidation properties of woven prepregs. Due to
the thin and loosen structure of unidirectional prepregs, the yarns’ properties cannot be
measured directly. Therefore, an iterative inverse method can be implemented to obtain yarns’
properties conveniently.
(ii)-3 Multiscale simulation and virtual validation of composite preforming considering
strain gradient effects
      Based on current multiscale simulation concept, the yarn’s material properties measured
in (ii)-2 and novel constitutive model developed in (ii)-1 can be implemented into mesoscopic
simulations by utilizing displacement periodic boundary conditions (PBCs) in unit-cell
modeling to obtain representative and homogeneous constitutive law for woven prepregs at
macroscale, as shown in Figure 5. The macroscale simulation results are then validated through
techniques, such as geometry scanning, yarn angle measurement and quality check, to eliminate
the spurious wrinkles and unrealistic wrinkles appeared in Figures 1 and 2.

(ii)-4 Gantt chart for management of research activities
 Tasks              4 months     8 months 12 months 16 months 20 months 24 months
 Objective #1
 Objective #2
 Objective #3
 Objective #4
 Objective #5
(iii)-Research Methodology
(iii)-1 Preliminary works completed by the applicant
      The applicant has comprehensive experience in and multiple finished works about woven
prepreg preforming and consolidation modeling. Starting with part-level FEA, the applicant has
developed a coupled 3D hyper-viscoelastic constitutive model for thin woven composite
prepregs in preforming and consolidation, to efficiently capture the coupling effect related to
initial thickness variations caused by yarn angle change [9, 10]. To accurately capture those
coupling effects and facilitate the manufacturing of composite parts with high quality, a creep
experiment assisted by digital image correlation was designed for single-layer prepregs with
sub-millimeter thickness [11]. The applicant has established a integrated mesoscopic modeling
scheme for 3D virtual testing of woven prepregs during forming processes, which can explain
the deformation mechanism of woven prepregs during manufacturing and simulate six
deformation modes simultaneously [20]. To accurately measure the bending stiffness of woven
prepregs, the applicant has implemented the force-controlled bending experiment method to
measure its variation under various temperatures [19], in which the basic strain gradient theory
has been revised by peer researchers. Furthermore, the applicant is doing the multiscale
simulation of woven prepregs for preforming and performance based on the micro-CT
reconstructed RVEs [21].
(iii)-2 Reformulated 3D constitutive law based on strain gradient theory
      This task is for Objective #1. As elaborated in (ii)-1, the 3D constitutive laws can be
reformulated based on strain gradient theory for both macroscopic prepregs and mesoscopic
yarns as the intrinsic mechanics formulation is similar. The constitutive law is therefore to be
first developed for yarns, which have only one reinforcement axis and transverse isotropy
compared to the more complicated woven prepregs.
      The hyperelastic constitutive law of the woven CFRP prepregs and yarns can be derived
and briefly denoted as:
                                     𝑤𝑝 = 𝑤𝑝𝑠 (𝜺) + 𝑤𝑝𝑔 (𝛈)                                  (2)
where 𝑤𝑝𝑠 is integral of 3D hyper-viscoelastic model developed by the applicant in [10] for
woven prepregs and [20] for yarns. 𝜺 represents the strain. 𝑤𝑦𝑔 is the additional term to
manage the effects of strain gradient 𝜼 = 𝛁𝜺 . The formulation and parameters of 𝑤𝑝𝑔 ,
developed by the applicant [19], is to be determined with assistance from bending tests of the
unidirectional prepregs. Details of these tests is to be further elaborated in (iii)-4.
(iii)-3 Implementation of the new 3D constitutive law to general FEA solvers
      This task is for Objective #2. Compared to conventional constitutive law, implementation
of those expressed in Eq. (1) for multiscale preforming modeling demand for information about
strain gradient. For general FEA solvers commonly adopted in industry, computation must be
conducted at the element level, and deformation of the neighboring elements is difficult to
obtain. Therefore, a user-defined high-order 3D solid element is to be developed [19] in order
to utilize element nodal displacement given by the FEA solvers to calculate local strain and
strain gradient, both of which are then to be substituted into the constitutive equations to
determine the nodal force to be returned to the FEA solvers, as plotted in Figure 6. Specifically,
                                                                    𝑒
the element is currently designed as C3D20, and components 𝜂𝑖𝑗𝑘       in strain gradient tensor of
any specific element will be approximated as:
                                     𝑒     𝑒       𝑒
                                   𝜂𝑖𝑗𝑘 = 𝜀𝑗𝑘,𝑖 = 𝑑𝑘,𝑖𝑗 = 𝑵𝑒,𝑖𝑗 𝒅𝑒𝑘                           (2)
                                        𝑒
where 𝑒 indicates element number, 𝜀𝑗𝑘,𝑖 denotes first-order partial derivative components of
               𝑒
the strain, 𝑑𝑘,𝑖𝑗  means second-order partial derivative components of the displacement field
along 𝑘 direction, 𝑵𝑒,𝑖𝑗 is second-order partial derivative components of the high-order shape
function matrix, and 𝒅𝑒𝑘 represents nodal displacement matrix along 𝑘 direction. This element
and the constitutive model mentioned in (iii)-2 can be integrated into FEA commercial software
Abaqus/Explicit with a user-defined subroutine (VUEL). Meanwhile, the computational
efficiency and mesh convergency of the program will be tested on parallel workstation.
(iii)-4 Experimental characterization for the woven prepregs and yarns
      This task is for Objective #3. For future industrial applicability of the proposed work,
commercial-grade CFRP prepregs from Shandong Jingshan Fiber Technology Company with
the same resin and both woven and unidirectional fiber arrangement are to be selected for
material characterization, as well as subsequent multiscale preforming modeling and validation.
The experimental characterization as shown in Figure 4, is to design for mesoscopic yarns,
which will be represented by the unidirectional prepregs, and woven prepregs. For bending
stiffness of the anisotropic samples under various preforming conditions, standard bending tests
along different loading directions are to be conducted on a universal testing machine with an
environmental chamber to control the temperature, a DIC system to record the strain field, and
the clamping technique previously developed by the applicant [19] to avoid slippage. The force-
controlled compression tests designed by the applicant [11] will be performed to measure
transversely isotropic compression, which is the major deformation mode for yarns in sheared
woven prepregs, of the unidirectional prepregs.
(iii)-5 Multiscale modeling for preforming of woven CFRP prepregs
      This task is for Objectives #4 and #5. As demonstrated in Figure 5, the irregular
geometrical features from micro-CT scanning of the real prepregs will first be automatically
extracted via a serial of convolutional neural networks (CNNs) to generate woven yarns’
geometry [21]. The 3D anisotropic constitutive law of the yarns from (iii)-2 with input of yarn
properties from (iii)-4, as well as high-order elements and yarn-to-yarn contact definition from
(iii)-3, are to be assigned to the unit-cell models. Assisted with the mesoscopic simulation
completed by the applicant [20], the homogeneous stress as a function of strain and strain
gradient can be virtually determined in 3D for woven prepregs and implemented into the
macroscopic preforming modeling for full-scale CFRP parts.
      For validation, preforming molds with double curvature features, such as the single-dome
benchmark geometry, and precise tool gap and binder force control will be built based on the
existing preforming experiment platform established by the applicant [10]. Geometrical and
assembly quality of the molds are to be examined and guaranteed by a coordinate measuring
machine. After preforming experiments, yarn directions throughout the entire surface of the
parts will be measured with the automatic system [22]. Furthermore, the 3D part geometry, with
non-uniform thickness included, and wrinkling morphology will be quantified with the 3D laser
scanner. All this information is to be compared with those from macroscopic preforming
modeling to solve the spurious and unrealistic wrinkles as described in Figures 1 and 2.
Figure 1. (a) Non-orthogonal deformation, (b) spurious mesh in FEA modeling and smooth deformation in reality,
                        and (c) in-plane yarn bending of the preformed woven prepregs.




       Figure 2. (a) Macroscale simulation result and unrealistic wrinkles, (b) smooth geometry in reality.




  Figure 3. (a) Schematics of the yarns and their fibers under shear and bending, and (b) spurious yarn mesh in
                              FEA modeling and smooth deformation of real yarns.




               Figure 4. (a) Bending stiffness and (b) consolidation property measurement tests.




Figure 5. Multiscale simulation and virtual validation of composite forming considering striain gradient effects.




        Figure 6. Mechanism of the user-defined high-order element to implement the constitutive law.
Pathway to impact
Carbon fiber reinforced plastics (CFRPs) are advanced composite materials exhibiting various
superior properties compared to traditional engineering materials such as steel and aluminum.
The key advantage of CFRPs lies in their high performance-to-weight ratio, enabling
significant reduction of product weight. This feature yields numerous benefits, including but
not limited to lower energy consumption, lower emissions and enhanced agility. However,
current production of CFRP components remains limited, primarily due to manufacturing
challenges such as long development cycle, heavy capital investment requirements and
intensive labor demands. In recent years, a highly automated prepreg compression molding
(PCM) process comprising preforming, compaction and curing has been developed as a more
efficient and cost-effective alternative for the mass production of woven CFRP parts with
complex three-dimensional geometries. In the manufacturing process of PCM, research on
preforming is still in the nascent stage, lacking comprehensive modeling methods considering
the effects of strain gradient on deformation of the discontinuous prepregs. To address this
issue, this study aims to establish an innovative multiscale 3D modeling method based on the
strain gradient theory to achieve accurate prediction of part geometry and support virtual
design of preforming parameters. This approach will positively impact industry and
environment.
Economy Impact
      One of the potential applications directly influenced by the proposed research is the
manufacturing of lightweight transportation vehicles such as automobiles, aircrafts and
low-altitude flying cars. Nowadays, only a limited number of high-end manufacturers has the
resources to undertake the expensive and time-consuming design and production of CFRP
components, as the procedure relies heavily on physical experiments, highly customized
equipment and skilled workers. As a result, currently, manufacturing may contribute to as
much as 80% cost of the CFRP products. The proposed modeling approach aims to enable
more manufacturers to commercialize CFRP parts with complex designs and high quality. In
the short term, these manufacturers can be involved in utilizing and enhancing the modeling
tools for virtual design, processing and evaluation of CFRP parts. For instance, the similar
numerical modeling methods for CFRP preforming and curing previously completed by the
have already been adopted by Ford Motor Company and Commercial Aircraft Corporation of
China for design and production of CFRP components to achieve vehicle weight reduction. To
maximize the possibility of achieving these benefits and reaching the beneficiaries, the
applicant will: (1) invite relevant companies and institutions to participate in technical
seminars and workshops to disseminate project outcomes; and (2) provide trial software to
support tackling of practical production challenges for future development in industrial
laboratories.
     In the mid-term, the applicant expects to link the proposed method with previously
developed techniques to create a comprehensive CFRP PCM modeling package. This package
will assist manufacturers in the production of lightweight and geometrically complex CFRP
components. Moreover, this package can foster the design-thinking manufacturing strategy.
Traditionally, engineers work with existing CFRP materials to produce parts with locally
optimal parameters determined by physical experimentation. In contrast, design-thinking
manufacturing strategy utilizes numerical modeling to ascertain globally optimal processing
conditions prior to the actual production, which can facilitate the attainment of parts with the
highest quality and geometric accuracy across the entire design space without trial-and-error
processes. To enhance the possibility of achieving these benefits and reaching the
beneficiaries, the applicant will: (1) promote the method to software companies through
technical seminars; (2) collaborate with industrial partners to transfer the modeling method
from research stage to production phase by establishing sharing platforms or
exchange-training programs; and (3) cooperate with engineering design experts to finalize a
turnkey design-thinking manufacturing package.
     In the long term, the architecture of this proposed method for preforming of CFRP
prepregs can be extended to derive other manufacturing techniques applicable to various raw
materials, facilitating the creation of digital twins to serve as tools for process optimization
and rapid and cost-effective design of composite materials. For example, similar modeling
approaches could be developed to simulate the automated fiber placement of unidirectional
prepregs, enabling the investigation of optimal processing parameters, such as the placement
pressure and temperature, for highest production quality. To enhance the possibility of
achieving these benefits and reaching the beneficiaries, the applicant will: (1) participate in
academic conferences within the general manufacturing field to explore interdisciplinary
collaborations; and (2) potentially start a company to facilitate commercialization of the
technology.
Environment Impact
     In the short term, the application of virtual models to replace the extensive trial-and-error
physical experiments traditionally involved in the CFRP preforming design process will
significantly reduce both human and material resource waste, as well as the consumption of
raw materials. To enhance the possibility of this benefit, the applicant will meticulously
design the modeling program to minimize the need for characterization and validation
experiments.
     In the mid-term, this work can assist in alleviating global warming by promoting mass
production of CFRP parts. Effective utilization of lightweight CFRPs can reduce the weight of
automobiles and aircrafts by up to 50% and 30%, respectively, significantly decreasing fuel
consumption and emissions in the transportation sector that currently occupies 24% of
manmade greenhouse gas emissions. To enhance the possibility of this benefit, the applicant
will collaborate with material and environment scientists to track and mitigate the carbon
footprint of CFRP parts through their lifecycle.
     In the long term, as Earth's surface resources become increasingly limited, human
activities are anticipated to extend into the sky and even outer space. This foreseeable
aerospace and low-altitude economy era will necessitate the development of lightweight,
long-range, high-payload and economically constructed aircrafts, spacecrafts and flying cars.
The proposed research can effectively contribute to the construction of such vehicles using
advanced CFRP composites. To enhance the possibility of this benefit, the applicant will
collaborate with industrial partners to further develop the proposed multiscale 3D modeling
method to enable broader applications beyond specific prepreg PCM processes.
[1] Global Greenhouse Gas Overview. (2024, September 10, 2024). Retrieved from
https://www.epa.gov/ghgemissions/global-greenhouse-gas-overview.
[2] P. Blechinger. (2022, 26 January 2022). How to reduce the weight of an airplane and CO2
emissions           at         the         same          time?          Retrieved        from
https://industryinsider.eu/aerospace-industry/reduction-of-the-weight-of-the-aircraft/.
[3] Joost WJ. Reducing Vehicle Weight and Improving U.S. Energy Efficiency Using
Integrated Computational Materials Engineering. JOM. 2012;64(9):1032-8.
[4] Che D, Saxena I, Han P, Guo P, Ehmann KF. Machining of Carbon Fiber Reinforced
Plastics/Polymers: A Literature Review. Journal of Manufacturing Science and Engineering.
2014;136(3).
[5] Wang M, Kang Q, Pan N. Thermal conductivity enhancement of carbon fiber composites.
Applied Thermal Engineering. 2009;29(2):418-21.
[6] CF & CFRP market by source (virgin, recycled), precursor resin type, manufacturing
process (lay-up, compression molding, resin transfer molding, filament winding, pultrusion,
injection molding), end-use industry. and region – global forecasts to 2028. (CH2794). (2024).
Retrieved                               from                              MarketsandMarkets:
https://www.marketsandmarkets.com/Market-Reports/carbon-fiber-composites-market-416.ht
ml.
[7] Lee J-M, Kim B-M, Ko D-C. Development of vacuum-assisted prepreg compression
molding for production of automotive roof panels. Composite Structures. 2019;213:144-52.
[8] Zhang W, Ren H, Liang B, Zeng D, Su X, Dahl J, et al. A non-orthogonal material model
of woven composites in the preforming process. CIRP Annals. 2017;66(1):257-60.
[9] Sun D, Zhang W, Zou J, Xiong Y, Tang C, Zhang W. Coupled 3D non-orthogonal
constitutive model for woven composites in preforming and compaction processes.
Manufacturing Letters. 2024;41:412-20.
[10] Sun D, Zou J, Xiong Y, Zhang W, Tang C, Zhang W. A coupled 3D hyper-viscoelastic
constitutive model for thin woven composite prepregs in preforming and consolidation.
Composites Part B: Engineering. 2025;301.
[11] Sun D, Zhang W, Zou J, Xiong Y, Tang C, Belnoue JPH, et al. Characterizing and
modeling compaction behavior of thin woven fabric prepregs with various yarn angles.
Composites Part A: Applied Science and Manufacturing. 2025;197.
[12] Feng Y, Han Z, Liu M, Zhang W. Multiscale modeling for viscoelasticity of woven CFRP
considering preforming and curing effects via finite element and long-short term memory
analysis. Composites Part A: Applied Science and Manufacturing. 2024;186:108397.
[13] Zou J, Xiong Y, Tang C, Li R, Liang B, Zhang W. Blank geometry design for preforming
of woven composites based on numerical modeling. Journal of Advanced Manufacturing
Science and Technology. 2024;4(1):2023020-.
[14] Liang B, Zhang WZ, Fenner JS, Gao JY, Shi Y, Zeng D, et al. Multi-scale modeling of
mechanical behavior of cured woven textile composites accounting for the influence of yarn
angle variation. Compos Part a-Appl S. 2019;124.
[15] Feng Y, Wang Z, Liu M, Han Z, Liang B, Zhang W. Numerical prediction for
viscoelasticity of woven carbon fiber reinforced polymers (CFRPs) during curing accounting
for variation of yarn angle caused by preforming. Composites Part A: Applied Science and
Manufacturing. 2023:107631.
[16] Mathieu S, Hamila N, Bouillon F, Boisse P. Enhanced modeling of 3D composite
preform deformations taking into account local fiber bending stiffness. Composites Science
and Technology. 2015;117:322-33.
[17] Liang B, Boisse P. A review of numerical analyses and experimental characterization
methods for forming of textile reinforcements. Chinese J Aeronaut. 2021;34(8):143-63.
[18] Zhang W, Bostanabad R, Liang B, Su X, Zeng D, Bessa MA, et al. A numerical
Bayesian-calibrated characterization method for multiscale prepreg preforming simulations
with tension-shear coupling. Composites Science and Technology. 2019;170:15-24.
[19] Sun D, Li S, Tang S, Zhang W. Numerical modeling for forming of woven composites
based on 3D strain gradient elasticity. Materials Research Proceedings. 2025;54:9.
[20] Sun D, Liu M, Tang C, Feng Y, Zheng Q, Zhang W. A mesoscopic modeling scheme for
3D virtual testing of woven prepregs during forming processes (Accpeted). Composite
Sciencen and Technology. 2025.
[21] Tang C, Zou J, Xiong Y, Liang B, Zhang W. Automatic reconstruction of closely packed
fabric composite RVEs using yarn-level micro-CT images processed by convolutional neural
networks (CNNs) and based on physical characteristics. Composites Science and Technology.
2024;252.
[22] Tang C, Liang B, Zhang W. Detect and visualize non-uniform yarn orientations on
preformed CFRP parts using automatic scanning and image processing. Journal of
Manufacturing Processes. 2023;102:1043-58.
Summary of previous submissions
General Research Fund: No. 14211025
Title of Project: Development of a Multiscale 3D Modeling Method for Preforming of Woven
CFRP Prepregs based on the Strain Gradient Theory
Research purpose: This proposal aims to numerically predict complex 3D behaviors such as
bending, wrinkling and thickness varying of woven carbon fiber reinforced plastic (CFRP)
prepregs during preforming. To achieve this goal, a unified constitutive law for both mesoscopic
yarns and macroscopic prepregs will be developed based on the strain gradient theory and
implemented into numerical modeling through a bespoke strategy with special high-order
elements. Customized techniques will also be established to correctly characterize the soft and
tacky prepregs and their unidirectional yarns.
Revisions
(1) Research purpose: The current research will focus on numerical modeling for 3D strain-
    gradient effects in multiscale analysis for woven prepreg compression molding process,
    other effects mentioned in the previous proposal, such as friction and contact measurement,
    will not be studied to ensure that the plan is reasonable and focused.
(2) The specified forms of the constitutive models to be utilized in the current research have
    been developed and published by the applicant: 3D hyper-viscoelastic constitutive model
    for woven prepregs [10, 11], transversely isotropic hyperelastic model for yarns [20] and
    strain-gradient related bending stiffness [19]. These models were not completed and
    included in the previous proposal.
(3) The viscous effects can be captured by the applicant’s previous work documented in [10,
    11] to explain resin flow process during manufacturing. This aspect was not considered in
    the previous proposal.
(4) The experimental method to characterize bending deformation, the transformation for
    bending deformation to bending moment, and the relationship between bending moment
    and strain gradient are detailed in mathematical terms in the applicant’s previous work [19].
    This aspect was not considered in the previous proposal.
(5) Virtual validation standard is defined as the ability of the proposed strain-gradient based
    modeling method to eliminate (i) the in-plane spurious wrinkles in Figures 1 and 3 at
    macroscale and mesoscopic simulations, (ii) the out-of-plane unrealistic wrinkles at
    macroscale simulations, and (iii) the thickness variation process in manufacturing [10, 11].
    This standard was not included in the previous proposal.
(6) Compared to the previous proposal, the finite element modeling tool is specified in the
    current research, which is the finite element commercial software Abaqus/Explicit with the
    user-defined subroutine VUEL (for high-order elements).
(7) In this work, the computational efficiency and mesh convergence of the proposed algorithm
    will be tested on the high-performance parallel-computing workstation. This aspect was not
    considered in the previous proposal.
Related experience or participation in research projects
                 Reverse Design Scheme for Molds in Woven Carbon Fiber Composite
01/2025-Present Prepreg Compression Molding
                 RGC: GRF14205923
                 Combined with the multiscale simulation results of composite molding, molding
                 process standards were formulated; based on a computational geometry-based
                 scheme, the reverse design of molds in compression molding was realized. This
                 reduces the mold design cycle and test losses, and improves production efficiency.

                Multiscale Simulation and Homogenization Analysis of Woven Carbon
08/2024-01/2025 Fiber Composite Molding Process
                RGC: GRF14205923
                High-precision mesoscale modeling of composites was conducted using in-situ CT
                scanning and image recognition technology. A transversely isotropic
                hyperviscoelastic constitutive model was constructed, and through the secondary
                development of ABAQUS subroutines combined with three-dimensional periodic
                boundary conditions, the goals of explaining the molding mechanism and
                simplifying the experimental process were achieved.

                Virtual Characterization of Preforming and Compaction Coupling
08/2023-08/2024 Phenomenon in Woven Carbon Fiber Composite Prepreg Molding
                RGC: GRF14205923
                A nonlinear and non-orthogonal hyperviscoelastic constitutive model was
                constructed to describe all deformation modes and coupling characteristics during
                the molding process of woven prepregs. Through secondary development of
                ABAQUS subroutines, the model was integrated to achieve high-precision
                predictions of the geometric shape, fiber braiding angle, and local thickness
                distribution of carbon fiber composite parts, thereby enabling accurate prediction
                and control of molding forces.

                Mechanism and Performance Characterization of Woven Carbon Fiber
08/2022-08/2023 Composite Prepreg Molding
                RGC: GRF14205923
                A non-contact, indirect, and high-precision characterization method was established
                to target the macroscopic deformation characteristics of aerospace-grade woven
                carbon fiber composites during the molding process. Corresponding nonlinear and
                non-orthogonal hyperviscoelastic constitutive equations were constructed, and
                verified through the secondary development of ABAQUS user subroutines
                (UMAT/VUMAT). This enables the high-precision prediction of the macroscopic
                molding process of woven carbon fiber composites.

                Isogeometric Boundary Element Method for Viscoelastic Effects of Solid
06/2020-2021/09 Propellant
                 Simulating the viscoelastic effects of solid propellant by isogeometric boundary
                   element
                 Employing Laplace transform and correspondence principle to obtain analytical
                   solutions
                 Extending this framework to simulate the fracture and debonding behaviors of
                   viscoelastic-plastic materials in solid propellant

                Studies on Rapid and Direct Algorithm of Isogeometric Boundary Element
03/2019-06/2020 for Large Scale Multilayer Complex Coating Structures
                National Natural Science Foundation of China          Project No.11972085
                      Obtained 2D and 3D complexed CAD models from Rhino and reconstructed
                       them by MATLAB and Fortran according to basic parameters
                      Applied the shape optimization analysis based on the gradient-based optimization
                       method (MMA) to elastic problems
                      Applied the parallel computing method with Fortran and OPENMP to improve
                       the computing efficiency
                      Implemented Rapid and Direct Algorithm to optimize large scale coating
                       structures

                Isogeometric Boundary Element Method for Turbine Blade Failure in
06/2018-03/2019 Aeroengines
                National Natural Science Foundation of China                   Project No.11672038
                 Rebuilt 2D NACA airfoil geometries by NURBS using MATLAB
                 Implemented Galerkin boundary element method to calculate the potential
                   problem
                 Used Lagrange multiplier to deal with Kutta condition in coefficient matrix
                 Simulated the fluid-structure coupled behavior of turbine blades
                 Compared numerical results with that from traditional panel method and Xfoil
Leadship experience
                  Development of Heavy Oil in the Post-steam Era
09/2017-09/2018
                  National Student Research and Innovation Program
                  Project Leader (Funding: ￥20,000)            Supervisor: Prof. Xiaohu Dong
                     Carried out visual physical simulation experiment of steam injection in
                      heavy oil reservoirs and analysed the spatial distribution of remaining oil
                     Adopted reservoir numerical simulation method to study the distribution of
                      remaining oil in different types of heavy oil reservoirs
                  Tight Oil Development Technology and Economic Feasibility Evaluation
09/2016-09/2017
                  Beijing Student Research and Innovation Program
                  Project Leader (Funding: ￥10,000)                Supervisor: Prof. Shenglai Yang
                      Predicted the total production and its decline law of tight oil
                      Investigated and built economic evaluation models
                     Evaluated the production limits under economic feasibility development
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) SUN
                        (Other name) Deyong
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

As the nominee's collaborator and potential supervisor before, I have known him for nearly
one and a half years. We first met when he applied for the visiting scholar program at our
university, where I participated in reviewing his materials and communicating with him in my
capacity as his potential supervisor, and witnessed his successful nomination. Since then, we
have worked together as collaborators on "experimental characterization and multiscale
simulation for woven composite forming", and our joint research focusing on compaction
behavior of thin woven fabric prepregs with different yarn angles has been published in
Composite Part A. The new model in the paper breaks existing limitations and has improved
ability to capture part thickness variation in composite manufacturing.

This cooperation was pleasant and fruitful. We have reached the preliminary consensus on
future cooperation and look forward to continuing our collaboration in the field of composite
manufacturing.



                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent   Very Good        Good             Average          No basis
                           (top 5%)    (top 6-20%) (top 21-50%)         or below            for
                                                                       (lower than       judgment
                                                                          50%)
Intellectual capacity                      ☐              ☐                 ☐               ☐
                                            ☐              ☐                 ☐               ☐
Ability for conducting
scholastic research
                              
                                            ☐              ☐                 ☐               ☐
Knowledge of proposed
research study
                              

                                            ☐              ☐                 ☐               ☐
Analytical power and
reasoning
                              
Judgment                                   ☐              ☐                 ☐               ☐
                                            ☐              ☐                 ☐               ☐
Imagination and
originality
                              

                                            ☐              ☐                 ☐               ☐
Motivation and
perseverance
                              

                                            ☐              ☐                 ☐               ☐
Skills of writing and
argumentation
                              

                                            ☐              ☐                 ☐               ☐
Capacity for independent
work
                              

                                            ☐              ☐                 ☐               ☐
Reliability and sense of
responsibility
                              
Leadership experience                      ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
During the past one and a half years of collaboration and frequent in-depth communication, I
have been deeply impressed by the nominee's professional competence and comprehensive
abilities, with his research capability and development potential being particularly prominent,
as evidenced by the following aspects:

First, he possesses solid and comprehensive knowledge foundation in the field of solid
mechanics. He can quickly and accurately translate theoretical knowledge into practical
solutions for problems encountered in experiments and simulations, laying solid foundation for
the efficient advancement of research work. Second, innovative thinking is one of his core
strengths: at the critical stage of model construction, he broke through the limitations of
traditional research ideas and proposed a number of innovative solutions, which directly
became the core driving force for breaking through research bottlenecks and advancing the
project smoothly. Third, he demonstrates the strong comprehensive ability in theoretical
                                                3
                                                       JRFS-3 Academic Referee’s Report Form (Aug 2025)
derivation and programming implementation, and he can not only independently complete the
derivation of complex mechanical formulas but also skillfully use professional programming
tools to conduct numerical simulation and verification of models, effectively ensuring the
validity and reliability of research results.

In addition, the nominee also has excellent communication, coordination and teamwork skills.
During the collaboration process, he has not only expressed his academic views clearly and
systematically but also listened patiently to the opinions of team members, always maintaining
open attitude of active cooperation and willingness to share, which has created positive
research atmosphere for the team. More importantly, he showed a keen insight into academic
frontiers and is always eager to enhance his study. The in-depth and systematic research
experience he has accumulated in the field of composite material compression molding can be
smoothly extended to molding and manufacturing technology fields related to composite
materials, such as automatic fiber placement, continuous fiber 3D printing and structural
battery packaging. This extension will enable precise control and optimization of the
manufacturing process, ultimately achieving the goals of reducing product defects and
improving yield rates.

Overall, the nominee has demonstrated significant potential to become an outstanding
researcher. Winning the JRFS Award will provide strong support for the further development
of his academic career.




                                               4
                                                       JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: JONATHAN P.-H. BELNOUE                          Title: Dr
 University / Organisation: University of Bristol
 Position: Senior Lecturer
 E-mail Address: <EMAIL>
 Telephone Number#: +44-************
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date: 28/08/2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      5
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                RGC Reference Number :               To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/
     fax (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover
     page) to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements
      and research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                       1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) SUN
                        (Other name) Deyong
Host Faculty / School / Department / Department of Mechanical and Automation Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the
    Selection Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known the nominee for a year now, and throughout this period, we have worked as
collaborators, conducting in-depth discussions and joint research on simulation and design
methods in the field of composite material compression molding.​

In our core collaborative project, we jointly explored and developed a "reverse design method
for composite compression molds". By reversely deriving key parameters in the molding
process, such as mold cavity structure, pressure distribution threshold, etc., this method
effectively solves the problems in precise control for manufacturing quality and 3D
geometrical accuracy of the composite parts in traditional forward design. Ultimately, it
realizes the accurate regulation of part thickness and effective assurance of product quality,
providing a practical technical path for promoting the composite compression molding
technology towards high-volume and low-cost production.​




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                           Excellent   Very Good        Good            Average          No basis
                           (top 5%)    (top 6-20%) (top 21-50%)        or below            for
                                                                      (lower than       judgment
                                                                         50%)
Intellectual capacity                     ☐              ☐                 ☐               ☐
                                           ☐              ☐                 ☐               ☐
Ability for conducting
scholastic research
                              
                                           ☐              ☐                 ☐               ☐
Knowledge of proposed
research study
                              
                                           ☐              ☐                 ☐               ☐
Analytical power and
reasoning
                              
Judgment                                  ☐              ☐                 ☐               ☐
                                           ☐              ☐                 ☐               ☐
Imagination and
originality
                              
                                           ☐              ☐                 ☐               ☐
Motivation and
perseverance
                              
                                           ☐              ☐                 ☐               ☐
Skills of writing and
argumentation
                              
                                           ☐              ☐                 ☐               ☐
Capacity for independent
work
                              
                                           ☐              ☐                 ☐               ☐
Reliability and sense of
responsibility
                              
Leadership experience                     ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
During the cooperation with the nominee, I was deeply impressed by his comprehensive
capability, particularly his outstanding performance in solving complex problems. He not only
possesses efficient collaborative communication skills, enabling them to quickly align goals
with the team and advance tasks in the coordinated manner, but also demonstrates innovative
thinking, excelling at breaking through traditional frameworks to propose new ideas.
Meanwhile, he can always link the research to real industrialization prospects of the
technology, providing crucial support to determine the direction of the cooperative project.

Among his contributions, the "Out-of-plane Performance Prediction Method for Composite
Material Molding" completed by the nominee is a concentrated embodiment of his capability,
and I highly recognize its innovation and practicality. This method breaks the limitations of
traditional prediction approaches for composite compression molding, as it can accurately
capture various constitutive laws for mechanical properties of the composite material in the
                                               3
                                                      JRFS-3 Academic Referee’s Report Form (Aug 2025)
out-of-plane direction during the molding process. It provides a quantitative basis for the
subsequent optimization of process parameters and evaluation of product quality, effectively
avoiding waste and inefficiency in the previous experiment-based trial-and-error design
methods. Currently, our team is actively promoting the integration of this method into the
research and development of "Thermal Diaphragm Forming Technology". We expect that
through this integrated application, control accuracy of the thermal diaphragm forming process
over composite material properties will be further improved, effectively promoting this
technology in manufacturing of high-end composite components and expanding its application
scenarios in different industrial fields, such as aerospace and high-end equipment.

These practices fully prove that the nominee not only has keen insight into condensing core
scientific issues and solid capability to solve complex technical problems, but also possesses
long-term vision to transform scientific research achievements into industrial applications.
These are the exact core qualities required for an excellent researcher. If the nominee
successfully obtains support from JRFS, it will undoubtedly provide a broader platform for his
academic research, deepening his exploration in the field of composite material forming
technology and further promote development of his academic career.




                                               4
                                                       JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                        Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: YONG LI                                         Title: Prof
     University / Organisation: Beihang University
     Position: Professor
     E-mail Address: <EMAIL>
     Telephone Number#: +86-18811787363
     (# Please provide country code and area code, e.g. +86-10-1234567)
     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
     I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date:        2025.8.28




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          5
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
