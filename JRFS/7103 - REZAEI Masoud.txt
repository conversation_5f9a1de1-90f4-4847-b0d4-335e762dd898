Curriculum Vitae



  PERSONAL INFORMATION

 Name and Family Name: <PERSON><PERSON><PERSON>

 Current affiliation:   Post-doctoral Fellow,
                        Department of Mechanical and Automation Engineering,
                        The Chinese University of Hong Kong, Hong Kong,
                        30/06/2024- Present
 Email Address:         mreza<PERSON><EMAIL>
                        <EMAIL>
 ORCID:                 0000-0003-3141-2510
 ResearchGate:          https://www.researchgate.net/profile/<PERSON><PERSON><PERSON>-<PERSON>-12
 Phone:                 +85297025302



       Research Metrics            Scholarships and Awards                Academic service
  Journal papers        18          Scholarships        3         Peer reviews           15
  Conference papers      7         National awards      1         Invited talks           1
  Citations             697       University awards     2
  h-index               14


 PERSONAL STATMENT
I am a Postdoctoral Fellow in the Department of Mechanical and Automation Engineering at The
Chinese University of Hong Kong (CUHK). I am seeking fellowship opportunities to leverage my
current skills, acquire new interdisciplinary expertise, and conduct deep, innovative research.
Previously, I was a Postdoctoral Research Associate in the same department. My research career has
focused on structural mechanics, nonlinear vibrations, smart materials, energy harvesting, quasi-steady
fluid-solid interactions, and experimental modal analysis. I am skilled in both analytical and
experimental mechanics for fundamental research and problem-solving. I was awarded a fully funded
Ph.D. in 2017 for studying at the Iran University of Science and Technology (IUST) by the Iranian
Ministry of Science, Research, and Technology (MSRT) and received a sabbatical study scholarship
from MSRT in 2021. I have also served as a Research Assistant at the Sound and Vibration Laboratory
of Tarbiat Modares University (2014–2018) and the Noise and Vibration Control Research Laboratory
of IUST (2017–2023). In 2021, I was granted an Honorary Research Assistant fellowship by CUHK.
My research has resulted in 18 journal papers and 7 conference papers. I have been recognized with
several awards, including first place in Mechanical Engineering by MSRT in a national competition, as
well as top researcher and top student awards in university-level competitions at IUST. I ranked 1st
among Ph.D. students in Mechanical Engineering–Applied Design at IUST for four consecutive years,
and my Ph.D. thesis was awarded the best Ph.D. thesis award by the Iranian Society of Acoustics and
Vibration (ISAV). I have also had the opportunity to conduct joint research with academics from
Swansea University, CUHK, Huazhong University of Science and Technology, and the University of
Waterloo.




                                                  1
EDUCATION


23/09/2017-   Ph.D. Candidate of Mechanical Engineering- Dynamics, Vibrations, and Control
14/02/2023       School of Mechanical Engineering, Iran University of Science and Technology,
                 Tehran, Iran
                 Thesis Title: Simultaneous Energy Harvesting and Vibration Suppression
                 Utilizing Multi-stable Magneto-Piezoelastic Vibration Absorber
                 Supervisors: Prof. Roohollah Talebitooti and Prof. Shahrokh Hosseini Hashemi
                 Average: 18.53/20 (Ranked 1st)
                 Thesis Defense Grade: Excellent

23/09/2014-   M.Sc. Student in Mechanical Engineering- Applied Design
08/01/2017           School of Mechanical Engineering, Tarbiat Modares University, Tehran,
                     Iran
                     Thesis Title: Energy Harvesting from the Nonlinear Vibrations of a Clamped
                     Beam with a Piezoelectric Layer Supported by a Nonlinear Spring Due to
                     Transverse Galloping Excitation
                     Supervisor: Prof. Siamak Esmaeilzadeh Khadem
                     Average: 17.22/20


23/09/2009-   B.Sc. Student in Mechanical Engineering- Solid Mechanics
22/09/2013       Azad University- Central Tehran branch, Tehran, Iran
                 Thesis Title: Stress Analysis of a Leg Press Machine Using ABAQUS Software
                 Supervisor: Dr. Hossein Torabian
                 Average: 16.05/20




ACADEMIC EXPERIENCES


30/06/2023-   Postdoctoral Research Associate
29/06/2024       Department of Mechanical and Automation Engineering, The Chinese
                 University of Hong Kong, Hong Kong
                 Research Title: Tuning the Natural Frequency of Mechanical Oscillators Using
                 Their Inherent Energy
                 Supervisor: Prof. Wei-Hsin Liao

31/05/2021-   Visiting Ph.D. Student (title: Honorary Research Assistant)
30/05/2022        Department of Mechanical and Automation Engineering, The Chinese
                  University of Hong Kong, Hong Kong
                  Research Title: Simultaneous Energy Harvesting and Vibration Suppression
                  Utilizing Dynamic Vibration Absorbers
                  Supervisor: Prof. Wei-Hsin Liao




                                             2
 PUBLICATIONS

Journal Papers
1. Masoud Rezaei, Wei-Hsin Liao, Michael I Friswell, Towards self-powered tunable mechanical
oscillators: A conceptual study, Journal of Sound and Vibration, 599 (2025) 118918.
2. Masoud Rezaei, Roohollah Talebitooti, Wei-Hsin Liao, Michael I Friswell, A comparative study on
vibration suppression and energy harvesting via mono-, bi-, and tri-stable piezoelectric nonlinear
energy sinks, Nonlinear Dynamics, 112 (2024) 10871–10910.
3. Juntong Xing, Masoud Rezaei, Huliang Dai, Wei-Hsin Liao, Suppressing galloping-induced
vibrations by integrating bluff body with surface protrusions, AIP Advances, 14 (2024) 025236.
4. Juntong Xing, Masoud Rezaei, Huliang Dai, Wei-Hsin Liao, Investigating the coupled effect of
different aspect ratios and leeward protrusion lengths on vortex-induced vibration (VIV)-galloping
energy harvesting: Modelling and experimental validation, Journal of Sound and Vibration, 568 (2024)
118054.
5. Juntong Xing, Masoud Rezaei, Huliang Dai, Wei-Hsin Liao, Investigating the effect of surface
protrusions on galloping energy harvesting, Applied Physics Letters, 122 (2023) 153902.
6. Masoud Rezaei, Roohollah Talebitooti, Wei-Hsin Liao, Michael I Friswell, Integrating PZT layer
with tuned mass damper for simultaneous vibration suppression and energy harvesting considering
exciter dynamics: An analytical and experimental study, Journal of Sound and Vibration, 546 (2023)
117413.
7. Masoud Rezaei, Roohollah Talebitooti, Investigating the performance of tri-stable magneto-
piezoelastic absorber in simultaneous energy harvesting and vibration isolation, Applied Mathematical
Modelling, 102 (2022) 661-693.
8. Masoud Rezaei, Roohollah Talebitooti, Wei-Hsin Liao, Investigations on magnetic bistable PZT-
based absorber for concurrent energy harvesting and vibration mitigation: Numerical and analytical
approaches, Energy, 239 (2022) 122376.
9. Masoud Rezaei, Roohollah Talebitooti, Wei-Hsin Liao, Concurrent energy harvesting and vibration
suppression utilizing PZT-based dynamic vibration absorber, Archive of Applied Mechanics, 92 (2022)
363-382.
10. Masoud Rezaei, Roohollah Talebitooti, Wei-Hsin Liao, Exploiting bi-stable magneto-piezoelastic
absorber for simultaneous energy harvesting and vibration mitigation, International Journal of
Mechanical Sciences, 207 (2021) 106618.
11. Sasan Rahmanian, Shahrokh Hosseini Hashemi, Masoud Rezaei, Out-of-plane motion detection
in encapsulated electrostatic MEMS gyroscopes: Principal parametric resonance, International Journal
of Mechanical Sciences, 190 (2021) 106022.
12. Masoud Rezaei, Roohollah Talebitooti, Effects of higher-order terms in aerodynamic force on the
nonlinear response of a galloping PZT energy harvester, Journal of Theoretical and Applied Vibration
and Acoustics, 6 (2020) 271-280.
13. Ali Ebrahimi-Mamaghani, Hoda Sarparast, Masoud Rezaei, On the vibrations of axially graded
Rayleigh beams under a moving load, Applied Mathematical Modelling, 84 (2020) 554-570.
14. Masoud Rezaei, Siamak Esmaeilzadeh Khadem, Michael I Friswell, Energy harvesting from the
secondary resonances of a nonlinear piezoelectric beam under hard harmonic excitation, Meccanica,
55 (2020) 1463-1479.
15. Masoud Rezaei, Roohollah Talebitooti, Wideband PZT energy harvesting from the wake of a bluff
body in varying flow speeds, International Journal of Mechanical Sciences, 163 (2019) 105135.
16. Masoud Rezaei, Roohollah Talebitooti, Michael I Friswell, Efficient acoustic energy harvesting by
deploying magnetic restoring force, Smart Materials and Structures, 28 (2019) 105037.
17. Masoud Rezaei, Roohollah Talebitooti, Sasan Rahmanian, Efficient energy harvesting from
nonlinear vibrations of PZT beam under simultaneous resonances, Energy, 182 (2019) 369-380.


                                                 3
18. Masoud Rezaei, Siamak Esmaeilzadeh Khadem, Peyman Firoozy, Broadband and tunable PZT
energy harvesting utilizing local nonlinearity and tip mass effects, International Journal of Engineering
Science, 118 (2017) 1-15.


Conference Papers

1. Masoud Rezaei, Wei-Hsin Liao, Michael I Friswell, Self-powered frequency tuning via energy
harvesting, Active and Passive Smart Structures and Integrated Systems XIX, SPIE Smart Structures +
Nondestructive Evaluation 2025 Conference, Vancouver, Canada 2025.
2. Juntong Xing, Masoud Rezaei, Wei-Hsin Liao, Investigating the effects of auxetic structures on
galloping energy harvesting performance, Active and Passive Smart Structures and Integrated Systems
XIX, SPIE Smart Structures + Nondestructive Evaluation 2025 Conference, Vancouver, Canada 2025.
3. Juntong Xing, Masoud Rezaei, Wei-Hsin Liao, Comparative studies on galloping energy harvesters
with optimally designed leeward surface protrusions, Active and Passive Smart Structures and
Integrated Systems XVIII, SPIE Smart Structures + Nondestructive Evaluation 2024 Conference,
California, United States, 2024, pp. 89-98.
4. Juntong Xing, Masoud Rezaei, Huliang Dai, Wei-Hsin Liao, Suppression of galloping oscillations
using perforated bluff bodies, The International Conference on Applied Nonlinear Dynamics, Vibration
and Control, Hong Kong, 2023, pp. 514-528.
5. Masoud Rezaei, Roohollah Talebitooti, Effects of higher order terms in aerodynamic force on the
nonlinear response of a galloping PZT energy harvester, The 10th International Conference on Acoustics
and Vibration, Tehran University, Tehran, Iran, 2021.
6. Masoud Rezaei, Roohollah Talebitooti, Investigating the Effects of Aerodynamic Force Model on
the Response of a Galloping PZT Energy Harvester, Xmech 2020, Iran University of Science and
Technology, Tehran, Iran, 2020.
7. Masoud Rezaei, Roohollah Talebitooti, Michael I Friswell, Applying Nonlinear Restoring Force to
Improve the Performance of Acoustic PZT Energy Harvesters, The 9th International Conference on
Acoustics and Vibration, Iran University of Science and Technology, Tehran, Iran, 2019.


RESEARCH INTERESTS


•   Nonlinear Dynamics and Vibrations
•   Self-Powered Resonance Tunability
•   Simultaneous Energy Harvesting and Vibration Mitigation
•   Vibration Suppression
•   Smart Structures and Meta-Materials
•   Analytical and Experimental Modal Analysis



HONORS AND AWARDS

1. Seal of Excellence for Marie Skłodowska-Curie Fellowship Application, European Commission,
   2024.
2. Editor-in-Chief’s Choice Paper - Nonlinear Dynamics Journal, July 2024.
3. Invited by Shenzhen Talent Institution for a Scientific Visit and Study Tour, Shenzhen, China,
   December 2023.

                                                   4
4. Awarded the ‘2023 Best PhD Thesis Award (Prof. Esmaeilzadeh Prize)’ by the Iranian Society of
   Acoustics and Vibration (ISAV), Iran, December 2023.
5. Editor’s Pick Paper - Applied Physics Letters, April 2023.
6. Awarded 1st among the Ph.D. students in the field of Mechanical Engineering, the 7th National
   Isar Award, Iran, 2023.
7. Top researcher of the Ph.D. program in the field of Mechanical Engineering- Applied Design
   (Dynamics, Vibrations, and Control), Iran University of Science and Technology, 2022.
8. Top Mechanical Engineering Ph.D. Student in Education and Research, Iran University of Science
   and Technology, 2022.
9. Ranked 1st among the Ph.D. students in the field of Mechanical Engineering- Applied Design
   (Dynamics, Vibrations, and Control), Iran University of Science and Technology, 2017-2023.
10. Awarded 3rd place for designing a “Vortex Bladeless Turbine” in the 9th International Conference
    on Acoustics and Vibration (24-25 December 2019).
11. Nominated for Eni2020 Award, 2019.


    SCHOLARSHIPS

1. Honorary Research Assistant Scholarship, The Chinese University of Hong Kong, 2021-2022.
2. Sabbatical Leave Scholarship, Iranian Ministry of Science, Research, and Technology, 2021-2022.
3. Full-Tuition PhD Scholarship, Iranian Ministry of Science, Research, and Technology, 2017-2023.


    COURSES

1. Structural Dynamics: (20/20)
2. Modal Analysis: (18.25/20)
3. Nonlinear Vibrations and Chaos: (17.6/20)
4. Perturbation Techniques: (18.6/20)
5. Automatic Control: (18.9/20)
6. Finite Element Methods: (18.5/20)


    TEACHING ACTIVITIES

1. Teaching Assistant, Nonlinear Vibrations (Instructor: Prof. Siamak Esmaeilzadeh Khadem),
   Tarbiat Modares University, Spring 2020.
2. Teaching Assistant, Advanced Vibrations (Instructor: Dr. Morteza Karamooz Mahdiabadi), Tarbiat
   Modares University, Spring 2021.



COMPUTATIONAL AND THEORETICAL RESEARCH


•    Self-powered tunable mechanical oscillators for resonance frequency tuning.


                                                  5
•    Dual-purpose multi-stable nonlinear energy sinks for simultaneous vibration suppression and
     energy harvesting.
•    Exploiting meta-surfaces to enhance energy harvesting from galloping oscillations and mitigate
     galloping vibrations.
•    Deployment of nonlinear restoring forces to enhance bandwidth of energy harvesters under base,
     galloping, and acoustic excitations.
•    Finite element modeling, verification, and model updating using ANSYS


EXPERIMENTAL ACTIVITIES


•    Design, Testing, and Validation of Self-Powered Tunable Oscillators, Smart Materials and
     Structures Lab, CUHK (Jun 2023 – Present)
•    Design and Analysis of a Dual-Purpose Vibration Absorber/Energy Harvester, Smart
     Materials and Structures Lab, CUHK (Jun 2021 – Jun 2022)
•    Modeling and Parameter Extraction of an Electrodynamic Shaker, Smart Materials and
     Structures Lab, CUHK (2022)
•    Design and Manufacturing of Iran's First Vibration Stress Relief Device, Modares Vibratory
     Stress Relief Knowledge Enterprise (2017 – 2023)
•    Design and Fabrication of a Wake-Induced Energy Harvester, presented at the 9th International
     Conference on Acoustics and Vibration (ISAV 2019)
•    Modal Analysis and Response Measurement of Pipes and Plates, Sound and Vibration
     Laboratory, Tarbiat Modares University (2014 – 2018)
•    Experimental Modal Analysis, Modal Analysis Laboratory, Iran University of Science and
     Technology (2018)

    ACADEMIC SERVICE


Journal Reviewer:
1. Nonlinear Dynamics (Springer)
2. Mechanical Systems and Signal Processing (Elsevier)
3. Journal of Intelligent Material Systems and Structures (Sage)
4. Engineering Structures (Elsevier)
5. Aerospace Systems (Springer)
6. Scientific Reports (Nature Portfolio)


Conference Reviewer:
•    International Design Engineering Technical Conferences & Computers and Information in
     Engineering Conference (IDETC-CIE), American Society of Mechanical Engineers (ASME), 2025.
Invited Speaker (Webinar):
•    “Simultaneous Vibration Suppression and Energy Harvesting Using Multi-stable Nonlinear
     Vibration Absorbers,” Iranian Society of Acoustics and Vibration (ISAV), 2024.



                                                 6
    SKILLS

•    Programming and Analytical Computing: MATLAB, Mathematica
•    CAD and Finite Element Analysis (FEA): CATIA, SolidWorks, Abaqus, ANSYS
•    Experimental Testing: Modal analysis (Impact hammer test, Shaker test), Vibration response
     analysis, PZT energy harvesting, flow-induced energy harvesting, Vibration stress relief (VSR)
•    Additive Manufacturing: 3D model design and printing



    LANGUAGE PROFICIENCY

1. English: Advanced
2. Persian: Native
3. Azeri: Elementary Proficiency



    INTERESTS AND HOBBIES


•    Long-Distance Walking and Hiking
•    Reading Scientific Biographies (with a focus on notable inventors and researchers)
•    Swimming




                                                   7
Abstract of Research
Vibrations are ubiquitous in the environment, industry, and daily life. They originate from natural
phenomena such as earthquakes and wind, mechanical phenomena like engines and rotating
blades, and even human movement. In any form, uncontrolled vibrations are considered
undesirable and can lead to deterioration, and they require attention and suppression.
Mechanical systems encompass a wide range of systems all involving motion and energy. These
systems span various scales, from large-scale applications such as energy generation and
transportation, to medium-scale systems like medical devices and home appliances, and down to
small-scale components such as resonators and sensors. Due to the coexistence of energy and
structural flexibility in mechanical systems, vibrations are inherent in them. Examples of such
systems prone to vibrations are illustrated in Figure 1. While small levels of vibrations may cause
discomfort or minor disturbances, high-amplitude vibrations occurring at the natural frequency of
a system, known as resonant vibrations, can lead to sudden failures and catastrophic consequences.
From the perspectives of design, maintenance, and reliability, all levels of vibration must be
addressed, as even low-amplitude vibrations can cause damage over a long time, leading to
machine shutdowns, financial losses, and risks to human safety.
Throughout all industrial revolutions, efficiency and reliability have been central goals. This fact
implies that regardless of technological advancements, mechanical systems must be continuously
improved to meet higher standards. However, vibration remains a primary cause of failure and a
barrier to achieving these goals. Thus, the presence of vibrations undermines the objectives of
modern industrial paradigms. This underscores why vibration mitigation has long been a core
research topic in mechanical engineering and applied mathematics. Despite decades of research in
this area, the increasing complexity of mechanical systems continues to drive the need for more
efficient vibration control techniques.
Effective vibration mitigation relies on understanding the characteristics of system, fault locations
that lead to vibrations, and response amplitudes, all of which require an accurate model of system.
Yet, as mechanical systems grow more complex, traditional modeling approaches become either
infeasible or computationally expensive. In recent years, data-driven methods such as Machine
Learning (ML) have emerged as powerful tools for modeling complex systems. ML can discover
intricate relationships between system inputs and outputs, so serving as a surrogate for traditional
mathematical models. It requires data to estimate system responses and can adapt to the changes
in systems.
Given the importance of accurate understanding of systems for vibration control and the challenges
of traditional modeling, this research, titled SMART-VM (Smarter Vibration Mitigation in
Mechanical Systems by Leveraging Machine Learning), proposes leveraging data-driven
techniques to model vibrating mechanical systems. These models will then be used to design
vibration controllers for effective vibration suppression. Specifically, simulation datasets of
continuous vibration systems will be used to train ML algorithms to estimate system characteristics
such as response behavior and roots of vibrations. This information will then guide the design of
vibration controllers for improved mitigation. SMART-VM is expected to deliver significant
scientific, practical, and economic impacts by advancing vibration control in mechanical systems.




                                                 1
Proposed Objectives and Potential Impacts of Research Project/Activities


Objectives of Research Project/Activities:
The objectives of this project are classified into research and knowledge-transfer objectives, as
outlined below:
(a) Research Objectives: Most existing vibration reduction techniques heavily rely on
mathematical and Finite Element (FE) modeling. While these approaches perform well for certain
systems, the increasing complexity of mechanical systems makes mathematical modeling
challenging, cumbersome, and sometimes infeasible. Moreover, even when a mathematical model
exists, it may be computationally expensive to solve, leading to performance degradation over
time. To address these limitations, the following research objectives are proposed:


       •   Identify a continuous mechanical system under external forcing that exhibits
           vibrations.
       •   Develop a mathematical model for the continuous system under external forcing.
       •   Simulate the developed model and generate sufficient data for training a Machine
           Learning (ML) model.
       •   Investigate potential approaches and methods for developing suitable and efficient
           ML models.
       •   Develop a data-driven ML model for the mechanical system and train it using the
           simulated data.
       •   Estimate system characteristics, response behavior, and sources of vibration using the
           ML surrogate model.
       •   Determine techniques to suppress vibrations in the system.
       •   Use the ML-based insights to optimally design a vibration controller.
       •   Disseminate research results through high-quality journal publications.
       •   Present findings at conferences with financial support from the Junior Research
           Fellowship scheme.
       •   Organize workshops and talks to share research outcomes.


   (b) Knowledge-transfer Objectives: The applicant’s research background includes linear
   and nonlinear structural mechanics, numerical and approximate analytical solutions, and
   experimental modal analysis. While these skills support several research objectives in part
   (a), developing ML models requires additional training. The knowledge-transfer objectives
   are:




                                                1
       •   Learn the mathematical foundations of ML by participating in courses offered by the
           host university, other universities in Hong Kong, or through online resources.
       •   Acquire AI-related prerequisites for ML development through similar educational
           channels.


Potential Impacts of Research Project/Activities:
This project is a multidisciplinary effort bridging structural mechanics, applied mechanics,
machine learning, and vibration mitigation. It aims to leverage the ML paradigm to propose an
innovative, intelligent, and enhanced vibration suppression scheme. The outcomes are expected to
have academic, educational, and practical impacts, detailed as follows:


(a) Research impact: A significant portion of existing literature on vibration mitigation relies on
mathematical modeling. By integrating analytical mechanics with machine learning, the SMART-
VM project introduces a hybrid modeling approach for vibrating mechanical systems. This
research identifies the sources of vibration through data-driven models, advancing beyond
traditional paradigms. Furthermore, by linking ML-based response estimation with vibration
control, SMART-VM contributes to more effective vibration reduction strategies. This
methodology is particularly valuable given that most existing models are only efficient for simple
systems.


(b) Educational impact: Despite the strong potential of data-driven models to uncover complex
relationships between system inputs and outputs, their application in practical vibration problems
remains underutilized. Currently, their use is often limited to solving abstract mathematical
problems, which, while valuable, do not fully demonstrate ML practical capabilities. This research
will highlight the importance of integrating AI skills into undergraduate and graduate programs
alongside classical engineering courses. Workshops, webinars, and conferences will help promote
this educational impact. Additionally, the outcomes of SMART-VM will serve as educational
resources for students in vibration analysis, control systems, and data-driven modeling.


(c) Research or professional development or potential for practical application: The project
will serve as a catalyst for professional growth of Applicant by arming them with capabilities in
ML, mathematical modelling, and vibration control. Through the practical development of data-
driven vibration suppression strategies, Applicant will gain valuable experience in linking
theoretical mechanics with real-world engineering challenges. SMART-VM also opens ways for
collaboration with industry in sectors such as aerospace and automotive, where vibration control
is vital for efficiency and reliability. The intelligent passive vibration absorption developed through
SMART-VM has the potential to be integrated into existing mechanical systems. This offers
adaptive, efficient, and practical solutions for mitigating vibration. These innovations not only
enhance the reliability of mechanical systems but also contribute to sustainable engineering by
reducing costs of maintenance and energy consumption.

                                                  2
Background of Research, Research Plan and Methodology
1) Background of the Proposed Research
Due to the destructive nature of vibrations in mechanical systems, the topic of vibration mitigation
has received significant attention over the years. Control strategies to achieve this can be classified
into passive, semi-active, and active methods [1, 2]. Passive techniques suppress vibrations by
coupling a secondary system, called a dynamic vibration absorber (DVA), with the main vibrating
system to dissipate energy. In this method, the resonance frequency of the absorber must be tuned
to match that of the main system. Semi-active methods offer similar mitigation but compensate for
changes in the vibrating system’s parameters by adjusting the mass, stiffness, and damping of the
absorber. Active techniques, on the other hand, use a system of sensors and actuators to control
vibrations.
In terms of energy consumption and design complexity, passive methods require no input energy
and have relatively simpler designs, followed by semi-active and active methods. However, in
terms of robustness, active methods are the most resilient to changes in system parameters and
excitation characteristics, followed by semi-active and passive approaches. Thus, choosing among
these three methods involves a trade-off between design complexity and energy consumption.
Due to the advantages of passive methods, if they can be tuned to the resonance frequency of the
vibrating system, they offer excellent performance without requiring input energy. This is
particularly important for vibration mitigation in devices where power supply is limited, and it also
minimizes the complexity of the modified system [3-5]. However, prior knowledge of the system’s
resonance frequencies is crucial for effective vibration reduction. This becomes even more critical
in continuous systems, which possess multiple resonance frequencies, and where the excitation
frequency determines the response frequency. In addition to knowing the resonance frequencies,
the location where the absorber is coupled is also a key factor in passive vibration mitigation,
especially in systems such as rotor shafts and blades [6-8]. Therefore, accurate system modeling
is essential to achieve high-performance passive vibration mitigation.
However, the increasing complexity of mechanical systems, driven by demands for greater
efficiency, productivity, and reliability, makes mathematical modeling increasingly cumbersome
or even infeasible. The emergence of Machine Learning (ML) offers a promising alternative by
estimating system responses through a data-driven paradigm, bypassing the need for conventional
models. This groundbreaking methodology can be used to extract the information required for
designing passive vibration controllers and advancing vibration mitigation.
A review of the existing literature reveals that most studies using ML focus solely on estimating
system responses, without addressing vibration mitigation [9-13]. Combining ML with passive
vibration control remains an underexplored research area, and this project, SMART-VM, has the
potential to make a meaningful contribution to this niche.
The Applicant is an early-stage researcher in applied mechanics, with expertise in developing
linear and nonlinear models of continuous vibration systems, smart materials, energy harvesting,
passive vibration mitigation, and experimental modal analysis. However, the Applicant has not yet
gained experience in Machine Learning. Given ML strong potential for estimating mechanical
system responses, the Applicant is motivated to integrate this emerging tool with existing expertise
to propose a novel paradigm for vibration control.

                                                  1
Thus, the SMART-VM project provides an opportunity for the Applicant to receive training in ML
and apply it to the study of vibrating systems. The acquired ML knowledge will be combined with
the Applicant’s existing expertise in vibration mechanics to contribute to the advancement of
vibration mitigation in mechanical systems. To further clarify the relevance of the Applicant’s
background to this project, a brief description of their research experience will be provided.


2) Research Background of Applicant
In reverse chronological order, the Applicant’s research activities include:
➢   Self-powered mechanical oscillators
➢   Analytical and experimental modal analysis
➢   Simultaneous energy harvesting and vibration mitigation using linear and nonlinear absorbers
➢   Energy harvesting from environmental vibrations and fluid-structure interactions
Through these experiences, the Applicant has attained the following skills:
•   Developing continuous models of nonlinear vibration systems:
Extracting mathematical models of continuous vibrating systems underpins the Applicant’s
research background. These models have been developed using principles of mechanics, elasticity
theory, smart material modeling, and model-order reduction techniques.


•   Numerical simulation of nonlinear system responses:
In all research work, the Applicant has employed numerical methods to simulate system responses
in both time and frequency domains. This approach is essential for gaining insight into system
behavior, although it can be computationally expensive.
•   Applying approximate-analytical methods to simulate nonlinear vibration systems:
Since nearly all of the Applicant’s research has focused on nonlinear systems, advanced
approximate analytical techniques, such as the method of multiple scales (MMS) and the harmonic
balance method (HBM), have been extensively used. These methods offer computational
efficiency and can reveal certain dynamic behaviors that numerical methods may fail to capture.
•   Analytical and experimental modal analysis:
A significant portion of the Applicant’s research has involved modal analysis. This includes
designing test setups using 3D modeling software, fabricating components, and conducting
experiments using various modal analysis tools. These tools include electrodynamic shakers,
impact hammers, piezoelectric sensors, laser sensors, and data acquisition units. The results are
then analyzed using a variety of techniques, such as curve fitting, to extract the modal
characteristics of the system. Through analytical and experimental modal analysis, it is possible to
derive decoupled equations and estimate the underlying modal parameters.




                                                 2
3) Research Plan
The schematic of the proposed research project, SMART-VM, is shown in Figure 2. In this project,
the mechanical system is first selected, and its governing mathematical equations are developed.
These equations are then simulated to generate data for training a machine learning model. The
trained ML model is subsequently used to design a controller that suppresses vibrations.
Accordingly, the project is structured into eight phases (P1–P8), each with a specific objective and
approach, as described below.
P1) Training in Machine Learning Techniques: Since ML is a cornerstone of the SMART-VM
project and the Applicant has not yet received formal training in this area, the first phase is
dedicated to learning ML fundamentals. This includes prerequisite mathematics, various ML
methods, and their applications. Relevant courses and departments have already been identified,
and online resources from other universities will also be utilized. Completion of these courses will
fulfill the objectives of P1.
P2) Mathematical Modeling: Next, a mechanical system will be selected for vibration
suppression, and its mathematical model will be developed using principles from continuum
mechanics, elasticity theory, and vibration analysis. Analytical modal analysis and model order
reduction techniques will be applied to render the model computationally efficient. As shown in
Figure 2, the mathematical model of the system can be represented as the following form:
 𝐌𝐘̈ + 𝐂𝐘̇ + 𝐊𝐘 = 𝐅                                                                                    (1)


In Equation (1), 𝐌 is the mass matrix, 𝐂 is the damping matrix, and 𝐊 is the stiffness matrix of the
system. In addition, 𝐘 and 𝐅 represent the response and external forcing vectors, respectively.
Furthermore, the over-dot notation indicates differentiation with respect to time.
P3) Numerical Simulations: The developed mathematical model will be simulated to generate
datasets for ML training. Comprehensive numerical simulations will produce both training and test
datasets. Candidate approaches include numerical integration techniques and Finite Element
Methods (FEM). Software such as MATLAB and Mathematica will be used for this purpose.
P4) Approximate-Analytical Solutions: To complement the dataset, especially in cases where
numerical simulations (P3) fail to yield results, approximate analytical methods such as the method
of multiple scales and harmonic balance will be employed. These techniques convert the governing
partial or ordinary differential equations into algebraic equations. Symbolic computation software
like MAPLE will be used in this phase.
P5) ML Model Training: Using the datasets generated in P2–P4, a machine learning algorithm,
specifically a Neural Network (NN), will be trained. The NN will be trained on the training dataset
and validated using the test dataset to estimate system characteristics, response behavior, and
sources of vibration. This phase will extract the necessary information for designing an efficient
vibration absorber. Python and its ML libraries will be used for this purpose.
P6) Experimental Tests and Validation: If the fellowship is extended by an additional 12 months
after the initial two years, the Applicant plans to validate the ML model using experimental data. This may
involve using existing test setups or designing new ones to collect data. The Supervisor’s laboratory at the
host university is well-equipped for this purpose, with resources including:


                                                     3
        •       3D design software (SolidWorks, Abaqus)
        •       3D printers for test setup fabrication
        •       Newport vibration isolation table
        •       Three B&K electrodynamic shakers
        •       Polytec 3D scanning laser vibrometer
        •       B&K LDS LASER USB shaker controller
        •       B&K piezoelectric accelerometers and force sensors
P7) Annual Progress Reports: As per fellowship regulations, the Applicant will submit annual
progress reports every 12 months. These reports will summarize project progress and will be
submitted within three months of the end of each reporting period.
P8) Dissemination of Results: Given the interdisciplinary nature of SMART-VM, multiple
channels will be used to disseminate research outcomes:
Journal publications: Target journals include high-quality, peer-reviewed publications in
dynamics, vibration control, and applied ML:
➢       Mechanical Systems and Signal Processing (Elsevier)
➢       Journal of Sound and Vibration (Elsevier)
➢       Nonlinear Dynamics (Springer)
➢       Computer Methods in Applied Mechanics and Engineering (Elsevier)
➢       Journal of Vibration and Acoustic (ASME)

•       Conferences: With support from the Fellowship and host university, the Applicant will attend
        leading conferences to present findings, engage with peers, and receive feedback. Potential
        conferences include:

        ➢ Vibration and Noise Conference, The American Society of Mechanical Engineers (ASME)
        ➢ International Design Engineering Technical Conferences & Computers and Information in
          Engineering Conference, ASME.
        ➢ The SPIE Smart Structures + NDE, The international society for optics and photonics
        ➢ International Conference on Noise and Vibration Engineering (ISMA)

•       Workshops and Talks: Research outputs will also be shared through workshops and invited talks.


In table. 1, the approximate time for each of the phases are given.
    Table 1. Table 1. Gantt chart of the project phases
                                                                Year 1                                                              Year 2                                                                  Year 3
                 Phase
                                                                                    M10
                                                                                          M11
                                                                                                M12
                                                                                                      M13
                                                                                                            M14
                                                                                                                  M15
                                                                                                                        M16
                                                                                                                              M17
                                                                                                                                    M18
                                                                                                                                          M19
                                                                                                                                                M20
                                                                                                                                                      M21
                                                                                                                                                            M22
                                                                                                                                                                  M23
                                                                                                                                                                        M24
                                                                                                                                                                              M25
                                                                                                                                                                                    M26
                                                                                                                                                                                          M27
                                                                                                                                                                                                M28
                                                                                                                                                                                                      M29
                                                                                                                                                                                                            M30
                                                                                                                                                                                                                  M31
                                                                                                                                                                                                                        M32
                                                                                                                                                                                                                              M33
                                                                                                                                                                                                                                    M34
                                                                                                                                                                                                                                          M35
                                                                                                                                                                                                                                                M36
                                      M1
                                            M2
                                                 M3
                                                      M4
                                                           M5
                                                                M6
                                                                     M7
                                                                          M8
                                                                               M9




    1: Training in Machine Learning     █   █    █    █    █    █
    2: Mathematical Modeling                     █    █    █    █    █    █    █
    3: Numerical Simulations                                                        █     █     █     █     █     █
    4: Approximate-Analytical Solutions                                                               █     █     █     █     █     █     █
    5: ML Model Training                                                                                                                  █     █     █     █     █     █
    6: Experimental Testing                                                                                                                                                   █     █     █     █     █     █     █     █     █     █     █
    7: Annual Progress Reports                                                      █     █     █                                                           █     █     █                                                           █     █     █
    8: Dissemination of Results                                                     █     █                                         █     █                                                                 █     █                 █     █




                                                                                                                  4
Illustrations



                                                   Turbine


                         Car

                                      Mechanical
                                       systems
                                       prone to
                                      vibrations
                                                             Train


                               Airplane




                Figure 1. Examples of mechanical systems prone to vibrations




 Figure 2. Schematic of the SMART-VM project illustrating the integration of machine learning
 with passive control design for vibration mitigation. Decoupled: system without absorber,
 S0=0.25Lm: system with absorber placed at 25% of beam length; S0=0.5Lm: system with
 absorber placed at 50% of beam length; S0=0.75Lm: system with absorber placed at 75% of
 beam length.
Pathway to Impact Statement
The interdisciplinary, intelligent, and practical nature of the SMART-VM project brings significant
impacts beyond academia. Its short-, medium-, and long-term outcomes contribute to communities
outside academia, including the economy, society, environment, and public health. At the same
time, the project faces risks and challenges that must be addressed.
(a) Potential Beneficiaries of SMART-VM
Short-term (1–3 years):
The project introduces a new, innovative, and intelligent paradigm to industry, helping establish
its relevance and visibility. Beneficiary sectors include aerospace, automotive, railway, and
manufacturing, where vibration control is critical for performance and reliability.
Medium-term (4–10 years):
The application of SMART-VM findings in industry is expected to lead to increased productivity
and safety. As end-users of these sectors, society benefits directly through improved safety and
reduced operational risks. Moreover, since excessive vibrations contribute to noise and CO2
emissions in mechanical systems, SMART-VM supports environmental goals by mitigating these
effects. This impact is significant for two reasons:
   1. It reduces government spending on noise and emission control, representing a clear
      economic benefit.
   2. It improves public health and quality of life by eliminating harmful environmental factors.
Long-term (over 10 years):
SMART-VM has the potential to deliver lasting practical impacts. By enhancing reliability and
performance through improved vibration mitigation, it aligns with industrial innovation initiatives
such as Industry 4.0 and 5.0. After a tentative 10-year period, SMART-VM is expected to be
recognized as a standard paradigm for effective vibration suppression in complex mechanical
systems. As systems become more sophisticated over time, the value of SMART-VM will become
increasingly tangible.


(b) Beyond-Academia Impact via Industrial Innovation Policies
The demand for increased performance, safety, and reliability has resulted in setting systematic
plans to achieve goals and monitoring their progress. There are well-established policies set by
different countries and unions to achieve these goals. SMART-VM contributes to key
transformation frameworks either at national or international levels. Examples are:
   •   Made in China 2025:
       This ambitious policy, launched in 2015, aims to transform China from a low-cost
       manufacturer into a global leader in high-tech industries. A central objective is
       technological advancement. SMART-VM aligns closely with this goal by integrating
       machine learning and mechanical engineering to enhance industrial capabilities.


                                                1
    •   Industry 4.0 and 5.0:
    A cornerstone of these initiatives is efficiency enhancement. SMART-VM links ML and
    mechanical engineering to improve the quality, efficiency, and reliability of mechanical
    systems, advancing existing techniques to meet these goals.


To increase the likelihood of achieving the identified benefits and reaching its intended
beneficiaries, the following measures will be adopted:
    •   Dissemination of research outputs through journal papers, conferences, workshops, and
        public talks.
    •   Adoption of open science practices, where permitted by data and research privacy
        regulations.
    •   Networking with target beneficiaries through participation in seminars and industry-
        focused conferences.


(c) Potential Risks and Mitigation Strategies
As an innovative and multidisciplinary project, SMART-VM faces several potential risks. The
following strategies are proposed to mitigate them:
•   Computational Efficiency of Models: Due to the inherent complexity of many mechanical
    systems, pure mathematical modeling can become cumbersome and computationally
    inefficient. To address this, the Finite Element Method will be considered either in combination
    with analytical modeling or as the primary modeling approach.
•   Computational Resource Constraints: Training and validating ML models for high-
    dimensional, dynamic systems may require significant computational resources. To mitigate
    this, the project will utilize the host university High Performance Computing (HPC) facilities
    to support computational tasks.
•   Data Dependency: Data-driven methods rely heavily on the quality and diversity of training
    data. Their convergence and extrapolation capabilities can be affected by data limitations. To
    address this, Applicant will undergo ML training and collaborate with experienced ML
    researchers to develop robust algorithms with high estimation capacity.
•   Interdisciplinary Challenges: Since Applicant is not formally trained in ML, integrating
    machine learning with mechanics may pose challenges. To overcome this, collaborations with
    researchers and institutions, locally, nationally, and internationally, have already been
    identified. Sabbatical studies and short research visits are also planned to support the success
    of the project.
•    Experimental Validation and Data Quality: Conducting experiments for data collection and
    model validation may present challenges in acquiring high-fidelity, low-noise data. This risk
    will be mitigated by employing advanced vibration isolation techniques and utilizing high-
    precision measurement devices to ensure the quality of the experimental data.

                                                 2
References


1. Li, H., et al., A state-of-the-art review on negative stiffness-based structural vibration control.
Engineering Structures, 2025. 323: p. 119247.
2. Jalili, N., A comparative study and analysis of semi-active vibration-control systems. Journal
of vibration and acoustics, 2002. 124(4): p. 593-605.
3. Rezaei, M., et al., A comparative study on vibration suppression and energy harvesting via
mono-, bi-, and tri-stable piezoelectric nonlinear energy sinks. Nonlinear Dynamics, 2024.
112(13): p. 10871-10910.
4. Yang, F., R. Sedaghati, and E. Esmailzadeh, Vibration suppression of structures using tuned
mass damper technology: A state-of-the-art review. Journal of Vibration and Control, 2022. 28(7-
8): p. 812-836.
5. Tigli, O.F., Optimum vibration absorber (tuned mass damper) design for linear damped
systems subjected to random loads. Journal of sound and vibration, 2012. 331(13): p. 3035-3049.
6. Aghayari, J., et al., A novel modal vibration reduction of a disk-blades of a turbine using
nonlinear energy sinks on the disk. Mechanism and Machine Theory, 2021. 155: p. 104048.
7. Sun, C. and V. Jahangiri, Bi-directional vibration control of offshore wind turbines using a 3D
pendulum tuned mass damper. Mechanical Systems and Signal Processing, 2018. 105: p. 338-
360.
8. Bab, S., et al., Vibration attenuation of a continuous rotor-blisk-journal bearing system
employing smooth nonlinear energy sinks. Mechanical Systems and Signal Processing, 2017. 84:
p. 128-157.
9. AL Houri, A., A. Habib, and Z.A. Al-Sadoon, Artificial intelligence-based design and analysis
of passive control structures: an overview. Journal of Soft Computing in Civil Engineering, 2025.
9(3): p. 137-168.
10. Jiang, D., et al., Rotor dynamic response prediction using physics-informed multi-LSTM
networks. Aerospace Science and Technology, 2024. 155: p. 109648.
11. Raj, R.N. Physics Informed Neural Network for Solution of Duffing Oscillators. in
Proceedings of the 2nd International Conference on Nonlinear Dynamics and Applications
(ICNDA 2024), Volume 3: Dynamical Models, Communications and Networks. 2024. Springer
Nature.
12. Garpelli, L.N., et al., Physics-guided neural networks applied in rotor unbalance problems.
Structural Health Monitoring, 2023. 22(6): p. 4117-4130.
13. Yousefian, R. and S. Kamalasadan, A review of neural network based machine learning
approaches for rotor angle stability control. arXiv preprint arXiv:1701.01214, 2017.




                                                   1
Past Research Experience
Throughout my research career, starting from my master’s degree, continuing through my PhD,
and continuing to my current post-doctoral fellow position, I have been trained in various fields
related to mechanics. These include modeling, numerical simulations, approximate-analytical
techniques, and model and response analysis tests. In what follows, my past research experience
will be outlined in 7 categories in different research eras.
1) Flow-induced energy harvesting: First, my main investigations were focused on energy
harvesting from fluid-structure interactions (FSI) or flow-induced vibrations (FIV). That is, trying
to harvest electrical energy from flow energies, specifically wind. I studied energy harvesting from
a phenomenon called wake-galloping. A wake-galloping flow energy harvester vibrates linearly
under fluid force and has a narrow resonance bandwidth. Hence, its efficiency drops significantly
under variable flow speeds. We proposed an idea for capturing energy from a broad range of vortex
shedding frequencies associated with varying wind speeds in [1]. The proposed system is
composed of an array of piezoelectric generators. The results show that the proposed system has a
wider resonance region, compared to conventional wake-galloping energy harvesters. Hence, the
superior performance of the proposed system design is confirmed. My next research experience in
flow energy harvesting is [2]. In this paper, we studied the influences of the higher-order terms in
aerodynamic force models on the response of galloping piezoelectric energy harvesters. To model
the aerodynamic force, the 3rd and 7th order galloping forces were considered. The results disclosed
that considering higher-order terms in the aerodynamic model is necessary for characterization of
the mechanical and electrical responses. Furthermore, we participated as the ENERGY team in the
competition of ‘Vortex Bladeless Turbine’ at the 9th International Conference on Acoustics and
Vibration, which was held on 24-25 December 2019. We designed and manufactured a galloping
energy harvester. We were awarded the third place for our proposed design.
2) Vibration energy harvesting: The next phase of my research focuses on vibration-based
energy harvesting. I investigated the potential of direct vibrations to generate electrical energy,
particularly the role of nonlinearities in enhancing the bandwidth of energy harvesters. In [3], I
proposed the idea of applying a nonlinear restoring force in order to increase the bandwidth of a
piezoelectric energy harvester in the presence of harmonic excitation. It was shown that the
nonlinear restoring force broadens the bandwidth of high-amplitude energy harvesting
considerably. In [4], I investigated the idea of energy harvesting from nonlinear vibrations of a
beam under simultaneous hard excitations. It was found that the level of the harvested voltage was
remarkably amplified in the case of simultaneous excitations, which is promising for energy
harvesting purposes. In [5], I examined energy harvesting from a hard harmonic excitation. It was
observed that the hard force results in sub and super-harmonic resonances. It is revealed that the
hard excitation leads to a remarkable voltage generation in the secondary resonances and results
in a broadband energy harvesting.
3) Acoustic energy harvesting: I have also investigated the potential of nonlinear characteristics
in improving acoustic energy harvester performance. In [6], we proposed and examined the
addition of a nonlinear magnetic restoring force onto an acoustic energy harvester in order to tune
the resonance bandwidth to the acoustic pressure frequency, which varies due to temperature
changes. It was shown that the magnetic force tunes the natural frequency and broadens the
resonance region, which compensates for temperature changes.


                                                 1
4) Simultaneous energy harvesting and vibration mitigation: My recent research in PhD
concentrated on simultaneous energy harvesting and vibration suppression. In this approach, an
electromechanical transduction mechanism is integrated into a vibration absorber. Then, the
modified absorber can suppress the vibrations and harvest electrical energy at the same time. In
my first investigations in this field, I examined simultaneous vibration suppression and energy
harvesting through a dynamic vibration absorber (DVA) with piezoelectric coupling [7]. It is
observed that the DVA effectively annihilates the host structure vibrations, up to 98.5%, and at the
same time harvests considerable levels of power. Next, I proposed the idea of concurrent energy
harvesting and vibration reduction using bi- and tri-stable nonlinear absorbers [8-11]. The results
showed that the bi- and tri-stable absorbers exhibit complex dynamics and harvest energy and
suppress vibrations better than a linear absorber. I have also conducted experimental studies and
validated the previous findings experimentally in [12]. My PhD thesis was awarded as the best
PhD thesis by the Iranian Society of Acoustics and Vibration (ISAV).
5) Auxetic structures: As a collaborator, I have been involved in an ongoing research project on
investigating the role of auxetic structures in the performances of vibrating systems from the
perspectives of vibration mitigation, energy harvesting from vibrations and FSI [13].
6) Experimental modal analysis: I have had significant experience in the modal analysis of
flexible structures using impact hammer and shaker tests. Modal analysis is part of my
experimental research [12].
7) Self-powered frequency tuning: My current research focus is self-powered mechanical
oscillators that are able to tune their resonance frequency via their own energy. In this technique,
we have proposed, for the first time, a mechanical oscillator that provides the energy required for
tuning its resonance frequency. This research is ongoing research and has cultivated in one journal
paper [14], one conference paper [15], and one under-review paper.


References
[1] https://doi.org/10.1016/j.ijmecsci.2019.105135.
[2] https://doi.org/10.22064/tava.2021.530769.1182.
[3] https://doi.org/10.1016/j.ijengsci.2017.04.001.
[4] https://doi.org/10.1016/j.energy.2019.05.212.
[5] https://doi.org/10.1007/s11012-020-01187-1.
[6] https://doi.org/10.1088/1361-665X/ab3a6a.
[7] https://doi.org/10.1007/s00419-021-02063-4.
[8] https://doi.org/10.1016/j.apm.2021.09.044.
[9] https://doi.org/10.1016/j.ijmecsci.2021.106618.
[10] https://doi.org/10.1016/j.energy.2021.122376.
[11] https://doi.org/10.1007/s11071-024-09562-3.
[12] https://doi.org/10.1016/j.jsv.2022.117413.
[13] https://doi.org/10.1117/12.3051656.
[14] https://doi.org/10.1016/j.jsv.2024.118918.
[15] https://doi.org/10.1117/12.3051647.



                                                 2
Leadership Experience


Throughout my PhD and postdoctoral research, I have cultivated leadership skills across multiple
dimensions:


1. Personal Leadership and Initiative
   •   Defined and developed several research projects, including the successful drafting and
       development of research proposals.
   •   Developed nonlinear mathematical models for complex systems (vibrating systems,
       electromechanical systems, and fluid-structure interactions).
   •   Designed, constructed, and validated two experimental test setups, performed
       comprehensive modal analysis and time-domain and frequency-domain tests.
   •   Established collaborations with high-tech companies specializing in material sciences
       and measurement devices to advance research objectives.


2. Mentorship and Team Leadership
   •   Led a multidisciplinary team in designing a Vortex Bladeless Turbine for the 9th
       International Conference on Acoustics and Vibration (2019). Our team, "ENERGY,"
       designed and manufactured a galloping energy harvester, earning third place in the
       competition.
   •   Mentored master students PhD candidates (non-supervisory) during the final year of
       my PhD studies, providing guidance in modelling and experimental methods. This
       collaboration contributed to three journal publications and two conference papers.
   •   Collaborated closely with a senior research associate during my postdoc to lead two
       research projects. My contributions in modelling and experimental design were
       instrumental in preparing two manuscripts for submission.


3. Advisory and Academic Leadership
   •   Provided expert feedback on multiple Master’s and PhD research proposals, leading to
       significant improvements in project design and methodological rigor.




                                               1
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
⚫    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
⚫    The nominee should complete Part A of the referee’s report.
⚫    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
⚫    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
⚫    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Rezaei
                        (First name) Masoud
Host Faculty / School / Department / Faculty of Engineering/ Department of Mechanical and
Division / Unit ^ :                  Automation Engineering

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
⚫   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
⚫   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
⚫   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
⚫   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
⚫   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
⚫   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
⚫   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known Masoud since he was a MSc student in Iran. He reached out to me via e-mail to discuss few items
concerning his research on vibration energy harvesting. Since then, we have had many online discussions of
research topics related to vibration and flow energy harvesting.




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
 2. How would you rate the following attributes of the nominee?
    (Please tick as appropriate)
                                 Excellent      Very Good       Good                  Average         No basis
                                 (top 5%)       (top 6-20%) (top 21-50%)             or below           for
                                                                                    (lower than      judgment
                                                                                       50%)
 Intellectual capacity                               ☐                ☐                 ☐               ☐
 Ability for conducting
 scholastic research
                                                     ☐                ☐                 ☐               ☐
 Knowledge of proposed
 research study
                                                     ☐                ☐                 ☐               ☐
 Analytical power and
 reasoning
                                                     ☐                ☐                 ☐               ☐
 Judgment                                            ☐                ☐                 ☐               ☐
 Imagination and
 originality
                                                     ☐                ☐                 ☐               ☐
 Motivation and
 perseverance
                                                     ☐                ☐                 ☐               ☐
 Skills of writing and
 argumentation                        ☐                               ☐                 ☐               ☐
 Capacity for independent
 work
                                                     ☐                ☐                 ☐               ☐
 Reliability and sense of
 responsibility
                                                     ☐                ☐                 ☐               ☐
 Leadership experience               ☐                ☐                ☐                ☐                

 3. Please describe the research ability and potential of the nominee, and explain any other
    aspects of the nominee’s experience and skills that are relevant to JRFS.
    (Please refrain from mentioning the name of the supporting university. Please attach
    separate sheet(s) if more space is required.)


I base my assessment of Masoud’s potential on three main points: (i) our ongoing participation in online research
discussions over the years, (ii) my review of several of his publications, and (iii) his performance as a reviewer
for multiple papers I assigned to him for the journal Nonlinear Dynamics.

Masoud consistently demonstrates exceptional creativity, intelligence, diligence, and a strong work ethic. His
research is of the highest caliber, showcasing his ability to engage deeply with analytical, computational, and
experimental methods. As a reviewer for Nonlinear Dynamics, his reports have been consistently thorough,
insightful, and constructive, clearly reflecting his outstanding expertise in the field.

In short, Masoud is a true scholar whose excellence is evident in every aspect of his work. I firmly believe that
hiring someone of his caliber is a decision you will not regret. In my view, he would be a tremendous asset to any
university fortunate enough to have him.


                                                          3
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                             Recommend very strongly
                     ☐        Recommend strongly
                     ☐        Recommend with reservation
                     ☐        Do not recommend

     Name of Referee: MOHAMMED DAQAQ                                                  Title: Prof
                                   (in BLOCK letters)                                           (Prof / Dr / Mr / Ms / Mrs)*

     University / Organisation: New York University
     Position: Professor of Mechanical Engineering, NYU Abu Dhabi; Global Network Professor of Mechanical Engineering, NYU Tandon
     E-mail Address: <EMAIL>
     Telephone Number#: +971543078951
     (# Please provide country code and area code, e.g. +86-10-1234567)
⚫     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
     I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                                 Date: 08/29/2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                                       4
                                                                                  JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) REZAEI
                        (First name) Masoud
Host Faculty / School / Department / Faculty of Engineering/ Department of Mechanical and
Division / Unit ^ :                  Automation Engineering

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?


 I have known Masoud Rezaei since December 2018, when we began to exchange e-mails
 concerning our common research interest in energy harvesting from vibrating structures.
 Initially Masoud asked about clarifications in the analysis in one of my published papers,
 but this soon led to a discussion of wider issues of energy harvesting specifically, and
 structural dynamics in general. Our discussions have led to six joint papers that have been
 published in top international journals. I should emphasize that our collaboration has been
 conducted solely by e-mail and I have never met Masoud in person.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                             Excellent    Very Good        Good               Average          No basis
                             (top 5%)     (top 6-20%) (top 21-50%)           or below            for
                                                                            (lower than       judgment
                                                                               50%)
Intellectual capacity                         ☐                ☐                 ☐               ☐
                                               ☐                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                 
                                               ☐                ☐                 ☐               ☐
Knowledge of proposed
research study
                                 

                                               ☐                ☐                 ☐               ☐
Analytical power and
reasoning
                                 
Judgment                                      ☐                ☐                 ☐               ☐
                                               ☐                ☐                 ☐               ☐
Imagination and
originality
                                 

                                               ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                 

                                 ☐                              ☐                 ☐               ☐
Skills of writing and
argumentation
                                               

                                               ☐                ☐                 ☐               ☐
Capacity for independent
work
                                 

                                               ☐                ☐                 ☐               ☐
Reliability and sense of
responsibility
                                 
Leadership experience           ☐                             ☐                 ☐                ☐
3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
I have been very impressed with Masoud since our initial e-mail exchange. He has shown great
understanding of the issues involved in both analysis and experiments for energy harvesting and
structural dynamics. The questions Masoud asks are always detailed, relevant and important, and he
shows a great thirst for knowledge. Masoud always wants to understand the topic of interest in depth,
and has demonstrated this many times in our collaborations; for example, recently developing novel
experiments to determine electrical losses in energy harvesting systems. Masoud shows great
perseverance and is able to independently plan his research schedule. Masoud’s draft papers are
always well organized and his English is very good. He is very conscientious and hardworking.
Recently he has also started to develop excellent research proposals that demonstrate a detailed
knowledge of gaps in understanding that can be filled by research. This combination of great
academic ability, hard work and independence means that Masoud is developing into an outstanding
researcher. I would have happily recruited Masoud to a fellowship position at Swansea, although I
have recently retired. In summary, I very strongly recommend his appointment to the junior
fellowship, which will be a huge boost to his development.

                                                   3
                                                            JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: MICHAEL I. FRISWELL                             Title: Prof
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Swansea University
 Position: Emeritus Professor of Aerospace Structures, Faculty of Science and Engineering
 E-mail Address: <EMAIL>
 Telephone Number#: +44 7595 994983
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.



 Signature:                                                  Date: 30 August 2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
