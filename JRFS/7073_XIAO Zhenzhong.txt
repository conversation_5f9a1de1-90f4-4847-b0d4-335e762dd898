      (+44) 07947792741 / (+86) 13713378332   |
                                                   <PERSON>
                                                    <EMAIL>   |    timx.me   |    timxzz   |    timxzz     |  <PERSON>



Education
Max Planck Institute for Intelligent Systems                                                                                 Tübingen, Germany
Visiting Ph.D. student       Supervisors: <PERSON>, <PERSON><PERSON>                                                       March 2024 - Now
• Research in scalable test-time inference algorithms for large language models, and Verbalized Machine Learning.

International Max Planck Research School for Intelligent Systems,
                                                                                                                             Tübingen, Germany
The University of Tübingen (IMPRS-IS)
Ph.D. student in Machine Learning         Supervisor: <PERSON>                                                               Sept. 2021 - Now
• Research in generative models and deep probabilistic models and their applications.

University College London                                                                                            London, United Kingdom
MRes Computational Statistics and Machine Learning              Supervisor: <PERSON>                                    Sept. 2019 - Sept. 2020
• Graduate with Distinction.
• Thesis on using semi-supervised generative model to improve active learning.
• Took courses from the Gatsby Unit: Probabilistic & Unsupervised Learning, Approximate Inference and Learning in
  Probabilistic Models, Reproducing Kernel Hilbert Space in Machine Learning

The University of Oxford                                                                                              Oxford, United Kingdom
MSc Computer Science           Supervisors: <PERSON><PERSON>, <PERSON>                                                          Sept. 2018 - Sept. 2019
• Thesis on exploiting uncertainty in Neural Machine Translation (NMT) by applying Bayesian deep learning (BDL) to
  Transformers.
• The outcome of the thesis has been accepted by BDL workshop (Spotlight) in NeurIPS 2019.

The University of Manchester                                                                                     Manchester, United Kingdom
BSc (Hons) Computer Science with Industrial Experience                                                                       Sept. 2014 - July 2018
• First Class degree in Computer Science (Average marks: 84%, top 10% among the year).
• First year position in Single Honours Programme: 1st out of 135.
• Second year position in Single Honours Programme: 4th out of 122.



Experience
EMPLOYMENT
Morgan Stanley                                                                                                       London, United Kingdom
Industrial Placement Analyst                                                                                                 June 2016 - June 2017
• Worked with a global team in the Enterprise Infrastructure - Technology division, which is in charge of the world-round
  distributed system in the firm.
• Created and developed tools (mainly Angular, Python) for managing and monitoring the internal virtualization in-
  frastructure / Cloud platform that has a huge number of servers located around the world.
• The final product of this internship was deployed in the production environment and it is used by colleagues around
  the world.

The Advanced Processors Technologies Research Group,
                                                                                                                 Manchester, United Kingdom
The University of Manchester
Research Assistant        Supervisor: Dirk Koch                                                                              June 2015 - Sept. 2015
• Researched in FPGA and implemented FPGA Partial Reconfiguration using Altera tool flows.
• The research result was published on a top FPGA conference.

PROJECTS
               Main organizer of the prominent bi-weekly seminar series for the local Tübingen AI research community
               (Friday Talks @ Tübingen ). My role involves designing and implementing operational procedures for
 ✓             the entire series, which includes identifying and inviting speakers, managing all talk logistics, and
               actively creating opportunities for local AI doctoral students to present and receive feedback on their
               work. I also established a workflow for recording, editing, and publishing the talks on YouTube .


September 11, 2025                                   Tim Zhenzhong Xiao       ○    Résumé                                                        1
Publications
Large Language Models Are Zero-Shot Problem Solvers—Just Like Modern
Computers
Harvard Data Science Review                                                                                       2025
Tim Z. Xiao, Weiyang Liu, and Robert Bamler.

Flipping Against All Odds: Reducing LLM Coin Flip Bias via Verbalized
Rejection Sampling
arXiv preprint arXiv:2506.09998                                                                                   2025
Tim Z. Xiao, Johannes Zenn, Zhen Liu, Weiyang Liu, Robert Bamler, and Bernhard Schölkopf.

Reparameterized LLM Training via Orthogonal Equivalence Transformation
arXiv preprint arXiv:2506.08001                                                                                   2025
Zeju Qiu, Simon Buchholz, Tim Z. Xiao, Maximilian Dax, Bernhard Schölkopf, and Weiyang Liu.

Generating Symbolic World Models via Test-time Scaling of Large
Language Models
TMLR 2025                                                                                                         2025
Zhouliang Yu, Yuhuan Yuan, Tim Z. Xiao, Fuxiang Frank Xia, Jie Fu, Ge Zhang, Ge lin, and Weiyang Liu.

Verbalized Machine Learning: Revisiting Machine Learning with Language
Models
TMLR 2025                                                                                                         2025
Tim Z. Xiao, Robert Bamler, Bernhard Schölkopf, and Weiyang Liu.

Fast Diversity-Preserving Reward Finetuning of Diffusion Models via
Nabla-GFlowNets
ICLR 2025                                                                                                         2025
Zhen Liu, Tim Z. Xiao*, Weiyang Liu*, Yoshua Bengio, and Dinghuai Zhang.

Can Large Language Models Understand Symbolic Graphics Programs?
ICLR 2025, Spotlight - top 5.1%                                                                                   2025
Zeju Qiu*, Weiyang Liu*, Haiwen Feng*, Zhen Liu, Tim Z. Xiao, Katherine M. Collins, Joshua B. Tenenbaum, Adrian
Weller, Michael J. Black, and Bernhard Schölkopf.

Improving Probabilistic Diffusion Models With Optimal Covariance
Matching
ICLR 2025, Oral - top 1.8%                                                                                        2025
Zijing Ou*, Mingtian Zhang*, Andi Zhang, Tim Z. Xiao, Yingzhen Li, and David Barber.

Your Finetuned Large Language Model is Already a Powerful
Out-of-distribution Detector
AISTATS 2025                                                                                                      2025
Andi Zhang, Tim Z. Xiao, Weiyang Liu, Robert Bamler, and Damon Wischik.

A Note on Generalization in Variational Autoencoders: How Effective Is
Synthetic Data and Overparameterization?
TMLR 2024                                                                                                         2024
Tim Z. Xiao*, Johannes Zenn*, and Robert Bamler.

A Compact Representation for Bayesian Neural Networks By Removing
Permutation Symmetry
NeurIPS 2023 Workshop on Unifying Representations                                                                 2023
Tim Z. Xiao, Weiyang Liu, and Robert Bamler.




September 11, 2025                          Tim Zhenzhong Xiao   ○   Résumé                                         2
The SVHN Dataset Is Deceptive for Probabilistic Generative Models Due to a
Distribution Mismatch
NeurIPS 2023 Workshop on Distribution Shifts                                                                     2023
Tim Z. Xiao*, Johannes Zenn*, and Robert Bamler.

Trading Information between Latents in Hierarchical Variational
Autoencoders
ICLR 2023                                                                                                        2023
Tim Z. Xiao, and Robert Bamler.

Iterative Teaching by Data Hallucination
AISTATS 2023                                                                                                     2023
Zeju Qiu, Weiyang Liu, Tim Z. Xiao, Zhen Liu, Umang Bhatt, Yucen Luo, Adrian Weller, and Bernhard Schölkopf.

You Need Only Uncertain Answers: Data Efficient Multilingual Question
Answering
ICML 2020 Workshop on Uncertainty and Robustness in Deep Learning                                                2020
Zhihao Lyu, Danier Duolikun, Bowei Dai, Yuan Yao, Pasquale Minervini, Tim Z. Xiao and Yarin Gal.

Wat heb je gezegd? Detecting Out-of-Distribution Translations with
Variational Transformers
NeurIPS 2019 Workshop on Bayesian Deep Learning, Spotlight                                                        2019
Tim Z. Xiao, Aidan N. Gomez, and Yarin Gal.

A partial reconfiguration controller for Altera Stratix V FPGAs
In 26th International Conference on Field Programmable Logic and Applications (FPL)                               2016
Zhenzhong Xiao, Dirk Koch, and Mikel Lujan.


Honors & Awards
  2024      G-Research Grant Winner , For PhD students in quantitative disciplines                  Tübingen, Germany
  2023      Attended Machine Learning Summer School (MLSS), Fully founded                               Kraków, Poland
   2018     Attended UCLQ Quantum Technology Summer School, Fully founded by EPSRC                          London, UK
   2018     Certificate of Excellence, Awarded to top 10% of graduating students                        Manchester, UK
   2015     Golden Anniversary Prizes, For top five students in the first year studies                  Manchester, UK
            Grand prize winner, in OxHack (students hackathon), and the best use of Bloomberg API.
   2014                                                                                                     Oxford, UK
            (Built an iPhone App, in charge of backend design)




September 11, 2025                             Tim Zhenzhong Xiao    ○   Résumé                                    3
1     Abstract
Throughout history, computing has served
                                                     Sort
to automate human reasoning. However,
this has traditionally required humans to               Let me …

first formalize their informal goals and con-                       (a) Human Reasoning

cepts into rigid symbolic structures. This            Sort

act of formalization is not only the most                                 sort.py            0110…

di!cult part of problem-solving, but it also
                                                                   (b) Classical Computing
creates a major bottleneck, preventing com-
putational systems from directly utilizing             Sort

the vast stores of human knowledge that                                                     Let me …

are documented and shared in natural lan-                         (c) Verbalized Computing
guage. The advent of large language mod-
                                                     Figure 1: Three stages of problem-solving.
els (LLMs) is beginning to lift these limi-
tations, creating a new era where machines can reason directly in natural language.
     My research explores this emerging paradigm, which I term Verbalized Computing, where
natural language serves not just as an interface but as the central medium for learning, reasoning,
and problem-solving. My prior work in Verbalized Machine Learning (VML [1]) has demon-
strated that classical learning tasks can be re-expressed entirely in language space. However,
these initial algorithms are analogous to a single perceptron, the simplest form of a neural
network. As such, they are foundational but lack the compositional, multi-layered structure
required to tackle more complex reasoning tasks.
     This fellowship project aims to overcome these challenges by developing the foundational
algorithms and infrastructure for scalable verbalized reasoning. This research is structured
around three core, interconnected objectives, and the proposal provides a complete description
of the research plan and methodology:

    1. To design and implement “Verbalized Deep Learning” (VDL): I will develop a novel
       framework that extends VML to create deep, compositional reasoning models entirely
       within natural language. This will enable scalable learning and problem-solving, mirroring
       the pivotal transition from the perceptron to neural networks in classical machine learning.

    2. To build an open-source LLM orchestration framework: To power VDL and future
       verbalized systems, I will develop and open-source a high-concurrency framework de-
       signed to manage and coordinate large clusters of LLMs, enabling them to function as a
       single, powerful computational unit.

    3. To validate the VDL framework through a high-impact scientific application: I will
       apply the developed system to a critical challenge in natural science. One concrete
       application is to automate discovery of novel biochemical reaction pathways. This will
       serve as a powerful demonstration of how verbalized reasoning can be used to generate
       and evaluate complex scientific hypotheses.

By achieving these objectives, this project will not only produce a new class of powerful and
interpretable AI algorithms but also deliver a valuable open-source tool for the research com-
munity. The successful application in biochemistry will serve as a compelling demonstration of
how verbalized computing can accelerate scientific discovery, ultimately laying the groundwork
for a more intuitive and language-native future for machine reasoning.


                                                 2
2     Proposed Objectives and Potential Impact
2.1 Rationale and Vision
The recent advent of powerful large language models (LLMs) creates a unique opportunity to
move beyond the current heuristic-based “prompt engineering” and establish a rigorous new
discipline of verbalized computing. The history of computing is a story of scaling, and as
existing paradigms like Moore’s Law and LLM scaling laws approach their limits, this research
explores the next frontier: scaling computation by orchestrating large ensembles of LLMs.
We will validate this new paradigm by applying it to automated scientific discovery, an ideal,
language-native testbed where success promises to solve a major research bottleneck and deliver
significant real-world impact.

2.2    Objectives
    1. To design and implement Verbalized Deep Learning (VDL), a novel, scalable frame-
       work for complex reasoning in natural language.

         • Key Problem: Current language-based reasoning methods are either single-step
           or rely on brittle, manually-designed workflows for multi-step problems. The key
           problem is the absence of a framework that can learn a deep, compositional rea-
           soning process automatically, which is necessary for tackling complex, open-ended
           challenges.
         • Elements of the Problem:
             – Lack of a standardized, compositional “verbal layer” architecture.
             – Di!culty in maintaining, aggregating, and propagating a coherent information
               and context across multi-step processes.
             – Absence of a systematic method for error attribution and correction in a reason-
               ing graph.
         • Critical Element for Solution: To design a scalable iterative refinement loop that
           acts as a “verbal backpropagation”, using a natural language critique of the final
           output to recursively trace back the error and instruct each verbal layer to correct its
           reasoning. This mechanism is key to enabling learning in a deep verbalized system.

    2. To develop a high-concurrency framework for orchestrating distributed LLMs.

         • Key Problem: Executing algorithms that require hundreds of coordinated LLM
           interactions is practically suboptimal with general-purpose tools, which are not
           designed for the unique communication patterns and latency challenges of large-
           scale verbalized computing.
         • Elements of the Problem:
             – Managing complex computational graphs where one LLM’s output triggers
               dozens of parallel tasks whose results must then be integrated.
             – LLM API calls have highly variable and unpredictable response times, making
               synchronous programming models ine!cient and prone to bottlenecks.
             – A system is needed to track token usage for cost control, and handle rate-limiting
               and automatic retries gracefully across a distributed cluster of models.

                                                3
         • Critical Element for Solution: The critical element is the development of a declar-
           ative computational graph API. This will allow a researcher to define what they want
           to compute (the flow of reasoning between LLMs), while the framework’s underlying
           asynchronous, fault-tolerant execution engine handles how to do it e!ciently (man-
           aging API calls, scheduling tasks, and optimizing the workflow). This separation of
           concerns is key to making the system both powerful and easy to use.

   3. To validate the VDL framework by applying it to the automated discovery of novel
      biochemical reaction pathways.

         • Key Problem: A primary bottleneck in scientific progress is the lack of robust, auto-
           mated methods to synthesize knowledge from literature to generate novel, plausible,
           and testable hypotheses. Finding novel biochemical reaction pathways serves as a
           high-impact, representative example of this grand challenge in automated scientific
           discovery. This application will require close collaboration with domain experts to
           define constraints and validate results.
         • Elements of the Problem:
              – Grounding LLM reasoning in existing knowledge to prevent “hallucinations”.
              – Learning from known pathways to infer novel yet plausible ones.
              – Generating predictions that can be computationally pre-validated to prioritize
                the most promising candidates for expensive wet lab testing.
         • Critical Element for Solution: The critical element is the implementation of a
           Generator-Critic reasoning loop. The VDL model will act as the Generator, tasked
           with learning the principles from a database of known pathways to infer and propose
           novel reaction steps. A separate Critic model (e.g., with access to chemical databases
           and validation rules) will evaluate each proposed step for plausibility and scientific
           rigor. The Critic’s feedback, expressed in natural language, will guide the Generator’s
           refinement process (i.e., the VDL algorithm), ensuring the final output is a complete,
           novel, and scientifically sound hypothesis ready for wet lab validation.

2.3    Potential Impact
Academic and Practical Impact. The outcomes of this research will be a new class of scalable
reasoning algorithms (VDL) and a foundational open-source LLM orchestration framework. Its
significance lies in serving as a pioneer investigation into verbalized computing, a new paradigm
for scaling AI that is likely to be a major future direction for the field. By demonstrating how to
solve complex, real-world problems generally formulated in natural language, this work provides
both foundational tools (the framework) and a compelling proof-of-concept (the application in
biochemistry). This will accelerate research in AI and simultaneously demonstrate a path to
shortening R&D timelines in applied sciences like biotechnology.

Societal Value. The long-term significance of this work lies in its potential to accelerate
solutions to critical societal challenges. Faster discovery of new medicines or materials can
yield tangible improvements in public health and environmental sustainability. Beyond this, a
key value of this research is its promotion of a more transparent and human-aligned AI paradigm.
By developing systems that reason in comprehensible natural language, this work fosters greater
trust and collaboration between humans and machines, which is of immense societal importance.


                                                4
3    Background of Research, Research Plan and Methodology
3.1 Background of Research
This project is situated within the emerging paradigm of Verbalized Computing, which posits
that natural language can serve not merely as a user interface but as the central medium for learn-
ing, reasoning, and complex problem-solving. The recent advent of powerful large language
models (LLMs) has made it possible for machines to reason directly in natural language, begin-
ning to lift the long-standing constraint of mandatory formalization that has defined classical
computing. This research aims to move beyond the current heuristic-based “prompt engineering”
and establish a rigorous, new discipline grounded in this language-native modality.

Work Carried Out by the Nominee. My prior research has laid the conceptual [2] and algo-
rithmic groundwork [1, 3, 4] for this new paradigm. In particular, the work Verbalized Machine
Learning [1] introduces the VML framework, which re-expresses classical learning tasks by
constraining the model’s parameter space to be human-interpretable natural language. The core
of VML is a novel learner-optimizer architecture where both components are parameterized
by natural language and evaluated through LLMs (see Figure 2). The learner LLM acts as a
function approximator, using a natural language description (e.g., “the output is a linear function
of the input”) as its parameters (𝐿). The optimizer LLM is tasked with training the learner. In
each training step, the optimizer analyzes the learner’s predictions on a batch of data, provides
a natural language rationale for any errors, and generates an improved description of the data
pattern (𝐿 𝑀+1 ) for the next iteration. This iterative prompt refinement allows the model to learn
directly from data in a fully interpretable manner. A key finding from this work is VML’s
capacity for automatic model class selection. For instance, when tasked with fitting a quadratic
dataset, the VML optimizer first hypothesized a simple linear model. After observing poor
performance, it autonomously reasoned that the linear model “oversimplifies the relationship”
and updated the learner’s parameters to a more complex quadratic function, which significantly
improved accuracy. This result is a critical proof-of-concept, demonstrating that an LLM-based
system can move beyond simple parameter tuning to perform a more sophisticated form of
automated reasoning about model structure itself. This work successfully demonstrated that
core computational primitives can be verbalized. However, the results also highlighted a key
limitation: these initial algorithms are analogous to a single perceptron, the simplest form of
a neural network. They are foundational but lack the deep, compositional structure required to
tackle more complex reasoning tasks, which necessitates the work proposed herein.

Previous and Alternative Approaches and Their Deficiencies. The proposed research builds
upon three distinct but converging fields of study, each with significant accomplishments and
critical deficiencies that this project will address.

    • For Project 1 (VDL Algorithm): The current state-of-the-art in complex LLM reasoning
      is dominated by agentic workflows and multi-agent systems (MAS) [5, 6]. Agentic work-
      flows like ReAct [7] enable an LLM to interleave reasoning and action steps to interact
      with external tools and environments. To tackle greater complexity, MAS orchestrate
      multiple specialized agents that collaborate to solve a problem, often organized in central-
      ized, decentralized, or hybrid architectures [8]. To improve reliability, techniques such as
      Self-Reflection, Multi-Agent Reflection, and LLM Ensembles introduce feedback loops
      for error correction.


                                                5
         – Deficiency: A systematic analysis reveals a fundamental limitation across these
           approaches: the reasoning structures (i.e., the workflows, collaboration patterns,
           and reflection loops) are almost universally designed by a human and remain static
           during execution [9]. The system can learn to optimize the content that flows through
           this fixed structure, but it cannot learn to optimize the structure itself. This is the
           primary bottleneck preventing current agentic systems from achieving truly scalable,
           autonomous learning.

    • For Project 2 (Orchestration Framework): The development of complex LLM applica-
      tions has been accelerated by orchestration frameworks, most notably LangChain [10] and
      DSPy [11]. LangChain provides a versatile and comprehensive toolkit for rapidly proto-
      typing applications by connecting LLMs with data sources and tools. DSPy introduces
      a more declarative “programming, not prompting” paradigm, where developers define a
      program’s logic and an optimizer automatically tunes the underlying prompts for a given
      task, enhancing robustness.

         – Deficiency: While these frameworks provide powerful high-level programming
           abstractions, their underlying execution models are architecturally mismatched for
           the demands of massively parallel LLM computation. Their concurrency is based
           on general-purpose patterns (e.g., Python’s asyncio, thread pools) designed for I/O-
           bound tasks like serving independent web requests. This model is ine!cient for
           orchestrating a large, dynamic computational graph of hundreds of interdependent
           LLM calls with highly variable and unpredictable latencies, leading to bottlenecks
           and suboptimal performance.

    • For Project 3 (Scientific Application): The use of AI in science is rapidly evolving
      from using LLMs as tools for discrete tasks (e.g., literature summarization) to deploying
      them as autonomous “AI Scientists” capable of hypothesis generation, experiment design,
      and analysis [12]. Methodologies include literature-based discovery (LBD) [13] and
      automated validation frameworks like POPPER [14].

         – Deficiency: A critical challenge hindering progress is the inherent unreliability of
           LLMs, which are prone to hallucination and may simply memorize rather than reason
           from first principles. This creates a fundamental trilemma: an ideal automated
           scientist must generate hypotheses that are simultaneously novel, plausible, and
           testable. Current systems struggle to achieve all three. Systems that prioritize novelty
           often produce ungrounded, implausible ideas. Conversely, systems that prioritize
           plausibility by grounding in existing literature are often limited in novelty. Finally,
           many systems generate hypotheses that are not directly testable, as the generation
           and validation processes are decoupled.

Justification for New Project. The deficiencies identified above demonstrate a clear and
compelling need for this new project. The absence of a learnable reasoning algorithm, a
high-performance execution engine, and an integrated hypothesis generation-validation loop are
not independent problems; they are interconnected bottlenecks. Solving complex, language-
native problems like scientific discovery requires foundational progress on all three fronts
simultaneously. This project is designed to provide that holistic solution, creating a complete
and synergistic computational stack for the next generation of machine reasoning.



                                                6
3.2 Research Plan and Methodology
This research is structured around three core, interconnected objectives designed to establish
the foundations for scalable Verbalized Computing. A Gantt chat can be found in Figure 3.

3.2.1   Project 1: Design and Implement Verbalized Deep Learning (VDL)
This project aims to create a novel framework for deep, compositional reasoning algorithms
entirely within natural language, inspired by the development of classical deep learning.

Selected Approach and Uniqueness. The critical innovation is a mechanism I term “verbal
backpropagation”. In a traditional neural network, backpropagation attributes error from the final
output back through the network’s layers to adjust weights. Analogously, verbal backpropagation
will use a natural language critique of a final output to recursively trace the source of error back
through a multi-step verbal reasoning process. This feedback signal, also in natural language,
instructs each “verbal layer” (an LLM agent with a specific function) on how to correct its
reasoning. This mechanism is unique as it enables the system to learn not just the content at
each step, but the very function of each layer within the overall reasoning architecture. This
represents the pivotal transition from brittle, manually-designed workflows to a truly learnable
reasoning system. An illustration and how VDL relates to VML [1] can be found in Figure 3.

Plan of Investigation:
    Months 1-6: Formally define a standardized, compositional “verbal layer” architecture and
the computational principles of the verbal backpropagation algorithm.
    Months 7-15: Implement a prototype VDL system. This implementation will be closely
coupled with the initial development of the high-concurrency framework (Project 2), utilizing
its emerging API and execution engine to manage the complex LLM interactions required by
the VDL algorithm.
    Months 16-24: Conduct rigorous benchmarking of the VDL prototype against state-of-the-
art agentic frameworks on complex, multi-step reasoning tasks. Performance will be evaluated
based on accuracy, e!ciency, and the ability to learn and adapt to new problem structures.

Dissemination of Research Output. The foundational work on the Verbalized Deep Learning
algorithm will be submitted for publication at top-tier AI conferences such as NeurIPS, ICML.

3.2.2   Project 2: Build a High-Concurrency LLM Orchestration Framework
This project will develop the high-performance computational engine required to power VDL
and future large-scale verbalized computing systems.

Selected Approach and Uniqueness. The framework will be built on two core components: a
declarative computational graph API and a native asynchronous, fault-tolerant execution engine.
This architecture is unique because it is purpose-built for the computational pattern of massive
LLM ensembles, unlike existing tools adapted from web-serving paradigms. The declarative
API will allow a researcher to define the logical flow of reasoning between LLMs, while the
underlying engine will handle the complex and messy details of how to execute it e!ciently, e.g.,
managing thousands of API calls, scheduling tasks to minimize latency, optimizing workflows,
and handling errors like rate-limiting and API failures gracefully. This separation of concerns
is key to making the system both powerful and easy to use.

                                                 7
Plan of Investigation:
    Months 1-9: Design and implement the initial version of the declarative API and a core
execution engine. This initial development will be prioritized to directly support the implemen-
tation needs of the VDL prototype (Project 1), providing the necessary concurrency management
from an early stage.
    Months 10-21: Develop the full asynchronous, massively parallel execution engine, building
upon the initial prototype. This phase will focus on implementing sophisticated scheduling
algorithms optimized for the stochastic latency of LLM API calls.
    Months 22-30: Integrate essential production-grade features, including cost tracking, au-
tomatic retries, and caching. The framework will be released as an open-source project with
comprehensive documentation to foster community adoption.

Dissemination of Research Output. The high-concurrency framework will be released as
an open-source project on GitHub, complete with comprehensive documentation and tutori-
als to foster community adoption and contribution. The novel architecture and performance
benchmarks will also be submitted for publication at top-tier AI conferences.

3.2.3   Project 3: Automated Discovery of Novel Biochemical Reaction Pathways
This project will serve as a high-impact validation of the entire verbalized computing stack by
applying it to a grand challenge in automated scientific discovery.

Selected Approach and Uniqueness. The methodology centers on a Generator-Critic reason-
ing loop, which directly addresses the novelty-plausibility-testability trilemma. The Generator
(i.e., learner), powered by the VDL algorithm running on the new framework, will be tasked
with learning the underlying principles from a database of known pathways (e.g., KEGG ) to
infer and propose novel reaction steps. A separate Critic model (i.e., optimizer), with access
to chemical databases and expert-defined validation rules, will evaluate each proposed step for
scientific rigor and feasibility. The uniqueness of this approach lies in the feedback mechanism:
the Critic’s evaluation, expressed in natural language, will drive the VDL algorithm’s verbal
backpropagation. This iterative refinement process forces the Generator to revise its proposals
until they satisfy the Critic’s constraints, ensuring the final output is a complete, novel, and
scientifically sound hypothesis that is inherently testable.

Plan of Investigation:
    Months 6-12: Establish collaboration with domain experts in biochemistry to define the
scientific constraints, knowledge sources, and validation rules for the Critic model.
    Months 13-24: Implement the Generator-Critic loop, integrating the VDL algorithm
(Project 1) with the high-concurrency framework (Project 2).
    Months 25-36: Execute the full system to generate novel biochemical pathway hypotheses.
The results will be validated computationally and through review by domain experts to assess
their scientific merit and prioritize the most promising candidates for future experimental testing.

Dissemination of Research Output. The results from the application of the VDL framework
to automated biochemical pathway discovery will be written up for a high-impact journal in
the natural sciences, such as those in the Nature portfolio, to reach a broad scientific audience
beyond the AI community.



                                                 8
4        Illustrations

         Text prompt template for the learner                                                   Text prompt template for the optimizer

You are the model. You will use the descriptions below             You are the optimizer for a model, your goal is to learn the best descriptions for the model. The
to predict the output of the given input.                          model used the Current Pattern Descriptions below produced the outputs of the given inputs. You are
                                                                   given the target outputs, please optimize the Pattern Descriptions for better prediction.
** Pattern Descriptions: **
You are designed to do regression, i.e., to predict the            ** Inputs (a batch of i.i.d. data): **
output of any given input. Both input and output                   [[0.59] [1.55] [0.64] [1.43] [0.28] [0.02] [0.84] [0.39] [0.02] [1.28]]
are real numbers.                                                                                                                          A batch of data {x1, …, xn}
                             Model parameters                      ** Current Pattern Descriptions: **
** Input: **                                                       You are designed to do regression, i.e., to predict the output of any given input. Both input and
[0.59]                                                             output are real numbers.
                                     Data x                                                                                                   Model parameters
Please give your output strictly in the following format:          ** The model outputs: **
                                                                   [[0.59] [3.88] [1.28] [1.43] [0.53] [0.02] [1. ] [0.39] [1. ] [0. ]]
```                                                                                                                                          Prediction {y^1, …, y^n}
Explanations: [Your step-by-step analyses and results]             ** The target outputs: **
                                                                   [[5.84] [8.51] [5.92] [8.09] [4.98] [3.91] [6.46] [5.23] [3.88] [7.88]]
Output:                                                                                                                                    Ground Truth {y1, …, yn}
[Your output MUST be in REAL NUMBER
                                                                   If the model is doing well, you can keep using the current descriptions. However, if the model is
ROUNDED TO TWO DECIMAL POINTS; make
                                                                   not performing well, please optimize the model by improving the ' ew Pattern Descriptions'.
necessary assumptions if needed; it MUST be in the
                                                                   The model uses the ' ew Pattern Descriptions' should better predict the target outputs of the
same format as the Input]
                                                                   given inputs, as well as the next batch of i.i.d. input data from the same distribution. If previous
```
                                                                   'Optimization Step' are provided, you can use the information from your last optimization step
Please ONLY reply according to this format, don't give
                                                                   if it's helpful. Please think step by step and give your outputs strictly in the following format:
me any other words.
                                                                                                                                             Optimizer parameter
                                                                   ```                                      Verbalized loss function
                                           update                  Reasoning:
                                       model parameters            [be explicit and verbose, improve the Current Pattern Descriptions by yourself;]
Learner LLM fmodel
                                                                   New Pattern Descriptions:
                Iterative optimization                             [put your new descriptions here; MUST be specific and concrete;]
                                                                   ```
     provide                        Optimizer LLM fopt             Please ONLY reply according to this format, don't give me any other words.
inference results


                                       Figure 2: The learner and optimizer template for VML [1].



                                               Year 1         Year 2           Year 3
                   Activity
                                            Q1 Q2 Q3 Q4 Q1 Q2 Q3 Q4 Q1 Q2 Q3 Q4
Project 1: VDL Algorithm
Formal Definition & Design                                                                              VML                      x           fθ           y
Prototype Implementation (uses P2)
Benchmarking & Evaluation
Manuscript 1 Preparation
Project 2: Orchestration Framework
Initial API & Engine (for P1)
Full Engine Implementation
                                                                                                                                     fθ1,1        fθ2,1
Open-Source Release & Documentation

                                                                                                        VDL
                                                                                                                                 ⋯


                                                                                                                                                  ⋯




Manuscript 2 Preparation
Project 3: Scientific Application
                                                                                                                       x                                           y
Domain Expert Collaboration
Generator-Critic Implementation                                                                                                      fθ1,N        fθ2,N
System Validation & Analysis
Manuscript 3 Preparation


                           (a) Gantt chart for the research plan                                        (b) Illustration of the prior work VML and the proposed VDL


                                                  Figure 3: Gantt chart and VDL illustration.




                                                                                        9
5    Pathways to Impact Statement
This research will establish verbalized computing, a new AI paradigm where computing is
achieved in natural language. The project will deliver a foundational algorithm, an open-source
software framework, and a high-impact demonstration in automated scientific discovery, creating
tangible benefits for the technology sector, scientific community, and society at large.

5.1 Potential Beneficiaries
The benefits of this research will unfold over short, medium, and long-term horizons, reaching
a progressively broader audience.
    Short Term (1–2 years): The most immediate beneficiaries will be the global AI research
community and software developers. The VDL algorithm (Project 1) will introduce a new
paradigm for learnable, language-native reasoning, inspiring new lines of academic inquiry
into verbalized computing and more advanced algorithms. Simultaneously, the open-source
framework (Project 2) will provide a powerful, practical tool for building sophisticated AI
applications. Concurrently, biochemists and researchers in the pharmaceutical industry will
benefit from the initial methodologies and findings of Project 3, gaining new tools and insights
for automating hypothesis generation.
    Medium Term (3–5 years): The impact will expand to the technology industry, from
startups to major corporations. Companies will be able to leverage the VDL algorithm and
orchestration framework to create a new generation of AI products that are more intuitive,
powerful, and capable of complex reasoning. This will also directly benefit the biotechnology
and pharmaceutical industries, who can use the validated system to significantly accelerate their
research and development pipelines, shortening the time required to identify new drug targets or
design more e!cient industrial processes. The principles of the system will also be adaptable
to other scientific fields, such as materials science and environmental research.
    Long Term (over 5 years): Society as a whole will benefit from accelerated scientific
discovery, e.g., leading to faster development of new medicines (public health) and novel
enzymes for biofuels (environmental sustainability). By making the process of learning and
prediction transparent and human-readable, this work will also foster greater public trust and
inform better public policy.

5.2 Demonstrable Benefits Beyond Academia
The benefits will be concrete and measurable, providing advantages beyond the academic sphere.
    For the AI and technology sectors, benefits are measurable. The VDL algorithm’s impact
will be tracked via academic citations and follow-on research. The impact of the open-source
framework will be measured by tangible metrics such as the number of downloads from software
repositories like GitHub, the number of new projects that build upon it, and its adoption by
commercial companies in their products and services.
    For the biotechnology and pharmaceutical industries, the benefit is increased R&D e!-
ciency, measured by reduced discovery time and cost. The system’s success will be objectively
measured by the number of novel, scientifically plausible biochemical pathways it generates
that are subsequently validated by our expert collaborators and prioritized for further experi-
mental testing. In the longer term, the impact will be demonstrated by patents filed or products
developed based on discoveries initiated by the system.



                                               10
    For society, the benefit is improved health and quality of life. The project’s output of validated
scientific hypotheses is a demonstrable first step toward shortening discovery timelines, a metric
of immense value to public health and the global economy.

5.3 Strategy for Impact Realization
Following strategy will be employed to ensure the research translates into real-world impact.
    Open-Source Commitment: The core software framework (Project 2) will be released under
a permissive open-source license. It will be accompanied by comprehensive documentation,
tutorials, and example use cases to lower the barrier to adoption for both academic researchers
and industry practitioners.
    Strategic Dissemination: Research findings will be published in top-tier, high-impact
venues. The AI-focused work will be presented at leading conferences like NeurIPS to reach the
technology community, while the scientific discovery application will be submitted to journals
like Nature to ensure it reaches scientists in biology and chemistry.
    Direct Collaboration: Project 3 is built on a foundation of close collaboration with domain
experts in biochemistry. This ensures the system is designed to solve real-world problems and
that its outputs are rigorously validated and relevant to the field.
    Community Engagement: I will actively engage with potential beneficiaries by presenting
the research at industry workshops, academic seminars, and developer conferences. This will
build awareness, gather feedback, and foster a community of users who can contribute to and
benefit from the project.

5.4    Risk Mitigation Plan
We have identified four potential risks and have clear mitigation strategies for each.
    Risk: Technical Feasibility. The proposed ”verbal backpropagation” algorithm is novel and
may not perform as anticipated. Mitigation: My prior work on Verbalized Machine Learning
(VML [1]) provides a strong proof-of-concept for the core ideas. The research plan is structured
iteratively, starting with small-scale prototypes and rigorous benchmarking. This allows for
continuous refinement and adjustment, ensuring the final algorithm is robust and e”ective.
    Risk: Low Adoption of the Open-Source Framework. The software might be perceived
as too complex or niche, limiting its uptake. Mitigation: The framework’s development is
directly tied to the needs of the VDL algorithm and the biochemistry application, ensuring it is
purpose-built for a high-impact use case. This, combined with a strong focus on high-quality
documentation and community engagement, will drive adoption.
    Risk: Insu!cient Scientific Novelty or Validity. The hypotheses generated by the system
could be trivial, incorrect, or untestable, failing to provide real value. Mitigation: This is the
most critical risk for the scientific application and is addressed directly by the Generator-Critic
design. The Critic model, armed with chemical databases and expert-defined rules, acts as
an automated scientific peer reviewer, ensuring every proposed step is plausible and grounded
in established knowledge. Furthermore, our collaboration with domain experts provides the
ultimate check on the novelty and validity of the final results.
    Risk: Ethical Concerns and Misuse. An “automated scientist” could be misused or create
societal apprehension. Mitigation: This project is framed as a human-AI collaboration, creating
a tool that augments and accelerates human ingenuity, not replaces it. The system’s language-
based computing medium makes it transparent and auditable, a key safeguard. All outputs will
include discussions on ethical considerations and responsible use.


                                                 11
References
 [1] Tim Z. Xiao, Robert Bamler, Bernhard Schölkopf, and Weiyang Liu. Verbalized machine
     learning: Revisiting machine learning with language models. In Transactions on Machine
     Learning Research (TMLR), 2025.
 [2] Tim Z. Xiao, Weiyang Liu, and Robert Bamler. Large language models are zero-shot
     problem solvers—just like modern computers. Harvard Data Science Review, 2025.
 [3] Zhouliang Yu, Yuhuan Yuan, Tim Z. Xiao, Frank Xia, Jie Fu, Ge Zhang, Ge Lin, and
     Weiyang Liu. Generating symbolic world models via test-time scaling of large language
     models. In Transactions on Machine Learning Research (TMLR), 2025.
 [4] Tim Z. Xiao, Johannes Zenn, Zhen Liu, Weiyang Liu, Robert Bamler, and Bernhard
     Schölkopf. Flipping against all odds: Reducing llm coin flip bias via verbalized rejection
     sampling. arXiv preprint arXiv:2506.09998, 2025.
 [5] Xinzhe Li. A survey on LLM test-time compute via search: Tasks, LLM profiling, search
     algorithms, and relevant frameworks. Transactions on Machine Learning Research, 2025.
     ISSN 2835-8856. URL https://openreview.net/forum?id=x9VQFjtOPS.
 [6] Junyu Luo, Weizhi Zhang, Ye Yuan, Yusheng Zhao, Junwei Yang, Yiyang Gu, Bohan Wu,
     Binqi Chen, Ziyue Qiao, Qingqing Long, et al. Large language model agent: A survey on
     methodology, applications and challenges. arXiv preprint arXiv:2503.21460, 2025.
 [7] Shunyu Yao, Je!rey Zhao, Dian Yu, Nan Du, Izhak Shafran, Karthik Narasimhan, and
     Yuan Cao. React: Synergizing reasoning and acting in language models. In International
     Conference on Learning Representations (ICLR), 2023.
 [8] Khanh-Tung Tran, Dung Dao, Minh-Duong Nguyen, Quoc-Viet Pham, Barry O’Sullivan,
     and Hoang D Nguyen. Multi-agent collaboration mechanisms: A survey of llms. arXiv
     preprint arXiv:2501.06322, 2025.
 [9] Huan-ang Gao, Jiayi Geng, Wenyue Hua, Mengkang Hu, Xinzhe Juan, Hongzhang Liu,
     Shilong Liu, Jiahao Qiu, Xuan Qi, Yiran Wu, et al. A survey of self-evolving agents: On
     path to artificial super intelligence. arXiv preprint arXiv:2507.21046, 2025.
[10] Oguzhan Topsakal and Tahir Cetin Akinci. Creating large language model applications
     utilizing langchain: A primer on developing llm apps fast. In International conference on
     applied engineering and natural sciences, volume 1, pages 1050–1056, 2023.
[11] Omar Khattab, Arnav Singhvi, Paridhi Maheshwari, Zhiyuan Zhang, Keshav Santhanam,
     Sri Vardhamanan, Saiful Haq, Ashutosh Sharma, Thomas T Joshi, Hanna Moazam, et al.
     Dspy: Compiling declarative language model calls into self-improving pipelines. arXiv
     preprint arXiv:2310.03714, 2023.
[12] Tianshi Zheng, Zheye Deng, Hong Ting Tsang, Weiqi Wang, Jiaxin Bai, Zihao Wang, and
     Yangqiu Song. From automation to autonomy: A survey on large language models in
     scientific discovery. arXiv preprint arXiv:2505.13259, 2025.
[13] Qingyun Wang, Doug Downey, Heng Ji, and Tom Hope. Scimon: Scientific inspiration
     machines optimized for novelty. In Proceedings of the 62nd Annual Meeting of the Asso-
     ciation for Computational Linguistics (Volume 1: Long Papers), pages 279–299, 2024.

                                              12
[14] Kexin Huang, Ying Jin, Ryan Li, Michael Y Li, Emmanuel Candès, and Jure Leskovec.
     Automated hypothesis validation with agentic sequential falsifications. arXiv preprint
     arXiv:2502.09858, 2025.

[15] Zeju Qiu, Weiyang Liu, Haiwen Feng, Zhen Liu, Tim Z. Xiao, Katherine M. Collins,
     Joshua B. Tenenbaum, Adrian Weller, Michael J. Black, and Bernhard Schölkopf. Can large
     language models understand symbolic graphics programs? In International Conference
     on Learning Representations (ICLR), 2025.

[16] Zeju Qiu, Simon Buchholz, Tim Z. Xiao, Maximilian Dax, Bernhard Schölkopf, and
     Weiyang Liu. Reparameterized llm training via orthogonal equivalence transformation.
     arXiv preprint arXiv:2506.08001, 2025.

[17] Zhen Liu, Tim Z. Xiao, Weiyang Liu, Yoshua Bengio, and Dinghuai Zhang. Fast diversity-
     preserving reward finetuning of di!usion models via nabla-gflownets. In International
     Conference on Learning Representations (ICLR), 2025.

[18] Zijing Ou, Mingtian Zhang, Andi Zhang, Tim Z. Xiao, Yingzhen Li, and David Barber. Im-
     proving probabilistic di!usion models with optimal covariance matching. In International
     Conference on Learning Representations (ICLR), 2025.

[19] Tim Z. Xiao and Robert Bamler. Trading information between latents in hierarchical vari-
     ational autoencoders. In International Conference on Learning Representations (ICLR),
     2023.

[20] Tim Z. Xiao, Johannes Zenn, and Robert Bamler. A note on generalization in variational
     autoencoders: How e!ective is synthetic data and overparameterization? In Transactions
     on Machine Learning Research (TMLR), 2024.

[21] Tim Z. Xiao, Johannes Zenn, and Robert Bamler. The svhn dataset is deceptive for
     probabilistic generative models due to a distribution mismatch. NeurIPS 2023 Workshop
     on Distribution Shifts, 2023.

[22] Tim Z. Xiao, Weiyang Liu, and Robert Bamler. A compact representation for bayesian
     neural networks by removing permutation symmetry. NeurIPS 2023 Workshop on Unifying
     Representations in Neural Models, 2023.

[23] Andi Zhang, Tim Z. Xiao, Weiyang Liu, Robert Bamler, and Damon Wischik. Your
     finetuned large language model is already a powerful out-of-distribution detector. In
     International Conference on Artificial Intelligence and Statistics (AISTATS), 2025.

[24] Tim Z. Xiao, Aidan N. Gomez, and Yarin Gal. Wat zei je? detecting out-of-distribution
     translations with variational transformers. NeurIPS 2019 Workshop on Bayesian Deep
     Learning, 2020.

[25] Zhihao Lyu, Danier Duolikun, Bowei Dai, Yuan Yao, Pasquale Minervini, Tim Z. Xiao,
     and Yarin Gal. You need only uncertain answers: Data e”cient multilingual question
     answering. ICML 2020 Workshop on Uncertainty and Robustness in Deep Learning,
     2020.




                                             13
Past Research Experience
My recent research advances two directions that together lay the foundation for verbalized
computing. The first focuses on developing systems where informal reasoning, expressed in
natural language, can be treated as a form of computation. The second develops probabilistic
methods for representing and reasoning under uncertainty. Although my probabilistic work was
not developed specifically for verbalized systems, it addresses the same challenges that arise
once computation moves beyond formal logic. Informal reasoning is uncertain and context
dependent, so scalable language based systems must be able to model and act on uncertainty.

1. Informal Reasoning: LLM-Powered Computation
Conceptual Foundation. Our recent paper [2] argues that large language models (LLMs)
have followed the same path as classical computers, evolving from task specific tools to general
problem solvers. In modern computers, programs and data share the same representation space,
enabling zero-shot execution of arbitrary code. In LLMs, prompts and answers likewise live
in the same token space, in a manner reminiscent of the von Neumann architecture, enabling
zero-shot problem solving directly from natural language. This motivates treating prompts as
programs and language as a medium of computation, which is the basis for verbalized computing.

Algorithms. Building on this perspective, I have explored a family of algorithms that operate
entirely in natural language space. Verbalized Machine Learning (VML [1]) re-expresses
classical learning tasks, such as regression and classification, by parameterizing the learner
and the optimizer in language space rather than in numerical parameter space, and updating
them through iterative prompt refinement. For settings where optimization is performed on a
single instance at test time, we introduced instance VML (iVML [3]), which applies the same
principle to instance-specific optimization: starting from an initial solution, the model iteratively
refines symbolic world models (in PDDL) to improve performance under a task-specific loss,
e!ectively performing test-time scaling. We also developed Verbalized Rejection Sampling
(VRS [4]), which adapts classical rejection sampling into a language-based procedure, using
natural language reasoning to accept or reject candidate samples and thereby reduce bias in
generating from natural-language-parameterized distributions. Together, these algorithms show
that both deterministic and probabilistic computation can be verbalized, enabling learning,
instance optimization, and sampling to be carried out entirely in natural language using LLMs.

Capability. I have also examined how verbalized computation can bridge between informal
and formal representations. In one study, we evaluated whether LLMs can understand and
reason about symbolic graphics programs without visual input, a task that requires parsing
formal procedural descriptions and inferring their semantic content [15]. We found that LLMs
can interpret such programs and answer semantic questions about their outputs, suggesting
that prompt-based computation can integrate informal instructions with formal programs. This
capability is essential for verbalized systems that must operate across the boundary between
natural language reasoning and precise, symbolic programs.

Infrastructure. Verbalized computing relies on powerful and reliable LLM engines, much as
modern computing relies on fast and stable CPUs. As part of my broader interest in building
this engine layer, we have explored training methods informed by network architecture and
optimization dynamics. One example is POET [16], which reparameterizes each weight matrix
with two learnable orthogonal matrices around a fixed random initialization, preserving key
spectral properties during training. This design improves stability and generalization, and we
introduced scalable approximations that make it practical for large models. It also illustrates how
deep learning insights and parameter-e”cient training strategies can contribute to creating more
capable, robust models that can serve as the computational backbone for verbalized systems.

2. Probabilistic Methods: Toolkits for Reasoning Under Uncertainty
While my work above focuses on algorithms, capabilities, and infrastructure for verbalized
computing, my second line of research develops probabilistic methods that address a fundamental
challenge in informal reasoning: uncertainty. Informal reasoning involves ambiguous goals,
incomplete information, and shifting context, so scalable language-based systems must be able to
model, diagnose, and act on uncertainty. Although these methods were not originally developed
for verbalized systems, many of their principles and tools transfer directly to this setting.

Modeling. I have worked on advancing probabilistic generative models to improve fidelity,
diversity, and controllability. We introduced Nabla-GFlowNet [17], a reward-finetuning method
for di!usion models that uses gradient-informed detailed balance objectives to incorporate
reward gradients, achieving fast alignment while preserving diversity and the pretrained prior.
In a separate work, we proposed OCM [18], an unbiased objective that learns the optimal
diagonal covariance from the model’s score, which improves sampling e”ciency and likelihood
across di!erent models without incurring prohibitive Hessian costs. Earlier, I also worked on
hierarchical variational autoencoders and studied how information should be allocated across
latent layers to balance expressivity and reconstruction fidelity [19]. These advances focus on
improving how models represent and sample from complex distributions.

Understanding. Robust reasoning also demands insight into where uncertainty originates. In
one project, we examined how synthetic data and overparameterization can mitigate overfitting
and improve robustness in variational autoencoders [20]. We also uncovered a hidden train–test
distribution mismatch in the widely used SVHN benchmark and proposed a corrected split,
highlighting the importance of dataset diagnostics for probabilistic evaluation [21]. Finally, we
used rebasin techniques to remove permutation symmetry in Bayesian neural networks, enabling
a compact representation that unifies sampling-based and parametric-based inference methods
[22]. Together, these contributions improve our understanding of where uncertainty arises and
provide tools for managing it in large probabilistic models.

Acting. E!ective reasoning systems must not only represent and understand uncertainty but
also act on it. We developed a simple likelihood–ratio criterion between a pretrained language
model and its finetuned variant, which serves as a strong signal for out-of-distribution detection
in question-answering and safety-critical settings [23]. Earlier, we introduced a variational
Transformer framework that computes a sentence-level uncertainty metric for neural machine
translation, allowing the system to flag unseen language pairs and defer when confidence is low
[24]. Building on this, I advised a project that developed an active-learning pipeline which uses
the model’s uncertainty to select the most uncertain questions for labeling in multilingual QA,
reducing annotation cost while maintaining accuracy [25]. These studies show how uncertainty
estimates can be turned into actionable safeguards and data-e”cient learning strategies.
Leadership Experience
My leadership experience has been developed through both direct mentorship of junior re-
searchers and proactive initiatives to build and sustain our scientific community.

Mentorship and Research Guidance
I have actively sought opportunities to guide and develop emerging researchers. I have formally
advised two Master’s students through the entire research lifecycle, from initial ideation and
experiment design to the final stages of writing and submission. My approach to mentorship
focuses on fostering independent critical thinking while providing the necessary structure and
support to ensure project success. This guidance led to one of these projects being accepted
as a publication [25] at a workshop at a top-tier international machine learning conference, a
significant achievement for a Master’s-level research project.

Community Building and Initiative
Beyond my own research, I am committed to fostering a vibrant and collaborative scientific
environment. I identified a critical gap in our local research hub when a prominent bi-weekly
seminar series, essential for connecting hundreds of AI doctoral students, ceased to operate
after its previous organizers departed. Recognizing the immense value of this platform for
communication and the dissemination of ideas, I took the initiative to resurrect it.
     I proactively recruited two colleagues and, as a team, we re-established the seminar from
the ground up1 . My leadership role involved designing and implementing new operational
procedures for the entire series. This included identifying and inviting speakers, managing
all talk logistics, and actively creating opportunities for local doctoral students to present and
receive feedback on their work.
     To date, our team has successfully organized and hosted 22 talks. To maximize the reach and
impact of this initiative beyond our local community, I also established a workflow for recording,
editing, and publishing the talks on YouTube2 , creating a lasting and globally accessible edu-
cational resource. This e!ort has been instrumental in rebuilding a core pillar of our academic
community, demonstrating my ability to identify a need, build a team, and execute a plan to
create a self-sustaining and valuable initiative.




  1 Friday Talks @ Tübingen: https://fridaytalks.github.io
  2 https://www.youtube.com/@FridayTalksTuebingen



                                               16
                                                RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                 Academic Referee’s Report Form
                                      (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/
     fax (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover
     page) to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements
      and research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                       1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A Nominee’s Particulars              (to be completed by the nominee)
Name of Nominee :       (Surname) Xiao
                        (Other name) Zhenzhong
Host Faculty / School / Department / Faculty of Engineering,
Division / Unit ^ :                  Department of Computer Science & Engineering
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the
    Selection Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

  Early 2021: Mr. Zhenzhong Xiao applied for a PhD position in my research group with an excellent talk
   and interview.
  Since September 2021 until now: I have been Mr. Xiao’s PhD advisor. I’ve worked very closely with
   him since then, with usually at least one meeting per week (and tighter collaboration ahead of paper
   deadlines), except for the time from April 2024 to February 2025, during which Mr. Xiao was doing a
   research internship at the Max Planck Institute for Intelligent Systems (I still checked in with him
   regularly during this time, just less frequently).




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                  Excellent      Very Good        Good                  Average          No basis
                                  (top 5%)       (top 6-20%) (top 21-50%)              or below            for
                                                                                      (lower than       judgment
                                                                                         50%)
Intellectual capacity                 ☒                 ☐                 ☐                ☐                ☐
Ability for conducting
scholastic research                   ☒                ☐                  ☐                ☐                ☐
Knowledge of proposed
research study                        ☒                ☐                  ☐                ☐                ☐
Analytical power and
reasoning                             ☒                ☐                  ☐                ☐                ☐
Judgment                              ☐                ☒                  ☐                ☐                ☐
Imagination and
originality                           ☒                ☐                  ☐                ☐                ☐
Motivation and
perseverance                          ☒                ☐                  ☐                ☐                ☐
Skills of writing and
argumentation                         ☒                ☐                  ☐                ☐                ☐
Capacity for independent
work                                  ☒                ☐                  ☐                ☐                ☐
Reliability and sense of
responsibility                        ☒                ☐                  ☐                ☐                ☐
Leadership experience                 ☐                 ☐                 ☒                 ☐               ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

For space reasons, please see separate letter attached at the end of this document.




                                                            3
                                                                      JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?


              ☒      Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: ROBERT BAMLER                                 Title: Prof Dr
                        (in BLOCK letters)                                (Prof / Dr / Mr)*

 University / Organisation: University of Tuebingen
 Position: Assistant Professor
 E-mail Address: <EMAIL>
                     #
 Telephone Number : +49-162-6085858
(# Please provide country code and area code, e.g. +86-10-1234567)
* Please delete as appropriate


Declaration by the Referee:
I hereby declare that
☒ I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
☒ I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                 Date: 30 August 2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                     4
                                                              JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                                                         Faculty of Science



                                                                                         Department of Computer Science
                                                                                         Cluster of Excellence:
                                                                                         Machine Learning
University of Tübingen - Maria-von-Linden-Str. 6 - 72076 Tübingen - Germany

                                                                                         Prof. Dr. Robert Bamler
                                                                                         Group Leader
                                                                                         Data Science and Machine Learning
To the members of the
JRFS Selection Panels                                                                    University of Tübingen
                                                                                         AI Research Building
                                                                                         Room No. 40-31/A4
                                                                                         Maria-von-Linden-Straße 6
                                                                                         72076 Tübingen

                                                                                         Telephone +49 ************
                                                                                         <EMAIL>
                                                                                         https://robamler.github.io



                                                                              Tübingen, Germany, 30 August 2025


Research ability and potential of the nominee Zhenzhong Xiao (Item 3 of Part B of the referee’s report)


Dear members of the JRFS Selection Panels,

My name is Robert Bamler and I am an assistant professor of data science and machine learning at the
University of Tübingen, Germany. I have had the pleasure of advising Mr. Zhenzhong Xiao in his PhD program
since the fall of 2021, and I recommend him very strongly for the Junior Research Fellow Scheme.
Mr. Xiao is a brilliant researcher and a true source of inspiration due to his original ideas, deep knowledge of
the literature, perseverance, productivity, and communication skills. What I find most impressive, however—
especially considering his early career stage—is that Mr. Xiao has a true vision for his research, and for how
it can can shape his research area in the long term. Mr. Xiao’s research is in a field that has seen rapid
growth in the past few years: reasoning with natural-language-based artificial intelligence (AI) systems.
Despite the somewhat chaotic research landscape due to the fast pace of this field, Mr. Xiao has managed to
establish his own vision for an original and sustainable line of research that achieves the balancing act
between promising a truly novel approach to current problems in AI reasoning, while still being well-
motivated and grounded in both the history and the current developments in AI reasoning and computer
science. Mr. Xiao has developed this vision autonomously over the past years but was always open to my
feedback.
While Mr. Xiao’s long-term vision is ambitious, he has already proven to me repeatedly that he can derive
realistic and original concrete research projects from it and successfully push them to publication with great
perseverance. Aside from his excellent reliability and work ethics, another strength of Mr. Xiao is his intimate
knowledge with the literature. Mr. Xiao knows both the research in his particular field in great detail, and he
has a great overview of current developments in the wider area and an excellent sense of where the wider
research community seems to be heading and where possible missed opportunities lie. Due to his wide
knowledge of the field, Mr. Xiao has been instrumental in co-supervising two bachelor and one master’s
students in my group on their respective thesis projects. And even in situations where Mr. Xiao is not the
official co-supervisor, he is often my go-to person to point me to crucial literature.
When it comes to writing papers, I have noticed a remarkable growth in Mr. Xiao’s skills regarding both
project ideation and paper writing over the course of his PhD. At the beginning of his PhD, some of his many
original ideas were difficult to publish at AI venues because some of his early findings, while insightful, often
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
⚫    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
⚫    The nominee should complete Part A of the referee’s report.
⚫    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
⚫    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
⚫    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A     Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Xiao
                        (Other name) Zhenzhong
Host Faculty / School / Department / Faculty of Engineering,
Division / Unit ^ :                  Department of Computer Science & Engineering
^ Please do not mention the name of the supporting university


Part B     Academic Referee’s Report           (to be completed by the referee)
Notes to Referee:
⚫   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
⚫   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
⚫   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
⚫   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
⚫   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
⚫   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
⚫   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?
I have known Mr. Xiao for over three years, during which time we were colleagues at the Max Planck Institute
for Intelligent Systems. We have collaborated closely on five research projects (three published and two under
review) so far, working together at various levels of depth, including idea development, code implementation,
experimental execution, and manuscript writing. These collaborations have allowed me to gain a clear
understanding of his research skills and professional qualities.




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent   Very Good       Good            Average          No basis
                                (top 5%)    (top 6-20%) (top 21-50%)       or below            for
                                                                          (lower than       judgment
                                                                             50%)
Intellectual capacity                         ☐              ☐                 ☐               ☐
Ability for conducting
scholastic research
                                               ☐             ☐                 ☐               ☐
Knowledge of proposed
research study
                                               ☐             ☐                 ☐               ☐
Analytical power and
reasoning
                                               ☐             ☐                 ☐               ☐
Judgment                                       ☐             ☐                 ☐               ☐
Imagination and
originality
                                               ☐             ☐                 ☐               ☐
Motivation and
perseverance
                                               ☐             ☐                 ☐               ☐
Skills of writing and
argumentation
                                               ☐             ☐                 ☐               ☐
Capacity for independent
work
                                               ☐             ☐                 ☐               ☐
Reliability and sense of
responsibility
                                               ☐             ☐                 ☐               ☐
Leadership experience                         ☐             ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
Please see the separate sheets attached.




                                                    3
                                                          JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?


                  ☐
                  ✓      Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: ZHEN LIU                                       Title: Prof
                             (in BLOCK letters)
     University / Organisation: The Chinese University of Hong Kong, Shenzhen
     Position: Assistant Professor
     E-mail Address: <EMAIL>
     Telephone Number#: +86-13823295558
     (# Please provide country code and area code, e.g. +86-10-1234567)
⚫     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
     I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date: 31/08, 2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
addressed such novel problems that quantitative comparisons to the literature along established evaluation
metrics were difficult, which tends to put off some more benchmark-driven reviewers in our field. It has
given me great joy to see how quickly Mr. Xiao has learned from this experience, and whenever he presents
a new project idea to me now, he thinks about ways to quantify his findings from the start. I have seen this
openness and ability to quickly learn from feedback also in Mr. Xiao’s writing skills, as some initial difficulties
with subtleties of the English grammar are now completely gone, and in the last one to two years, even the
first drafts of his papers were examples of exceptionally clear writing. Having seen how quickly Mr. Xiao can
learn to fit into a new role, I have no doubt that he will also quickly rise to new challenges such as acquiring
more funding and managing his own research group.
Finally, I would like to highlight Mr. Xiao’s impressive communication skills. I know that it is a bit of a cliché
to say this in a letter of recommendation, but Mr. Xiao is truly relationship oriented. This can be seen not
only in the many collaborations for his publications, both within our research institute and beyond, which he
has all established on his own. Moreover, Mr. Xiao has identified a structural lack of inter-group
communication among PhD students in our AI research institute. His reaction to this issue was that he set up
and continues to organize a bi-weekly seminar that now seems to be the main hub at which scientific idea
exchange between PhD students from different groups at our institute is initiated.
I am convinced that Mr. Xiao has a bright academic future ahead of him. He has a clear vision for a highly
relevant research agenda, and the scientific expertise, perseverance, academic connections, and
communication skills to pull it off. I strongly encourage you to look favorably upon his application.
Sincerely,

Robert Bamler
