                                       <PERSON><PERSON><PERSON><PERSON> (王子啸)
                           +86-188-1201-1897      |   <EMAIL>     |   shiningsord.github.io
                                                a ShiningSord   |   ŵ Google Scholar
                                                       Hong Kong SAR, China

E DUCATION
•   The Chinese University of Hong Kong                                                                     Aug 2022 – Present
    Ph.D. in Computer Science and Engineering                                                              Hong Kong, China
    ◦   Advisor: Prof. <PERSON><PERSON> & Prof. <PERSON><PERSON>
•   Tsinghua University                                                                                    Sep 2019 – Jun 2022
    M.Sc. in Computer Science and Technology                                                                    Beijing, China
•   Tsinghua University                                                                                    Sep 2015 – Jun 2019
    B.Eng. in Automation                                                                                        Beijing, China
•   Tsinghua University                                                                                    Sep 2016 – Jun 2019
    B.A. in Economics (Second Major)                                                                            Beijing, China



E XPERIENCE
•   Huawei Noah’s Ark Lab                                                                                 Aug 2024 – May 2025
    Research Intern                                                                                        Hong Kong, China
•   SenseTime Research                                                                                    Apr 2023 – Dec 2023
    Research Intern                                                                                        Hong Kong, China
•   Tencent AI Lab                                                                                        Oct 2020 – Aug 2022
    Research Intern                                                                                          Shenzhen, China
•   UBTECH Robotics                                                                                       Sep 2017 – Aug 2018
    Research Intern                                                                                             Beijing, China



S ELECTED P UBLICATIONS
[C10] <PERSON>ng <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
      “DCLOG: Don’t Cares-based Logic Optimization using Pre-training Graph Neural Networks”, ASPDAC 2026.
[C9]       <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON><PERSON>* , <PERSON><PERSON><PERSON>, <PERSON><PERSON>, “Video-based Visible-Event Cross-modal Person
           Re-identiﬁcation for Edge AI Surveillance Systems”, ASPDAC 2026.
[C8]       Ziyang Yu, Peng Xu, Zixiao Wang, Binwu Zhu, Qipan Wang, Yibo Lin, Runsheng Wang, Bei Yu, Martin Wong,
           “SDM-PEB: Spatial-Depthwise Mamba for Enhanced Post-Exposure Bake Simulation”, DAC 2025. [paper]
           [poster] [slides]
[C7]       Zixiao Wang, Junwu Weng, Mengyuan Liu, Bei Yu, “FlexPose: Pose Distribution Adaptation with Limited
           Guidance”, AAAI 2025. [paper] [poster]
[C6]       Zixiao Wang, Jieya Zhou, Su Zheng, Shuo Yin, Kaichao Liang, Shoubo Hu, Xiao Chen, Bei Yu, “TorchResist:
           Open-source Differentiable Resist Simulator”, SPIE 2025. [paper] [poster] [code]
[C5]       Zixiao Wang* , Yunheng Shen* , Xufeng Yao, Wenqian Zhao, Yang Bai, Farzan Farnia, Bei Yu, “ChatPattern:
           Layout Pattern Customization via Natural Language”, DAC 2024. [paper] [poster] [slides]
[C4]       Yang Bai, Wenqian Zhao, Shuo Yin, Zixiao Wang, Bei Yu, “ATFormer: A Learned Performance Model with
           Transfer Learning Across Devices for Large-Scale Transformers”, EMNLP 2023. [paper] [poster] [slides]
[C3]       Zixiao Wang* , Yunheng Shen* , Wenqian Zhao, Yang Bai, Guojin Chen, Farzan Farnia, Bei Yu, “DiffPattern:
           Layout Pattern Generation via Discrete Diffusion”, DAC 2023. [paper] [poster] [slides]
[C2]       Zixiao Wang, Junwu Weng, Chun Yuan, Jue Wang, “Truncate-Split-Contrast: A Framework for Learning from
           Mislabeled Videos”, AAAI 2023 (Oral). [paper] [poster] [slides] [code]
[C1]       Haijin Ding, Zixiao Wang, Rebing Wu, “Enhancing the Security of Multi-Agent Networked Control Systems
           Using QKD Based Homomorphic Encryption”, CDC 2018. [paper]
[J5]       Zixiao Wang, Wenqian Zhao, Yunheng Shen, Yang Bai, Guojin Chen, Farzan Farnia, Bei Yu, “DiffPattern-Flex:
           Efﬁcient Layout Pattern Generation via Discrete Diffusion”, IEEE TCAD, 2025. [paper]
[J4]      Wenqian Zhao, Lancheng Zou, Zixiao Wang, Xufeng Yao, Bei Yu, “HAPE: Hardware-Aware LLM Pruning For
          Efﬁcient On-Device Inference Optimization”, ACM TODAES, vol. 30, no. 04, pp. 61:1–61:18, 2025. (Editor’s
          Pick). [paper]
[J3]      Wenqian Zhao, Shuo Yin, Chen Bai, Zixiao Wang, Bei Yu, “BAQE: Backend-Adaptive DNN Deployment via
          Synchronous Bayesian Quantization and Hardware Conﬁguration Exploration”, IEEE TCAD, 2025. [paper]
[J2]      Guojin Chen, Zixiao Wang, Bei Yu, David Z. Pan, Martin D. F. Wong, “Ultra-Fast Source Mask Optimization via
          Conditional Discrete Diffusion”, IEEE TCAD, 2024. [paper]
[J1]      Yang Bai, Xufeng Yao, Qi Sun, Wenqian Zhao, Shixin Chen, Zixiao Wang, Bei Yu, “GTCO: Graph and Tensor
          Co-Design for Transformer-based Image Recognition on Tensor Cores”, IEEE TCAD, 2024. [paper]
[S1]      Zixiao Wang, et al., “On the Distributed Evaluation of Generative Models”, arXiv preprint.
[S2]      Zixiao Wang, et al., “MoreauPruner: Robust Pruning of Large Language Models Against Weight
          Perturbations”, arXiv preprint. [code]

S OFTWARE
•   TorchResist                                                                                                    2025
    Differentiable resist simulator built with PyTorch. Open-sourced for the lithography and EDA community.


H ONORS AND AWARDS
•   Full Postgraduate Studentship                                                                             2022–2026
    CUHK
•   Outstanding Graduate of CSE                                                                                    2022
    Tsinghua University
•   Qi Hang Scholarship                                                                                            2019
    Tsinghua University
•   Academician Ni Weidou Scholarship                                                                              2018
    Tsinghua University
•   2nd Place Award in Technical Challenge (Team Vulcan)                                                           2018
    RoboCup
•   3rd Place Award in 1v1 Contest (Team Vulcan)                                                                   2018
    RoboCup
•   Zheng Geru Scholarship                                                                                         2017
    Tsinghua University
•   Comprehensive Scholarship                                                                                 2016, 2020
    Tsinghua University


T EACHING E XPERIENCE
•   CSCI3320                                                                                                    2023-R2
    Fundamentals of Machine Learning, CUHK
•   ENGG2760A                                                                                                   2023-R1
    Probability for Engineers, CUHK
•   CSCI5030                                                                                                    2022-R2
    Machine Learning Theory, CUHK
•   ENGG2020                                                                                                    2022-R1
    Digital Logic and Systems, CUHK
Abstract of Research
Recent advances in generative artificial intelligence (AI) have demonstrated transformative po-
tential in fields such as natural language processing and image/video generation. However,
their direct application to Design-for-Manufacturing (DFM) in the semiconductor industry
remains largely unexplored and highly challenging. Unlike conventional domains, DFM in-
volves a tightly coupled and long simulation flow that spans multiple critical stages, including
photolithography, etching, and mask optimization. This end-to-end process is characterized
by high-dimensional design spaces, expensive data acquisition, complex multi-physics inter-
actions, and strict manufacturability constraints. Such unique challenges demand generative
solutions that are not only accurate but also computationally efficient, interpretable, and physi-
cally reliable.
    This project proposes a novel research framework for controllable generative modeling in
DFM full-flow simulation. The central idea is to combine the expressive power of gener-
ative models with the rigor of physics- and mathematics-based constraints. By embedding
domain-specific knowledge, ranging from optical and etching process models to empirical
design rules, into generative architectures, we aim to overcome the limitations of purely data-
driven approaches. This integration will enable controllable simulation and optimization across
the entire DFM pipeline, offering a unified methodology for tasks such as mask synthesis,
lithography hotspot mitigation, and etch profile prediction.
    The expected contributions of this research include: (1) new generative modeling approaches
that incorporate explicit physical and mathematical constraints for DFM, (2) a controllable
full-flow simulation framework that unifies lithography, etching, and mask optimization, (3)
prototype systems validated on realistic semiconductor design flows, and (4) open-source tools
and academic publications to advance both research and practice.
    By emphasizing controllability, interpretability, and industrial applicability, this project aims
to bridge the gap between cutting-edge generative AI and the pressing needs of semiconductor
manufacturing. The synergy between generative modeling and physics-based constraints will
not only advance the scientific foundations of explainable AI in EDA but also create practical,
high-impact solutions for next-generation DFM technologies.
Project Objectives
  • Develop controllable generative models tailored for full-flow DFM simulation, covering
    lithography, etching, and mask optimization.
  • Incorporate physics- and mathematics-based constraints into generative frameworks to
    ensure physical fidelity, efficiency, and reliability.
  • Design a unified methodology that enables end-to-end controllable simulation and opti-
    mization across the entire DFM pipeline.
  • Build and validate prototype systems in collaboration with Huawei Hong Kong and other
    industrial partners, ensuring integration with realistic EDA workflows.


Potential Impact
  • Advance generative modeling research by demonstrating its applicability to complex,
    physics-driven engineering domains such as semiconductor manufacturing.
  • Establish a generalizable paradigm that tightly couples generative AI with domain knowl-
    edge, physical principles, and manufacturability constraints.
  • Enable more accurate, interpretable, and computationally efficient DFM simulations,
    reducing design iterations and manufacturing costs.
  • Produce high-impact academic publications and open-source tools, fostering international
    collaboration and accelerating adoption in the research community.
  • Strengthen the competitiveness of domestic EDA and DFM solutions, supporting strategic
    initiatives in intelligent manufacturing and semiconductor self-reliance.
Background of Research, Research Plan and Method
Background of Research
Design-for-Manufacturing (DFM) [1, 2] has become a central challenge in modern semicon-
ductor production, as shrinking technology nodes demand increasingly precise control over
fabrication processes. The DFM pipeline involves a tightly coupled set of stages, including
photolithography [3], etching, and mask optimization, where small variations can propagate
and significantly impact yield, performance, and reliability. Traditional rule-based and heuris-
tic methods, while interpretable and widely adopted in industry, often fail to scale to these
high-dimensional, multi-physics, and highly constrained design spaces.
    In parallel, generative artificial intelligence [4] has demonstrated remarkable progress in
fields such as natural language and vision, showing strong potential for accelerating design
exploration and enabling automation. However, directly applying these methods to DFM
full-flow simulation is far from trivial. Semiconductor manufacturing requires correctness,
interpretability [5], and controllability at every stage of the pipeline [6]. Purely data-driven
generative models lack mechanisms to enforce physical consistency or to respect the strict
manufacturability constraints inherent in semiconductor processes.
    This project seeks to bridge this gap by developing controllable generative frameworks for
end-to-end DFM simulation [7]. By embedding physics-based models [8, 9], mathematical
constraints [10], and domain-specific rules [11] into generative AI, we aim to deliver trust-
worthy, efficient, and unified solutions for lithography, etching, and mask optimization. The
research outcomes will include new algorithms, prototype systems validated in collaboration
with industry, and open-source contributions to foster broad adoption.

                                          Challenges in DFM
                  Black-box                                                Traceable
                  behavior                                                 decisions

                   Sparse                                                Deterministic
               reasoning traces                                         reproducibility
                                     General
                                                        Industrial          Safety
               Hard to validate     Generative
                                                       EDA Needs          compliance
                                     Methods
                Weak domain
                                                                        Design sign-off
                 constrains
                                              Traditional
                 Inconsistent                                             Consistent
                                              Methods at
                   outputs                                                 outputs
                                              Advanced
                                                Noeds
                              Our focus

                                                             Corner
                                   Long runtime
                                                            explosion


                       Complex model        Tighter margins        Larger scale


Figure 1: Challenges in the DFM process. Our research integrates traditional and generative
methods to meet the demands of industrial EDA.
Research Gaps
Despite advances in generative AI for NLP and computer vision, key gaps hinder its application
to full-flow DFM simulation:
    • Lack of controllability across coupled stages: Current models target isolated tasks,
      but in DFM, lithography, etching, and mask optimization are tightly linked, with errors
      propagating downstream. Existing approaches cannot coordinate across the full pipeline.
    • Weak integration of physics and constraints: Data-driven models demand costly
      datasets and often violate physical laws, limiting reliability. Physics-informed learn-
      ing helps but is rarely combined with generative models in a scalable, controllable way.
    • Limited interpretability and trust: Black-box outputs make verification difficult when
      design rules and manufacturing constraints are implicit, reducing trust in high-stakes
      workflows.
   This proposal addresses these gaps by asking:
   • How to design controllable, reliable generative models for the entire DFM pipeline?
   • How to embed physics and constraints to ensure efficiency and consistency?
   • How to prototype and validate a unified system that demonstrates feasibility and builds
     trust in industrial EDA?


Research Methods
Our research methods are structured into two complementary components: (1) trustworthy
pattern generation, which ensures manufacturability-aware and controllable layout creation;
and (2) high-precision resist simulation, which delivers accurate and differentiable modeling of
photoresist processes. These two parts together establish a unified framework for controllable,
end-to-end DFM simulation.

Trustworthy Pattern Generation
Generating layout patterns that are both diverse and strictly compliant with design rules is a
long-standing challenge. Recent advances from our group, including DiffPattern, DiffPattern-
Flex [12, 13], and ChatPattern [14], demonstrate how generative models can be combined with
explicit rule enforcement to produce reliable patterns.
    At the core of DiffPattern is a discrete diffusion model that synthesizes binary layout
topologies directly, rather than continuous images requiring thresholding. DiffPattern-Flex
extends this approach with fast sampling and efficient legalization, achieving up to an 8×
speedup while maintaining a 100% legality rate for generated patterns. This guarantees that
every output can be trusted as compliant with design-for-manufacturability constraints.
    Complementing diffusion models, ChatPattern integrates a large language model (LLM)
as an interactive agent. Given natural language instructions such as “generate a dense via
array with 100nm pitch,” the LLM interprets the request, decomposes it into actionable com-
mands, and interfaces with the diffusion-based generator. This human-in-the-loop paradigm
enables controllability and customization of generated patterns, a critical step toward practical
deployment.
    Taken together, these innovations yield a workflow that is (1) controllable, via language or
rule-based conditions; (2) trustworthy, thanks to white-box legalization; and (3) aligned with
manufacturability, since generated layouts are verified against real industrial rules.
             #1 Agent Setting                                  #4 User Requirement
             You are a layout designer and are                 Generate a layout pattern library, there are 100k layout patterns in total. The physical
             required to produce a well-designed layout        size fixed as 1.5um * 1.5um. The topology size should be chosen from 200*200 and 500*500.
             pattern according to the user's requirements.     They should be in style of 'Layer-10001'.
             There are some rules you must follow:(Rules)
                                                                                               #6.2 Task Planning and Function Call
              #2 Tool Learning                                  #5.2 Standard Arguments     #6.1 Task Planning and Function Call
              During the design process, you have               Physical size:Arguments
                                                               #5.1 Standard                 # generate basic topology with fixed size
              access to the following functions:                [1500,1500]
                                                               Physical  size: nm            topology = topology_gen(seed, style)
              (functions and descriptions)                      Topology size:
                                                               [1500,1500]   nm [500,500]
                                                                Style: Layer-10001
                                                               Topology size: [200,200]      # extend topology to desired size
             #3 Document Learning
                                                                Count:
                                                               Style:   50000
                                                                      Layer-10001            topology = extension(topology, [200,200])
             There is a standard working pipeline
             your can refer to: (Standard Pipeline)             Extending
                                                               Count:  50000Method: None     # first attempt to legalize the topology
             There are some experience you can refer to:        Drop Allowed:
                                                               Extending         True
                                                                           Method:  Out      layout, failed, log = legalize(topology,[1500,1500])
             (Documents)                                       Drop Allowed: True
                                                                                             # modification un-solvable region for failed case
                                                                                             topology = modification(failed_topology, style)
             #7 Finished                                                                       (When Processing Finished) Return layout
             Summary results and return, save history if necessary                          (When Processing Finished) Return layout



Figure 2: Illustration of the pattern generation pipeline: user intent → LLM agent → diffusion-
based generator → DFM rule checker. The final output is a validated, legal layout pattern.


High-Precision Post-Litho Simulation
While pattern generation provides candidate layouts, their manufacturability must be validated
through lithography simulation. Our recent work TorchResist [15] introduces an open-source,
differentiable resist simulator that combines analytical physics models with modern differen-
tiable programming. Unlike purely data-driven black-box networks, TorchResist employs fewer
than twenty interpretable parameters, calibrated to experimental data, ensuring both efficiency
and physical fidelity. This hybrid design enables accurate simulation on modest datasets and
robust generalization to unseen layouts.
    A key focus is the Post-Exposure
Bake (PEB) process, where photoacid                                             Current Layer         Next Layer
                                                                3D Profile
diffusion and catalytic reactions deter- Aerial Image                                         Light
mine the latent image in the resist.                      Heavy                               Model
                                                          Model
Accurate PEB modeling is crucial, as                                           Aerial Image
                                                                                                Next Loop
                                                                                                  Error ↑
it directly influences the printed crit-
ical dimensions and line-edge rough-              (a) Direct Mapping           (b) Sequential Modeling
ness. However, rigorous numerical PEB Current Layer Aerial Image               Constraint         Next Layer
simulation can consume up to 30% of                                   Physical
                                                                                          Correct
                                                                       Model
runtime in full lithography flows [16].                                                   Error ↓
                                                                        Light
Our method addresses this by devel-                                    Model
                                                                                             Next Loop
oping an analytical, differentiable PEB
module that captures essential reac-                                (c) Our Methods
tion–diffusion physics while remaining
GPU-accelerated. This design achieves Figure 3: Comparison of different modeling
the dual goal of high accuracy and fast paradigms. Our method leverages physical con-
evaluation, making it suitable for itera- straints to correct predictions and reduce errors.
tive design optimization.
    The differentiable nature of TorchResist and PEB modeling further enables gradient-based
co-optimization. For example, gradients of resist profiles with respect to mask features can
guide OPC (optical proximity correction) or design adjustments, closing the loop between
generative pattern design and lithography simulation.
    By uniting trustworthy pattern generation and high-precision resist simulation, this project
creates a controllable full-flow DFM framework. The generative models guarantee legal and
diverse layout patterns, while the differentiable simulator ensures physically accurate predictions
and optimization capabilities. Together, they form a complete pipeline from intent-driven
design to lithography-aware validation, directly addressing the central challenge of trustworthy,
efficient, and manufacturable semiconductor design.


Research Plan
Vision. Build an end-to-end, differentiable, HPC-ready DFM pipeline that (i) simulates imaging
→ PEB → development → etch/CMP with industrial-grade alignment, and (ii) trains a
knowledge-embedded foundation model delivering orders-of-magnitude speedups. This closes
the loop with our trustworthy generation and high-precision resist modules.


WP1. HPC End-to-End DFM Simulation (E2E-Sim)
Goal: Deliver a multi-GPU/multi-node, fully differentiable simulator aligned with commercial
references and wafers, supplying high-fidelity labels and gradients.
    • Unified IR & interfaces: Standardize inputs (layout/mask, optics, process corners) and
      stitch stages with consistent units.
    • Numerical kernels: Optimize FFT/convolutions, reaction–diffusion, and front propa-
      gation with mixed precision, tiling, and pipeline/graph parallelism; expose adjoints for
      OPC/design co-optimization.
    • Physics fidelity & interpretability: Compact parameters (diffusivity, rates, dissolution)
      calibrated on small batches; quantify uncertainty.
    • Alignment & validation: Match golden tools on L/S, contacts, 2D bends, stacks across
      NA/illumination/corners; targets: CD ≤ 2 nm, EPE ≤ 3 nm.
    • Data distillation: Generate teacher datasets with process-aware labels and sensitivities
      to fuel WP2 pretraining/distillation.


WP2. Knowledge-Embedded End-to-End DFM Foundation Model (DFM-
Foundation)
Goal: Train a stage-aware, physics-informed model that mimics the full chain with large
speedups and strong generalization.
    • Architecture: Jointly encode layout + process + optics; use neural operators (e.g.,
      FNO/UNO) for long-range effects; insert domain adapters and physics-consistency layers
      at imaging/PEB/develop/etch.
    • Training: Pretrain/distill on E2E-Sim signals; add masked self-supervision and multi-
      corner multi-task heads; integrate RAG over process decks/rules and instruction tuning
      for callable domain knowledge.
    • Constraints & UQ: Enforce DFM feasibility via projection/feasible decoding; use cali-
      brated uncertainty and active learning for few-shot recalibration on new corners.
    • Closed-loop optimization: Couple with generators and E2E-Sim: fast sensitivities drive
      OPC/layout tweaks; E2E-Sim rechecks critical cases for signoff fidelity.

Expected Outcomes
Open-source high-performance kernels/benchmarks; a knowledge-embedded foundation model
and service; and an industrial-grade, controllable generation ↔ simulation ↔ optimization loop
with accompanying publications.
Pathways to Impact Statement
Design-for-Manufacturing (DFM) full-flow simulation is a critical bottleneck in semiconductor
production, spanning tightly coupled stages such as photolithography, etching, and mask op-
timization. The integration of generative AI into this pipeline faces fundamental challenges:
high-dimensional design spaces, expensive data acquisition, and complex multi-physics in-
teractions. This project advances controllable generative AI for full-flow DFM simulation,
embedding physical models and mathematical constraints into generative frameworks. The
resulting approach promises accurate, interpretable, and efficient AI solutions, with significant
benefits for academia, industry, and society across Hong Kong, the Greater Bay Area, and the
global ecosystem.
Key Issue 1: Controllable Generative Models for Full-Flow DFM
The first challenge is to design generative models that are controllable across the entire DFM
pipeline. Existing approaches to layout and process optimization are fragmented and often
require extensive manual intervention. Purely data-driven methods lack robustness and inter-
pretability, making them unsuitable for critical manufacturing tasks. Our project will embed
domain-specific rules and heuristics into generative AI, enabling systematic, trustworthy outputs
and reducing repetitive design iterations.
Key Issue 2: Physics- and Constraint-Integrated Generative Modeling
The second challenge is to ensure that generative outputs respect physical and mathemati-
cal constraints. Semiconductor processes, particularly photolithography and etching, involve
highly nonlinear multi-physics effects that cannot be captured by black-box models alone. By
incorporating physics-informed equations, numerical solvers, and constraint-guided optimiza-
tion directly into the generative pipeline, this project aims to produce models that are both
efficient and physically consistent, overcoming the limitations of purely empirical or purely
simulation-based methods.
Key Issue 3: Unified Prototype System for Industrial Validation
The third challenge is to integrate these advances into a prototype full-flow DFM simulation
system. This prototype will unify lithography, etching, and mask optimization into a controllable
end-to-end framework. It will be validated through collaboration with Huawei Hong Kong and
other industry partners, ensuring that methods are tested on authentic industrial datasets and
workflows, thereby accelerating transfer into practical EDA and manufacturing tools.
Academia-Industry Collaboration
The proposed research addresses underexplored but critical problems at the intersection of AI
and semiconductor manufacturing. Through close collaboration, industry partners will provide
realistic feedback, benchmarks, and validation environments, while the academic team develops
novel algorithms and theoretical foundations. This two-way synergy ensures that outcomes are
both scientifically groundbreaking and industrially relevant, bridging the gap between research
and practice.
Academic, Industrial, and Societal Impact
Academically, the project will establish a generalizable framework for controllable, physics-
informed generative AI in high-stakes engineering domains. It will contribute to top-tier
publications (e.g., DAC, ICCAD, TCAD, NeurIPS) and release open-source tools to foster
collaboration.
    Industrially, the research will enable more accurate and efficient DFM simulations, reduce
resource-intensive iterations, and strengthen the competitiveness of domestic EDA solutions,
aligning with strategic goals in intelligent manufacturing and semiconductor self-reliance.
    Societally, the project will generate broader impact:
    • Support sustainable manufacturing by reducing design errors, computation cost, and
      material waste.
    • Contribute to talent cultivation in Hong Kong and the Greater Bay Area through advanced
      research training and education.
    • Reinforce Hong Kong’s role as an international hub, linking mainland China with the
      global research community, and promoting responsible adoption of trustworthy AI in
      semiconductor manufacturing.
References
 [1] A. B. Kahng, “A roadmap and vision for physical design,” in Proceedings of the 2002
     international symposium on Physical design, 2002, pp. 112–117.

 [2] D. Z. Pan, B. Yu, and J.-R. Gao, “Design for manufacturing with emerging nanolithogra-
     phy,” IEEE Transactions on Computer-Aided Design of Integrated Circuits and Systems,
     vol. 32, no. 10, pp. 1453–1472, 2013.

 [3] C. Mack, Fundamental principles of optical lithography: the science of microfabrication.
     John Wiley & Sons, 2008.

 [4] I. J. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair,
     A. Courville, and Y. Bengio, “Generative adversarial nets,” Advances in neural infor-
     mation processing systems, vol. 27, 2014.

 [5] F. Doshi-Velez and B. Kim, “Towards a rigorous science of interpretable machine learn-
     ing,” arXiv preprint arXiv:1702.08608, 2017.

 [6] Y. Lin, S. Dhar, W. Li, H. Ren, B. Khailany, and D. Z. Pan, “Dreamplace: Deep learning
     toolkit-enabled GPU acceleration for modern VLSI placement,” in Proceedings of the
     56th Annual Design Automation Conference (DAC), 2019.

 [7] W. Samek, G. Montavon, A. Vedaldi, L. K. Hansen, and K. Müller, Eds., Explainable AI:
     Interpreting, Explaining and Visualizing Deep Learning, ser. Lecture Notes in Computer
     Science. Cham: Springer, 2019, vol. 11700.

 [8] M. Raissi, P. Perdikaris, and G. E. Karniadakis, “Physics-informed neural networks: A
     deep learning framework for solving forward and inverse problems involving nonlinear
     partial differential equations,” Journal of Computational Physics, vol. 378, pp. 686–707,
     2019.

 [9] G. E. Karniadakis, I. G. Kevrekidis, L. Lu, P. Perdikaris, S. Wang, and L. Yang, “Physics-
     informed machine learning,” Nature Reviews Physics, vol. 3, no. 6, pp. 422–440, 2021.

[10] S. Zheng, H. Yang, B. Zhu, B. Yu, and M. Wong, “Lithobench: Benchmarking ai compu-
     tational lithography for semiconductor manufacturing,” Advances in Neural Information
     Processing Systems, vol. 36, pp. 30 243–30 254, 2023.

[11] G. Chen, Z. Wang, B. Yu, D. Z. Pan, and M. D. F. Wong, “Ultrafast source mask
     optimization via conditional discrete diffusion,” IEEE Transactions on Computer-Aided
     Design of Integrated Circuits and Systems, vol. 43, no. 7, pp. 2140–2150, 2024.

[12] Z. Wang, Y. Shen, W. Zhao, Y. Bai, G. Chen, F. Farnia, and B. Yu, “Diffpattern: Layout
     pattern generation via discrete diffusion,” in Proceedings of the 60th Design Automation
     Conference (DAC), 2023.

[13] Z. Wang, W. Zhao, Y. Shen, Y. Bai, G. Chen, F. Farnia, and B. Yu, “Diffpattern-flex: Ef-
     ficient layout pattern generation via discrete diffusion,” arXiv preprint arXiv:2505.04173,
     2025.

[14] Z. Wang, Y. Shen, X. Yao, W. Zhao, Y. Bai, F. Farnia, and B. Yu, “Chatpattern: Layout
     pattern customization via natural language,” 2024, accepted by DAC 2024.
[15] Z. Wang, J. Zhou, S. Zheng, S. Yin, K. Liang, S. Hu, X. Chen, and B. Yu, “Torchresist:
     open-source differentiable resist simulator,” in DTCO and Computational Patterning IV,
     vol. 13425. SPIE, 2025, pp. 397–405.

[16] Z. Yu, P. Xu, Z. Wang, B. Zhu, Q. Wang, Y. Lin, R. Wang, B. Yu, and M. Wong,
     “SDM-PEB: Spatial-Depthwise Mamba for Enhanced Post-Exposure Bake Simulation,” in
     Proceedings of the 62nd ACM/IEEE Design Automation Conference (DAC), San Francisco,
     CA, USA, Jun. 2025, (paper) (poster) (slides).
Related Experience and Participation in Research Projects
During my academic and professional career, I have actively engaged in multiple research
projects and industrial collaborations that are directly relevant to the proposed research. These
experiences have provided me with a strong foundation in lithography simulation, Design
for Manufacturability (DFM), and related computational methodologies. A summary of my
relevant experiences is provided below.

Industrial Research Experience at Huawei Hong Kong Research Center
I undertook a nine-month research internship at Huawei Hong Kong, where I was deeply
involved in internal R&D activities. My primary research focus was on analytical resist
simulation, a crucial step in lithography modeling. Through this project, I developed analytical
approaches that were rigorously validated using real industrial lithography datasets. The results
demonstrated that the proposed analytical methods not only matched but in fact outperformed
existing commercial software in terms of predictive accuracy.
    In addition to the internal technical contributions, I consolidated the research outcomes into
a scholarly paper, which has been accepted for presentation at the SPIE Advanced Lithography +
Patterning 2025 conference, a premier venue in the field. Furthermore, to foster reproducibility
and broader impact, I took the initiative to release the source code of this work as an open-
source project, making the methodologies accessible to both academic researchers and industrial
practitioners.

Academic Research on Design for Manufacturability (DFM)
In parallel with my industrial research, I have also led and participated in multiple academic
research projects in the domain of Design for Manufacturability (DFM). These works have been
published in top-tier journals and conferences in the field of Electronic Design Automation
(EDA), underscoring their novelty and technical merit.
    The projects collectively spanned a wide range of topics across the DFM pipeline, including:
    • Pattern generation: Developing efficient algorithms for generating lithographic patterns
       with enhanced manufacturability.
    • Source-mask optimization: Investigating optimization frameworks for jointly tuning
       illumination sources and mask patterns to improve process windows.
    • Resist simulation: Advancing analytical and computational resist modeling to improve
       accuracy and scalability.
    • Post-Exposure Bake (PEB) simulation: Exploring physical and data-driven models to
       capture thermal diffusion and chemical processes during PEB.
    All of the above directions directly align with the scope of this proposal and collectively
demonstrate my strong research foundation in lithography, simulation, and manufacturability-
driven design methodologies. My experiences not only highlight my technical capabilities but
also reflect my ability to bridge industrial and academic research, a quality that will significantly
contribute to the successful execution of the proposed project.
Leadership Experience and Achievements
Throughout my academic and professional journey, I have consistently demonstrated leadership
abilities and achieved notable accomplishments both inside and outside academia. These expe-
riences have equipped me with strong skills in team management, mentoring, and collaborative
research, which will be invaluable for the successful execution of the proposed project.

Leadership in Academic Research and Student Mentorship
During my doctoral studies, I have taken on leadership roles in supervising and guiding under-
graduate and master’s students in their summer practice projects as well as final-year graduation
projects. I also provided research mentorship to multiple research interns, helping them to
define technical objectives, develop research methodologies, and achieve concrete outcomes.
    Several of the students I supervised received departmental awards for Outstanding Gradua-
tion Design and Excellent Summer Practice Project, reflecting the quality of their work and the
effectiveness of my mentorship. Moreover, the projects I led with research interns resulted in
publications at top-tier international conferences and journals, demonstrating that I was able to
successfully transform collaborative research into impactful scholarly outcomes. These experi-
ences highlight my ability to inspire, organize, and guide junior researchers toward high-level
academic achievements.

Leadership and Achievements in International Competitions
Prior to my doctoral studies, I participated in the RoboCup World Championship, one of
the most prestigious international competitions in robotics and artificial intelligence. I served
as the team leader of the vision algorithm group, where I was responsible for designing and
implementing advanced visual perception algorithms for robotic soccer agents.
    Under my leadership, the team achieved remarkable success by securing the Second Place in
the Technical Challenge and the Third Place in the 1v1 Contest during that year’s RoboCup
competition. These accomplishments not only showcased technical innovation but also demon-
strated my capacity to coordinate efforts in a high-pressure international setting and deliver
results at a global level.

Summary
Taken together, these experiences illustrate my proven track record of leadership both in research
and in competitive environments. I have consistently demonstrated the ability to mentor and
motivate junior researchers, manage collaborative projects to fruition, and achieve recognition
at international competitions. These qualities will contribute significantly to my ability to lead
and sustain impactful research projects in the proposed work.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
l    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
l    The nominee should complete Part A of the referee’s report.
l    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
l    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
l    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A     Nominee’s Particulars            (to be completed by the nominee)
Name of Nominee :       (Surname) WANG
                        (Other name) Zixiao
Host Faculty / School / Department / Dept of Computer Science and Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B     Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
l   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
l   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
l   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
l   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
l   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
l   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
l   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?


I have worked with Zixiao as his co-supervisor during his doctoral studies over the past three years.




                                                        2
                                                                  JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent      Very Good        Good                Average          No basis
                                (top 5%)       (top 6-20%) (top 21-50%)            or below            for
                                                                                  (lower than       judgment
                                                                                     50%)
Intellectual capacity               R               ☐                 ☐                 ☐               ☐
Ability for conducting
scholastic research
                                    R               ☐                 ☐                 ☐               ☐
Knowledge of proposed
research study
                                    R               ☐                 ☐                 ☐               ☐
Analytical power and
reasoning
                                    R               ☐                 ☐                 ☐               ☐
Judgment                            ☐               R                 ☐                 ☐               ☐
Imagination and
originality
                                    R               ☐                 ☐                 ☐               ☐
Motivation and
perseverance
                                    R               ☐                 ☐                 ☐               ☐
Skills of writing and
argumentation
                                    R               ☐                 ☐                 ☐               ☐
Capacity for independent
work
                                    R               ☐                 ☐                 ☐               ☐
Reliability and sense of
responsibility
                                    R               ☐                 ☐                 ☐               ☐
Leadership experience               R               ☐                ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

Zixiao has been an exceptionally motivated and capable research postgraduate student. He has consistently
impressed me with his diligence, intellectual curiosity, and creativity in research. We have collaborated on
several projects focused on the evaluation and application of deep generative models in both EDA and computer
vision domains.

In our first collaboration, Zixiao developed a scalable distributed evaluation mechanism for generative AI
models that can be seamlessly incorporated into federated learning settings with minimal communication and
privacy overhead. Building on this foundation, we extended our research to explore the application of generative
models, diffusion models, and large language models in EDA tasks. Zixiao’s innovative ideas and rigorous
execution have led to several top-tier publications.

Overall, I regard Zixiao as a highly talented and dedicated young researcher. I strongly recommend him for
academic and research positions, confident that he will continue to make impactful contributions to the field.


                                                        3
                                                                  JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
l    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
l    The nominee should complete Part A of the referee’s report.
l    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
l    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
l    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) WANG
                        (Other name) Zixiao
Host Faculty / School / Department / Dept of Computer Science and Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
l   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
l   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
l   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
l   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
l   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
l   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
l   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?
I have known Zixiao in the capacity of his Master’s supervisor. We first met in August 2018, and I have known
him for seven years to date.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent     Very Good        Good                Average          No basis
                                (top 5%)      (top 6-20%) (top 21-50%)            or below            for
                                                                                 (lower than       judgment
                                                                                    50%)
Intellectual capacity               R              ☐                 ☐                ☐                ☐
Ability for conducting
scholastic research
                                    R               ☐                ☐                ☐                ☐
Knowledge of proposed
research study
                                    R               ☐                ☐                ☐                ☐
Analytical power and
reasoning
                                    R               ☐                ☐                ☐                ☐
Judgment                            R               ☐                ☐                ☐                ☐
Imagination and
originality
                                    R               ☐                ☐                ☐                ☐
Motivation and
perseverance
                                    R               ☐                ☐                ☐                ☐
Skills of writing and
argumentation                       ☐               R                ☐                ☐                ☐
Capacity for independent
work
                                    R               ☐                ☐                ☐                ☐
Reliability and sense of
responsibility
                                    R               ☐                ☐                ☐                ☐
Leadership experience              ☐                R                ☐                ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
The nominee possesses exceptional research ability, with a solid theoretical foundation, outstanding
experimental skills, and a strong commitment to staying engaged with the latest developments in the field. He
has demonstrated remarkable talent in both understanding and advancing cutting-edge topics in explainable
generative models for electronic design automation (EDA).

In terms of research potential, he maintains a profound intellectual curiosity and an extraordinary intrinsic
motivation, which also inspired him to pursue doctoral studies after completing his Master’s degree under my
supervision. Unlike general machine learning research, the EDA domain requires methods to be grounded in
reliable theoretical support. His work on explainable generative models addresses this need and holds significant
promise for the advancement of EDA. Moreover, his rare ability to bridge insights across disciplines further
strengthens his long-term research potential.




                                                        3
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
    Beyond his academic excellence, the nominee has cultivated strong communication and leadership skills. During
    his Master’s program, he successfully led several research projects in collaboration with both academic and
    industrial partners. His scholarly contributions have been recognized through multiple publications in top-tier
    conferences and journals in the field, and he has delivered oral presentations at leading academic venues. These
    experiences reflect not only his technical expertise but also his ability to engage with the broader research
    community.

    I am confident that his proven research ability, strong potential for innovation, and well-rounded professional
    skills will enable him to make meaningful contributions to the JRFS program and to the academic community at
    large.


     4. What is your overall recommendation for this nominee?

                    R      Recommend very strongly
                    ☐      Recommend strongly
                    ☐      Recommend with reservation
                    ☐      Do not recommend

      Name of Referee: YUAN CHUN                                         Title: Prof
                               (in BLOCK letters)                                 (Prof / Dr / Mr / Ms / Mrs)*
      University / Organisation: Tsinghua University
      Position: Professor
      E-mail Address: <EMAIL>
      Telephone Number#: +86 13480800455
      (# Please provide country code and area code, e.g. +86-10-1234567)
l      * Please delete as appropriate


     Declaration by the Referee:

     I hereby declare that
     R I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
     R I have no conflict(s) of interest in the capacity of academic referee in this nomination.


      Signature:                                                    Date:     2025/08/28




     This document is strictly CONFIDENTIAL. Please send it directly to the supporting
     university (under confidential cover) whose details are given on the cover page.
                                                             4
                                                                      JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                  R      Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: FARZAN FARNIA                                   Title: Prof
                             (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*
     University / Organisation: Chinese University of Hong Kong
     Position: Assistant Professor
     E-mail Address: <EMAIL>
     Telephone Number#: +852-39431279
     (# Please provide country code and area code, e.g. +86-10-1234567)
l     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
    R I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
    R I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date:     27/08/2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
