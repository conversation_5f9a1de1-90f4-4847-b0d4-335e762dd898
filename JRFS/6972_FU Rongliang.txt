Introduction                 Rongliang Fu
     Home: Sichuan, China                      Mobile：0852 61083825 / 086 18881814693
     Email: <EMAIL>            GitHub：https://github.com/Flians
Research Interest
    EDA, Logic Synthesis, Superconducting Electronics
Education
    2014.09-2018.06    Bachelor                Northwestern Polytechnical University      Software Engineering
    2018.01-2018.06    Public study abroad     Universidad Politécnica de Madrid          Software Engineering
    2018.09-2021.06    Master                  ICT, CAS                                   Computer Science and Technology

    2022.08-Now        Doctor                  The Chinese University of Hong Kong        Computer Science and Engineering

Publications (CCF A: 6, CCF B: 5, CCF C: 4 as First Author; Totally 26 papers)
   [1] <PERSON> et al., “Design Automation Methodology from RTL to Gate-level Netlist and Schematic for RSFQ Logic Circuits,”
        GLSVLSI, pp. 145–150, 2020.
   [2] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, “Equivalence Checking for Superconducting RSFQ Logic Circuits,”
        GLSVLSI, pp. 51–56, 2021. (Best Paper Nomination)
   [3] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, “A Survey on Superconducting Computing Technology:
        Circuits, Architectures and Design Tools,” CCF Trans. HPC, vol. 4, pp. 1–22, 2022.
   [4] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, "JBNN: A Hardware Design
        for Binarized Neural Networks Using Single-Flux-Quantum Circuits," IEEE Transactions on Computers (TC), vol.
        71, no. 12, pp. 3203-3214, 2022.
   [5] Rongliang Fu, Mengmeng Wang, Yirong Kan, Nobuyuki Yoshikawa, Tsung-Yi Ho, Olivia Chen, “A Global
        Optimization Algorithm for Buffer and Splitter Insertion in Adiabatic Quantum-Flux-Parametron Circuits,” ASP-
        DAC, pp. 769–774, 2023.
   [6] Rongliang Fu, Junying Huang, Mengmeng Wang, Yoshikawa Nobuyuki, Bei Yu, Tsung-Yi Ho, Olivia Chen,
        “BOMIG: A Majority Logic Synthesis Framework for AQFP Logic,” DATE, pp. 1-2, 2023.
   [7] Xinda Chen, Rongliang Fu, Junying Huang, Huawei Cao, Zhimin Zhang, Xiaochun Ye, Tsung-Yi Ho, Dongrui Fan,
        “JRouter: A Multi-Terminal Hierarchical Length-Matching Router under Planar Manhattan Routing Model for RSFQ
        Circuits,” GLSVLSI, pp. 515–520, 2023.
   [8] Rongliang Fu, Olivia Chen, Nobuyuki Yoshikawa, Tsung-Yi Ho, “Exact Logic Synthesis for Reversible Quantum-
        Flux-Parametron Logic”, ICCAD, pp. 1-9, 2023.
   [9] Rongliang Fu, Olivia Chen, Bei Yu, Nobuyuki Yoshikawa, Tsung-Yi Ho, “DLPlace: A Delay-Line Clocking-based
        Placement Framework for AQFP Circuits”, ICCAD, pp. 1-8, 2023.
   [10] Siyan Chen, Rongliang Fu, Junying Huang, Zhimin Zhang, Xiaochun Ye, Tsung-Yi Ho, Dongrui Fan, “JPlace: A
        Clock-Aware Length-Matching Placement for Rapid Single-Flux-Quantum Circuits,” DATE, 2024.(Co-first Author)
   [11] Rongliang Fu, Robert Wille, Tsung-Yi Ho, “RCGP: An Automatic Synthesis Framework for Reversible Quantum-
        Flux-Parametron Logic Circuits based on Efficient Cartesian Genetic Programming,” DAC, 2024.
   [12] Shui Jiang, Rongliang Fu, Lukas Burgholzer, Robert Wille, Tsung-Yi Ho, Tsung-Wei Huang, “FlatDD: A High-
        Performance Quantum Circuit Simulator using Decision Diagram and Flat Array,” ICPP, 2024.
   [13] Siyuan Liang, Rongliang Fu, Mengchu Li, Sun-Ming Tseng, Ulf Schlichtmann, Tsung-Yi Ho, “RABER: Reliability-
        Aware Bayesian-Optimization-based Control Layer Escape Routing for Flow-based Microfluidics,” ICCAD, 2024.
   [14] Rongliang Fu, Mengmeng Wang, Yirong Kan, Olivia Chen, Nobuyuki Yoshikawa, Bei Yu, Tsung-Yi Ho, “Buffer
        and Splitter Insertion for Adiabatic Quantum-Flux-Parametron Circuits,” IEEE TCAD, 2024.
   [15] Rongliang Fu, Chao Wang, Bei Yu, Tsung-Yi Ho, “TeMACLE: A Technology Mapping-Aware Area-Efficient
        Standard Cell Library Extension Framework,” IEEE TCAD, 2024.
   [16] Yuan Pu, Fangzhou Liu, Z. He, K. Zhu, Rongliang Fu, Z. Wang, T.-Y. Ho, Bei Yu. “HeLO: A Heterogeneous Logic
        Optimization Framework by Hierarchical Clustering and Graph Learning,” ISPD, 2024. (Best Paper Nomination)
   [17] Rongliang Fu, Robert Wille, Nobuyuki Yoshikawa, Tsung-Yi Ho, “Efficient Cartesian Genetic Programming-based
        Automatic Synthesis Framework for Reversible Quantum-Flux-Parametron Logic Circuits,” IEEE TCAD, 2025.
   [18] Wei Xuan, Zhongrui Wang, Lang Feng, Ning Lin, Zihao Xuan, Rongliang Fu, Tsung-Yi Ho, Yuzhong Jiao and
        Luhong Liang, “SeDA: Secure and Efficient DNN Accelerators with Hardware/Software Synergy,” DAC, 2025.


                                                                                                                      1/3
    [19] Rongliang Fu, Ran Zhang, Ziyang Zheng, Zhengyuan Shi, Yuan Pu, Junying Huang, Qiang Xu, Tsung-Yi Ho, “Late
         Breaking Results:Hybrid Logic Optimization with Predictive Self-Supervision,”DAC,2025.(22.6%acceptance ratio)
    [20] Yang Su, Sheng Li, Huilong Jiang, Haofei Yin, Rongliang Fu, Junying Huang, Xiaochun Ye, Zhimin Zhang, Jie Ren,
         Xiaoping Gao, Tsung-Yi Ho, Dongrui Fan, “JBSA: A Bit-Serial Accelerator for Deep Neural Networks Using
         Superconducting SFQ Logic,” ICS, 2025.
    [21] Minglei Zhou, Rongliang Fu, Ran Zhang, Xiaochun Ye, Tsung-Yi Ho, Junying Huang. “An Optimal DFF-Oriented
         Technology Legalization Algorithm for Rapid Single-Flux-Quantum Circuits,” GLSVLSI, 2025.
    [22] Rongliang Fu, Minglei Zhou, Huilong Jiang, Junying Huang, Xiaochun Ye, Tsung-Yi Ho, “J2Place: A Multiphase
         Clocking-Oriented Length-Matching Placement for Rapid Single-Flux-Quantum Circuits,” ICCAD, 2025.
    [23] Leilei Jin, Rongliang Fu, Zhen Zhuang, Liang Xiao, Fangzhou Liu, Bei Yu, Tsung-Yi Ho, “ChronoTE: Crosstalk-
         Aware Timing Estimation for Routing Optimization via Edge-Enhanced GNNs,” ICCAD, 2025.
    [24] Rongliang Fu, Libo Shen, Ziyi Wang, Zhengxing Lei, Zixiao Wang, Junying Huang, Bei Yu, Tsung-Yi Ho, “DCLOG:
         Don't Cares-based Logic Optimization using Pre-training Graph Neural Networks,” ASPDAC, 2026.
    [25] Shuo Ren, Zhen Zhuang, Rongliang Fu, Leilei Jin, Libo Shen, Bei Yu, Tsung-Yi Ho, “Partitioning-free 3D-IC
         Floorplanning,” ASPDAC, 2026.
    [26] Rongliang Fu, Minglei Zhou, Siyan Chen, Xinda Chen, Junying Huang, Xiaochun Ye, Zhimin Zhang, Tsung-Yi Ho,
         “JPnR: A Length-Matching Placement and Routing Framework for Single-Flux-Quantum Circuits,” submitted to
         IEEE Transactions on Computers (TC), 2025.
Patents
    CN112114875A Superconducting parallel register file device, 2020.
    CN112116094B Superconducting pipeline circuit and processor, 2020.
    CN111427810B Superconducting interface asynchronous acquisition device, 2020.
    CN111950215B Method for generating multi-fan-out clock signal for superconducting RSFQ circuit, 2020.
    CN111950216B Method, computer readable storage medium and electronic device for generating multi-fanout signal, 2020.
    CN113128165A Method for generating multi-fan-out clock signal facing superconducting RSFQ circuit, 2021.
    CN112861463A Superconducting processor and input/output control module thereof, 2021.
    CN114841102A A superconducting binary neural network acceleration method and accelerator, 2022.
Internships
     2020.05-2020.08             Ant Group (Alibaba)           Graph Computing            Java
    Ø Mainly engaged in graph representation and storage, including graph representation and compression (50%reduced
        to 47%), and the implementation of struct2vec and A * algorithm.
     2020.12-2021.03             MeiTuan                       Motion Planning            C++
    Ø Mainly engaged in the decision-making of vehicle behavior and obstacle status on crosswalks, including path
        planning (A* algorithm, RRT) and path tracking (PID, LQR, MPC), etc.
     2021.04-2021.05             Tencent                       Crowd Navigation           C++
    Ø Mainly engaged in the diversity enhancement of crowd navigation based on UE4 in Timi Studio Group.
     2021.11-2022.07             The Chinese University of Hong Kong                      Research Assistant
    Ø Mainly engaged in EDA for superconducting logic circuits, during which one paper was published in IEEE TC.
     2024.05-2024.08             National Center of Technology Innovation for EDA         AI for Logic Synthesis
    Ø Mainly engaged in hybrid logic optimization with GNN, during which one paper was published in DAC.
Projects
    Ø Successfully applied for the 2022 General Research Fund (GRF) from the Research Grants Council (RGC) of Hong
        Kong, China with the topic “DIAL: design and implementation of AQFP logic”.
    Ø Successfully applied for the second batch of five major challenges of industrial software: "Generation of Component
        Library Based on Pre-trained AI Model" released by Huawei, https://www.disa.org.cn/Content-203.html.
    Ø Collaborate with Wang Chao Group at Southeast University "Richness Design of Industrial Standard Cells".
    Ø Lead the project “ABCOv100: 3D Architecture Floorplan with Multi-Objective Optimization” from Huawei, 2025.
    Ø Lead the project “LeafCell Layout Automation” from Huawei, 2025.
Teaching Assistant
   Ø Formal Languages and Automata Theory (CSCI3130)                 Ø Problem Solving by Programming (ENGG1110D)
   Ø EDA for Physical Design of Digital Systems (CENG5270)           Ø Data Structures (CSCI2100)


                                                                                                                    3/3
Services
    Ø Selected as DAC Young Fellow 2021, 2024, and 2025.
    Ø Obtained Postgraduate Studentship of THE CHINESE UNIVERSITY OF HONG KONG in April 2022.
    Ø Obtained ACM SIG Travel Grant in May 2025 and IEEE CEDA Student Travel Grant in August 2025.
    Ø Gave a talk entitled “Design Tools for Superconducting Logic Circuits Towards Extremely Energy-Efficient
        Computing” at the Research Institute of Superconductor Electronics (RISE), Nanjing University on Aug. 8, 2025.
    Ø Conference Reviewer of International Conference on Computer-Aided Design (ICCAD), International
        Symposium on Physical Design (ISPD), Great Lakes Symposium on VLSI (GLSVLSI), International
        Symposium of EDA (ISEDA).
    Ø Journal Reviewer of IEEE Transactions on Computer-Aided Design of Integrated Circuits and Systems (TCAD),
        ACM Journal on Emerging Technologies in Computing Systems (JETC), ACM Transactions on Design
        Automation of Electronic Systems (TODAES).




                                                                                                                 3/3
The growing demand for energy-efficient computing technologies has become imperative due
to the massive energy consumption challenges faced by modern society. The limitations of
CMOS-based computing systems, particularly the power wall problem, have driven the pursuit
of emerging technologies that can deliver comparable or superior performance with reduced
energy consumption. Among the most promising are Josephson junction (JJ)-based
superconducting logic circuits, including Rapid Single-Flux-Quantum (RSFQ) and Adiabatic
Quantum-Flux-Parametron (AQFP). These technologies offer ultra-low switching energy, near-
zero static power, and high-speed operation, positioning them as compelling candidates for
energy-efficient computing.

Despite their advantages, superconducting logic circuits pose unique design challenges that
render direct reuse of mature CMOS electronic design automation (EDA) flows ineffective.
Superconducting circuits exhibit fundamentally different device and interconnect
characteristics: (i) strict fan-out limitations require explicit splitters, (ii) data propagation is
tightly synchronized to global or multi-phase clocks, and (iii) wirelengths are bounded by
timing and signal integrity constraints. Furthermore, commonly used elements in CMOS design,
such as buffers, standard logic gates, and clocking topologies, have distinct implementations
and costs in JJ-based logic, particularly under AC biasing and multi-phase clocking. These
differences lead to unique trade-offs among area, timing, energy, and clock distribution that are
not captured by existing tools. The absence of mature, domain-specific electronic design
automation solutions has therefore become a primary bottleneck to the broader adoption of
superconducting logic.

To address this gap, we plan to develop an end-to-end design automation framework tailored to
superconducting logic, spanning technology-aware logic synthesis, placement, clock-domain
assignment, and routing. Our approach integrates superconducting-specific constraints, such as
single fan-out, clock phase alignment, and maximum permissible wirelength, directly into the
optimization loop. At the logic level, we target gate libraries and templates appropriate for
RSFQ and AQFP, incorporating energy-delay models that reflect JJ switching dynamics and
adiabatic operation. At the physical level, we couple placement with clock-tree co-design and
routing that respects technology legalization requirements while minimizing splitter count and
buffer overhead.

By unifying these capabilities, the proposed toolchain aims to deliver automated, scalable, and
verifiable design flows that bridge the gap between high-level specifications and fabricable
superconducting layouts. We anticipate that this framework will significantly reduce design
iteration time, improve circuit performance, and enable systematic exploration of architectural
trade-offs across superconducting logic families. Ultimately, we expect that our proposed
solution can hold promise for enabling the practical deployment of superconducting logic
circuits in energy-efficient computing systems, advancing the state of the art beyond CMOS-
era limitations.
1. Develop a technology legalization-aware placement for adiabatic quantum-flux parametron
circuits.

Adiabatic Quantum-Flux Parametron (AQFP) is an emerging superconducting technology
renowned for its ultra-low power consumption, but its circuit design faces unique challenges
due to stringent clock-synchronized data propagation and fan-out constraints. Existing AQFP
circuit design flows first perform logic synthesis, where technology legalization is implemented
by inserting buffers and splitters, and then proceed with placement. However, during the
placement process, numerous buffers are also required to meet wirelength constraints. Hence,
we plan to propose a technology legalization-aware placement methodology, that co-optimizes
technology legalization and placement. By integrating buffer and splitter insertion during the
placement process rather than during the logic synthesis process, our method simultaneously
addresses delay, fan-out, and wirelength constraints, thereby reducing the number of inserted
buffers. We will evaluate the effectiveness and efficiency of our approach on the AQFP
benchmarks.




2. Develop a multiphase clocking-oriented clock-aware length-matching placement for rapid
single-flux-quantum circuits.

Superconducting Rapid Single-Flux-Quantum (RSFQ) logic combines low power consumption
with high-frequency operation, making it a promising platform for future computing systems.
However, ensuring the correct operation of RSFQ circuits requires inserting numerous D flip-
flops (DFFs), which substantially increase circuit area and energy dissipation. Recent studies
have demonstrated that the multiphase clocking scheme can effectively reduce the number of
required DFFs. Despite these advantages, existing placement tools do not support multiphase
clocking RSFQ circuits. To address this limitation, we plan to develop a novel multiphase
clocking-oriented length-matching placement framework for RSFQ circuits. We will design
two new RSFQ cells, TFFDO and TFFDE, to simplify the clock network in two-phase clocking
designs. Meanwhile, we plan to propose a maximum flow-based method that constructs the
clock distribution column by column and a dynamic programming algorithm that minimizes
the total vertical wirelength while maintaining fixed placement orders. The effectiveness and
efficiency of our method will be demonstrated on the ISCAS85 and EPFL benchmarks.
3. Develop a shape-controllable placement algorithm for rapid single-flux-quantum circuits.

Superconducting Rapid Single-Flux-Quantum (RSFQ) circuits operate extremely fast, from a
few tens of GHz to hundreds of GHz. Since most RSFQ logic gates require synchronization
with a single clock, RSFQ circuits have a clock-driven gate-level pipelining architecture in
nature. To meet strict timing constraints in logic, the insertion of numerous D flip-flops (DFFs)
is required. This not only increases circuit power and incurs a prohibitive area penalty but also
causes a very slender circuit layout and a large width-to-height ratio. However, practical chip
integration demands explicit control of layout shape. Hence, we plan to develop a shape-
controllable placement algorithm for RSFQ circuits. The algorithm needs to simultaneously
consider data and clock signal length matching while regulating the layout aspect ratio,
effectively ensuring precise timing alignment and mitigating timing alignment challenges
during the routing phase. We will build on existing placement algorithms by dynamically
folding pipeline stages to shrink the layout width. Finally, we will validate the effectiveness and
efficiency of our approach on the RSFQ benchmark circuits.




4. Integrate point tools for superconducting logic circuits into an end-to-end design automation
flow.

Although superconducting logic circuits have received more and more attention and many EDA
methods have been proposed for superconducting logic circuits, the community still lacks an
open-source, end-to-end EDA tool, and even widely available point tools are scarce. Currently,
we have done numerous research works on superconducting logic circuit design automation
methods, including logic synthesis, placement, and routing. Hence, we plan to integrate our
point tools to form a unified, end-to-end flow, and then incorporate the methods proposed above.
We will release this flow as open source to accelerate research adoption and enable the practical
deployment of superconducting logic circuits in energy‑efficient computing systems.
1     Background of Research
Massive energy consumption has become an urgent problem in modern computing sys-
tems, driving the imperative requirement for energy-eﬃcient computing technologies. Due
to the power wall problem confronted within CMOS-based computing systems, the de-
mand for energy-eﬃcient computing systems has been driving the development of al-
ternative technologies that can match and extend the current performance of CMOS
technology at lower energy consumption. Among various emerging technologies, super-
conducting logic circuits based on the Josephson junction (JJ) have garnered significant
attention due to their ultra-low power consumption and high-speed operation.
1.1    Superconducting Logic Circuits
Superconducting logic families have demonstrated remarkable potential for processing
both analog and digital signals with minimal energy dissipation and ultra-fast switching
speeds [1]. As the most popular among them, rapid single-flux-quantum (RSFQ) logic [2]
has been a prominent technology, demonstrating operational speeds up to 770 GHz at 4.2
K [3]. Since its requirement for constant direct current (DC) bias through on-chip resis-
tors results in increased static power dissipation as circuits scale, various approaches have
been proposed to address this limitation, including LR-biased RSFQ logic [4], low-voltage
RSFQ logic [5], RQL [6], and energy-eﬃcient SFQ [7]. To fundamentally address the
power consumption overhead of DC bias, adiabatic quantum-flux-parametron (AQFP)
technology [8] has emerged as a promising solution by utilizing alternating current (AC)
excitation as both clock signal and power supply. This approach enables bit-energy reduc-
tion to the order of thermal energy while maintaining operational frequencies of several
Gigahertz. TABLE 1 presents a comparison among CMOS, RSFQ, and AQFP tech-
nologies. Despite these advantages, superconducting logic circuits pose unique design
challenges. Characteristics like fan-out limitations, clock-synchronized data propagation
requirements, and maximum wirelength constraints make their design and implementa-
tion highly complex. Therefore, mature and robust electronic design automation (EDA)
tools are critical for the eﬃcient design and adoption of superconducting logic circuits.
                 Table 1. The comparison among CMOS, RSFQ, and AQFP.
Circuit              CMOS             RSFQ                         AQFP
Active component Transistor        Josephson junction             Josephson junction
Passive component Capacitor        Inductor                       Inductor
Information       Voltage level    SFQ pulse                      Current pulse
Clocking scheme   Asynchronization Synchronization (Clock signal) Synchronization (Clock signal)
                                   1 (Splitter                    1 (Clocked splitter
Fan-out             1
                                   for multiple fan-outs)         for multiple fan-outs)
Power             Direct current   Direct current                 Alternating current
Frequency            ⇠6 GHz@77 K      50 GHz@4.2 K                 5 GHz@4.2 K
Switching energy     106 KB T ln 2    103 KB T ln 2                101 KB T ln 2


1.2    Previous Works for Logic Synthesis
Logic synthesis is a vital process, transferring a high-level description of the design into
a legal gate-level netlist. Since superconducting logic has the fan-out limitation and
the path balancing requirement, as shown in Fig. 2, mature EDA tools for conventional
CMOS logic cannot be directly applied to superconducting logic. Thus, many existing
works [9–15] investigate the buﬀer and splitter insertion to meet these two requirements
for superconducting logic circuit design. These works insert buﬀers and splitters after

                                               1
the circuit structure is determined by logic optimization. Furthermore, AQFP logic has
the native majority function. Since the majority-inverter graph (MIG) provides a more
compact logic representation while having the same area as AND/OR gates, it is usually
employed in AQFP circuit design. Thus, many existing works [16–19] proposed MIG-
based logic optimization methods suitable for AQFP logic. In contrast, the XOR-AND
graph (XAG) is more suitable for RSFQ logic, and existing works [20,21] proposed XAG-
based RSFQ logic optimization methods. These works try to optimize the size or depth
of the circuit and then process the splitter and buﬀer insertion. However, the size or
depth optimum may not produce an optimal superconducting circuit, causing buﬀers and
splitters to still make up a large portion of circuits. Therefore, it is critical to consider
the interaction between logic optimization and buﬀer and splitter insertion globally.
1.3    Previous Works for Physical Design
RSFQ logic gates have strict timing constraints regarding the arrival times of data and
clock pulses, and it is necessary to meet these timing constraints by extending the length of
passive transmission lines (PTLs). Kito et al. [22] proposed a single-phase clocking RSFQ
placement algorithm based on the Simulated Annealing (SA) algorithm. Wang et al. [23]
proposed a novel row-based clock-tree-aware placement methodology for RSFQ circuits
to minimize the maximum path length. Chen et al. [24] proposed a clock-aware length-
matching placement method for single-phase RSFQ circuits. This method simultaneously
addresses the length-matching issues of both data and clock signals, eﬀectively meeting the
timing constraints of RSFQ circuits and significantly reducing the complexity of timing
alignment during the routing phase. Recent studies [25, 26] indicate that multiphase
clocking can eﬀectively reduce the number of inserted DFFs and improve the area eﬃciency
of RSFQ circuits. Unfortunately, these single-phase clocking-oriented RSFQ placement
methods cannot be directly applied to RSFQ placement with multiphase clocking. Under
a multiphase clocking scheme, clock signals with diﬀerent phases increase the diﬃculty of
timing alignment. Therefore, multiphase clocking-oriented placement methods are critical
for RSFQ circuit design.
     AC-powered AQFP circuits operate in a multi-phase clocking schema. Existing physi-
cal design methods [27–29] focus on AQFP circuits with 4-phase clocking. The method [30]
further reduces the circuit latency through implementing the delay-line clocking. These
methods mainly address two constraints during AQFP circuit design: 1) data synchroniza-
tion and 2) maximum wirelength constraints. In AQFP circuits, minimizing the physical
distance between components is crucial for preserving signal integrity and reducing power
dissipation. Since excessive wirelength can introduce parasitic eﬀects, buﬀers are required
to mitigate the negative eﬀects of excessive interconnects, as shown in Fig. 3. Moreover,
from the perspective of the whole design flow for AQFP circuits, the insertion of buﬀers
is imperative not solely during the physical design stage but also throughout the logic
synthesis stage. Particularly, buﬀers inserted in both stages are to meet the delay re-
quirement of the AQFP circuit design. Therefore, the integration of these two stages may
be beneficial in minimizing the number of inserted buﬀers, the circuit latency, and even
the energy consumption.

2     Methodology
To address the above problems, we plan to propose SCFlow, an end-to-end framework, to
automatically design and implement superconducting logic circuits. SCFlow will achieve
the transformation from RTL description to the gate-level netlist and then to the layout,


                                             2
where more practical physical design issues will be taken into account and addressed.
2.1    Technology Legalization-aware Placement for AQFP Circuits
Existing AQFP circuit design flows first perform logic synthesis, insert buﬀers B and
splitters S to achieve technology legalization, and then proceed with placement. How-
ever, during the placement process, numerous buﬀers are also required to meet wire-
length constraints. Hence, we plan to propose a technology legalization-aware placement
methodology to collaboratively optimize technology legalization and placement. For a
given AQFP circuit netlist G(V, E), where the node set V = I [ O [ C consists of the
set I of primary inputs (PIs), the set O of primary outputs (POs), and the set C of logic
gates, and E is the set of data nets, the overall problem of the integration of technology
legalization and placement can be summarized as

      min
        0
          |S [ B|,                                                                        (1)
       G
 s.t. Fan-out constraints: 8e 2 E 0 , |et | = 1,                                          (2)
      Clock-synchronized data propagation constraints: 8u 2 FI(v), v 2 V , lv = lu + 1,
                                                                         0
                                                                                          (3)
      PI and PO alignment constraints: 8i 2 I, li = 0, and 8o 2 O, lo = max0 lv + 1,      (4)
                                                                             v2V
      Vertical position constraints: 8u, v 2 V , lu = lv =) yu = yv ,
                                                   0
                                                                                          (5)
      Wirelength constraints: 8u 2 FI(v), v 2 V 0 , dl u,v  lmaxu ,                      (6)
      Overlap constraints: 8u, v 2 V 0 , lu = lv =) xu + wu  xv _ xv + wv  xu .         (7)

     By introducing the binary variables and the big-M method, this problem can be
modeled as a mixed-integer linear programming model. However, directly solving this
model is impractical. The wirelength constraints are hardly satisfied due to the limited
driving force, especially for the splitter, meaning that the buﬀer insertion for wirelength
constraints is generally unavoidable. Since the node stage l builds the bridge between
technology legalization and placement, we can divide the problem into two sub-problems
to assist the model solution: (i) stage assignment and (ii) position assignment. We first
assign the stages to all nodes and then assign the positions to all nodes, followed by the
wirelength violation checking. When the violations exist, we can constrain a bigger stage
gap during the stage assignment and a smaller distance during the position assignment
between the source and sink of the violated connection to reduce the violations.
2.2    Multiphase Clocking-oriented Clock-aware Length-Matching
       Placement for RSFQ Circuits
Ensuring the correct operation of RSFQ circuits requires inserting numerous D flip-flops
(DFFs), which substantially increase circuit area and energy dissipation. Recent studies
have demonstrated that the multiphase clocking scheme can eﬀectively reduce the number
of required DFFs. Despite these advantages, existing placement tools do not support
multiphase clocking RSFQ circuits. As shown in Fig. 4(a), the single-phase clocking
RSFQ circuits usually adopt a stage-driven pipelined layout. Logic gates are arranged
in columns according to their stages, with gates of the same stage placed in the same
column. Unlike single-phase clocking RSFQ circuits, where all fanin gates of a logic gate
must be at the same stage, logic gates in multiphase clocking RSFQ circuits may have
fanin gates with diﬀerent stages (Fig. 4(b)), which pose challenges for the placement of
multiphase clocking RSFQ circuits. To address this limitation, we plan to develop a novel
multiphase clocking-oriented length-matching placement framework for RSFQ circuits.

                                                   3
We will design two new RSFQ cells, TFFDO (Fig. 4(e)) and TFFDE (Fig. 4(f)), to
simplify the clock network in two-phase clocking designs. Meanwhile, we plan to propose a
maximum flow-based method that constructs the clock distribution column by column and
a dynamic programming algorithm that minimizes the total vertical wirelength (TVWL)
while maintaining fixed placement orders.
                                    2                                3
                                X                  X
                      TVWL =        4lvvc + lvmc +       vd
                                                       (lu,v    md 5
                                                             + lu,v ) .               (8)
                                         v2V                   u2FI(v)


2.3      Shape-controllable Placement for RSFQ Circuits
Since most RSFQ logic gates require synchronization with a single clock, RSFQ circuits
have a clock-driven gate-level pipelining architecture in nature. To meet strict timing
constraints in logic, the insertion of numerous DFFs is required. This not only increases
circuit power and incurs a prohibitive area penalty but also causes a very slender circuit
layout and a large width-to-height ratio. However, practical chip integration demands
explicit control of layout shape. Hence, we plan to develop a shape-controllable placement
algorithm for RSFQ circuits. To flexibly adjust the layout’s aspect ratio and improve the
utilization of routing resources, after obtaining the initial placement, we fold the columns
of logic gates into three rows: 1th row includes 1–i columns, 2th row includes (i + 1)–k
columns, and 3th row includes (k + 1)–n columns, as shown in ??. Here, i and k are
determined by a greedy method aiming at a given aspect ratio. A dynamic programming-
based approach is adopted to merge columns across diﬀerent rows, thereby increasing
the routing space between columns. For rows with fewer columns, columns composed of
dummy nodes are inserted to merge them with the columns in the row above.
2.4      End-to-End Design Flow for Superconducting Logic Circuits
Although superconducting logic circuits have received more and more attention and many
EDA methods have been proposed for superconducting logic circuits, the community still
lacks an open-source, end-to-end EDA tool, and even widely available point tools are
scarce. Currently, we have done numerous research works on superconducting logic cir-
cuit design automation methods, including logic synthesis, placement, and routing, as
depicted in Fig. 5. Hence, we plan to integrate our point tools to form a unified, end-to-
end flow that spans the complete design stack from RTL to physical layout. The resulting
framework will be released as open-source software to facilitate community uptake, accel-
erate methodological progress, and support the practical deployment of superconducting
logic in energy-eﬃcient computing systems.

3      Research Plan
We will incrementally develop the proposed methods and evaluate the resulting end-to-
end flow using open-source superconducting logic benchmarks. The planning schedule of
this project is shown in the following Gantt Chart:
                                                                      Months
                                                 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23
      Technology Legalization-aware Placement
       Multiphase Clocking-oriented Placement
                 Shape-controllable Placement
                     End-to-End Design Flow
                          Integration and Test

                               Figure 1. Gantt chart for the research plan.


                                                          4
        CMOS                          RSFQ                    AQFP
                                                              Ix+Id                A
                                                                                                                                                                                                                                                                                                                                              + Id
                 A                             A


                                                                                                                                                                                                                                     + Id                                                                                                     - Id
                                          Splitter                             Splitter

                                                                                                                                                                                                                                     - Id                                                       1                       0               a
            B            C                B          C                         B           C

                                                                                                                                                                      1                           0                            a                                                                                                        b
                                             (a)
                 a bc                                buffer a                      bc                                                                                                                                          b                                                                                                        c

                                                                                                                                                                                                                               c




                     q                                                         q
                                                                                                                                                                              x                           x                    q                                                                                                        q

                (b)                                                 (c)                                                                                                                           (d)                                                                                                                   (e)
Figure 2. (a) The multi-fan-out comparison between CMOS, RSFQ and AQFP. (b)-(e) A gate-
level schematic shows the necessity of buﬀer insertion for correct operation in the AQFP circuit
with the function q = abc, where the 4-phase clocking scheme is applied. (b) and (c) are
schematics before and after buﬀer insertion, respectively, where Ix1 and Ix2 are AC clock signals
with a phase separation of 90 , and Id is the DC input, which applies an oﬀset flux of half an
SFQ to each logic gate. (d) and (e) are timing schematics of (b) and (c), respectively.

                         B                                                     B                                             a                           b                                                                     a                       b                                                                                b
     MAJ                                       MAJ
                                                                                                                                                                                                                                                                                                             5 buffers
                                                                                                                                                                  Row
                                                                                                                                                                 index                                                              e                                                                              a e
                                                          Cell spacing
                                             B                                                                                                                                                                  Buffer row with 6 buffers
                     MAJ                                  Zigzag spacing
                                                          Wirelength                                              c                                      d                                                          c                                  d                                                 c                              d

                                   (a)                                                                                                  (b)                                                                                              (c)                                                                                  (d)
Figure 3. (a) The illustration of spacing constraints in the AQFP placement. (b)-(d) Buﬀer
insertion for wirelength violations. (b) The length of the orange wire exceeds its maximum length.
(c) Existing methods [28–30] require a buﬀer row with 6 buﬀers to eliminate this wirelength
violation. (d) The expected solution requires only 5 buﬀers to eliminate the wirelength violation.

                                                                                                                                                                                                        IB1             IB2                 IB3         IB7                                     IB1          IB2                 IB3    IB7
                                                              Epoch                                                                                                                                                                                             RD2                                                                           RD2
                                              s Splitter                            S=0                    S=0            S=1
Stage      1                      2              3            Phase                 φ=0                    φ=1            φ=0                                                   A                      J1               J2          R3                               Q1 A                      J1            J2                                 Q1
                                                                                                                                                                                                                                             R10




                                                                                                                                                                                                                                                                                                                                  R10



                                                                                                                                                                                         R1                                                 J3         J10                         R1                                   R3       J3     J10
                                                              Stage                 σ=0                    σ=1            σ=2                                                                            R2                  IB4    J4                                                            R2              IB4   J4
                                                                                                                                                     A/Q0,Q1
          AND                   DFF                                                 AND
                                              OR         O1                                                                OR       O1 0              A/Q1
                                                                                                                                                                      1
                                                                                                                                                                                                                                    R4                                                                                  R4
                                                                                                                                                                                                           J5            R7          J6     R8                                                                R8            J5   R7
                                                                                                                                                     (c)
                                AND                                                                        AND           Crossing                                                                                                                                                                       J6
 a         DFF       s                                        a                                    s                                                                                                     IB5                                                                                                            IB5
                                                                                                                                                                                                                                                                                                                                      ‘0’
                                                                                                                          column
                                                                                                                                                                                    RD1 IB6                                    ‘0’                                            RD1
                                                                                                                                                                                                                                                                                        IB6
                                                                                                                                                                                                                         J7                 J8 ‘1’                                                            J8                 J7
 b s       XOR           s      XOR           DFF        O2   b s                   XOR                s   XOR                      O2
                                                                                                                                             0
                                                                                                                                                      A/Q1
                                                                                                                                                                      1
                                                                                                                                                                                Q0
                                                                                                                                                                                                  J9           R9
                                                                                                                                                                                                                               LS                                           Q0
                                                                                                                                                                                                                                                                                          J9            R9 ‘1’
                                                                                                                                                                                                                                                                                                              LS
 c   s                                                        c   s                                                                                  A/Q0,Q1
                                                                                                                                                      A/Q1


                     (a)                                                                           (b)                                               (d)                                                                      (e)                                                                                  (f)
Figure 4. Implementations of a 1-bit RSFQ full adder using (a) single-phase clocking, which
requires 3 path-balancing DFFs, and (b) two-phase clocking, which eliminates the need for path-
balancing DFFs. (e)–(f) show the JJ-level schematics of TFFDO and TFFDE, respectively, while
(c) and (d) show their corresponding state transform diagrams.

                                                                                   Logic Synthesis (ASPDAC'23, DATE'23, TCAD)                                                                                                                Physical Design (ICCAD'23)
                                 RTL
                                Design                                     AOI-based                                 MIG-based                                  Buffer and                                                            4-Phase / Delay-Line                                              Channel
                                                                          Optimizaiton                               Optimizaiton                            Splitter Insertion                                                     Clocking-based Placement                                            Routing

                                                                                                                                                                                                                                                   Using 4-phase clocking
                              module fullAdder1 (             a            |           &               & AND | OR                   a    S       |       S        &             B Buffer S Splitter
                                x, y, cin,                    b           &                    |               carry                                              B       |     S    B        B        carry
                                s, cout                                                                                             b    S       &       S
                             );                                                        |               &                                                          |       B     B    &
                                input x, y, cin;              c                        &                   |   sum                  c    B       B       S        &       B     B    B        |        sum
                                output s, cout;
                                                              MIG-based




                                assign s = (x ^ y) ^ cin;                                  a               M Majority               a    S           B       B
                                assign cout = (y & cin)
                                               | (x & y)
                                                                                           b           M   M     sum                b    S           M       B    M       sum
                                               | (x & cin);
                              endmodule // 1-bit full adder        Optimization
                                                                                           c           M         carry              c    S           M       S    B       carry                                                                    Buffer row               Using delay-line clocking




                                                           Figure 5. Design flow of superconducting logic circuits.


                                                                                                                                                                      5
                          Pathways to Impact Statement
With the ongoing advancement of multi-layer processes, it is now feasible to fabricate
thousands of superconducting logic gates on a chip surface measuring only a few square
millimeters, achieving densities nearing 0.3 million gates per square centimeter. This
development has led to a significant increase in the complexity of chip design. Consequently,
substantial efforts have been devoted over the past decade to the creation of design automation
solutions that facilitate the large-scale integration of superconducting logic circuits. These
solutions encompass various aspects, including logic synthesis and physical placement.

For logic synthesis, existing methodologies focus on optimizing either the size or depth of the
circuit, followed by the insertion of splitters and buffers. However, optimizing for size or depth
alone may not yield the most effective results. Regarding physical placement, both
superconducting Rapid Single Flux Quantum (RSFQ) and Adiabatic Quantum Flux Parametron
(AQFP) logic circuits impose stringent timing constraints and necessitate the implementation
of multi-phase clocking schemes. Current methodologies prioritize meeting timing constraints
initially during the logic design phase and subsequently during the physical design phase, often
resulting in the repetitive insertion of numerous buffers. As the scale of superconducting logic
circuits expands, the proliferation of buffers becomes increasingly untenable.

Accordingly, this project proposes the development of SCFlow, an end-to-end design
automation framework aimed at the automatic design and implementation of superconducting
logic circuits. SCFlow integrates the entire process from RTL to layout, including logic
synthesis, technology legalization (including buffer and splitter insertion), and clock-aware
placement and routing. This comprehensive integration seeks to minimize buffer counts, reduce
area, latency, and energy consumption, while simultaneously shortening design turnaround
times and enhancing reproducibility. SCFlow will be released as open-source software to
encourage community engagement, accelerate methodological advancements, and support the
practical implementation of superconducting logic in energy-efficient computing systems.

Impact on Society: The recent proliferation of high-performance computing systems, such as
supercomputers and data centers, coupled with the realization that CMOS-based exascale
supercomputers necessitate power consumption in the tens of megawatts, has redirected interest
towards non-CMOS technologies that promise energy-efficient computing solutions.
Superconducting logic, as an application of low-temperature superconductivity, emerges as a
promising candidate for the development of highly energy-efficient computing systems due to
its remarkable energy efficiency. Nevertheless, the complexity of these designs necessitates the
availability of design automation tools. This project introduces SCFlow, an end-to-end open-
source framework designed to automate the design and implementation of superconducting
logic circuits. We believe that SCFlow will have a transformative impact on the design
processes of future superconducting circuits.




                                                14
References
 [1] J. Huang, R. Fu, X. Ye, and D. Fan, “A survey on superconducting computing tech-
     nology: Circuits, architectures and design tools,” CCF Transactions on High Perfor-
     mance Computing, vol. 4, no. 1, 2022.
 [2] K. Likharev and V. Semenov, “RSFQ logic/memory family: a new josephson-junction
     technology for sub-terahertz-clock-frequency digital systems,” IEEE Transactions on
     Applied Superconductivity, vol. 1, no. 1, pp. 3–28, 1991.
 [3] W. Chen, A. Rylyakov, V. Patel, J. Lukens, and K. Likharev, “Rapid single flux
     quantum T-flip flop operating up to 770 GHz,” IEEE Transactions on Applied Su-
     perconductivity, vol. 9, no. 2, pp. 3212–3215, 1999.
 [4] N. Yoshikawa and Y. Kato, “Reduction of power consumption of RSFQ circuits by
     inductance-load biasing,” Superconductor Science and Technology, vol. 12, no. 11,
     pp. 918–920, 1999.
 [5] M. Tanaka, M. Ito, A. Kitayama, T. Kouketsu, and A. Fujimaki, “18-GHz, 4.0-aJ/bit
     operation of ultra-low-energy rapid single-flux-quantum shift registers,” Japanese
     Journal of Applied Physics, vol. 51, 2012.
 [6] Q. P. Herr, A. Y. Herr, O. T. Oberg, and A. G. Ioannidis, “Ultra-low-power super-
     conductor logic,” Journal of Applied Physics, vol. 109, no. 10, p. 103903, 2011.
 [7] O. A. Mukhanov, “Energy-eﬃcient single flux quantum technology,” IEEE Transac-
     tions on Applied Superconductivity, vol. 21, no. 3, 2011.
 [8] N. Takeuchi, D. Ozawa, Y. Yamanashi, and N. Yoshikawa, “An adiabatic quantum
     flux parametron as an ultra-low-power logic device,” Superconductor Science and
     Technology, vol. 26, no. 3, p. 035010, 2013.
 [9] R. Cai, O. Chen, A. Ren, N. Liu, N. Yoshikawa, and Y. Wang, “A buﬀer and split-
     ter insertion framework for adiabatic quantum-flux-parametron superconducting cir-
     cuits,” in Proc. ICCD, pp. 429–436, 2019.
[10] R. Fu, Z.-M. Zhang, G.-M. Tang, J. Huang, X.-C. Ye, D.-R. Fan, and N.-H. Sun,
     “Design automation methodology from rtl to gate-level netlist and schematic for
     RSFQ logic circuits,” in GLSVLSI, pp. 145–150, 2020.
[11] C.-Y. Huang, Y.-C. Chang, M.-J. Tsai, and T.-Y. Ho, “An optimal algorithm for split-
     ter and buﬀer insertion in adiabatic quantum-flux-parametron circuits,” in Proc. IC-
     CAD, pp. 1–8, 2021.
[12] S.-Y. Lee, H. Riener, and G. De Micheli, “Beyond local optimality of buﬀer and
     splitter insertion for AQFP circuits,” in Proc. DAC, pp. 445–450, 2022.
[13] R. Fu, M. Wang, Y. Kan, N. Yoshikawa, T.-Y. Ho, and O. Chen, “A global optimiza-
     tion algorithm for buﬀer and splitter insertion in adiabatic quantum-flux-parametron
     circuits,” in Proc. ASPDAC, pp. 769–774, 2023.
[14] R. Fu, M. Wang, Y. Kan, O. Chen, N. Yoshikawa, B. Yu, and T.-Y. Ho, “Buﬀer
     and splitter insertion for adiabatic quantum-flux-parametron circuits,” IEEE TCAD,
     vol. 44, no. 3, pp. 975–988, 2025.
[15] M. Zhou, R. Fu, R. Zhang, X. Ye, T.-Y. Ho, and J. Huang, “An optimal DFF-
     oriented technology legalization algorithm for rapid single-flux-quantum circuits,” in
     Proc. GLSVLSI, 2025.

                                            6
[16] R. Cai, O. Chen, A. Ren, N. Liu, C. Ding, N. Yoshikawa, and Y. Wang, “A majority
     logic synthesis framework for adiabatic quantum-flux-parametron superconducting
     circuits,” in Proc. GLSVLSI, 2019.
[17] E. Testa, S.-Y. Lee, H. Riener, and G. De Micheli, “Algebraic and boolean optimiza-
     tion methods for AQFP superconducting circuits,” in Proc. ASPDAC, pp. 779–785,
     2021.
[18] G. Meuli, V. Possani, R. Singh, S.-Y. Lee, A. T. Calvino, D. S. Marakkalage, P. Vuil-
     lod, L. Amaru, S. Chase, J. Kawa, and G. De Micheli, “Majority-based design flow
     for AQFP superconducting family,” in Proc. DATE, pp. 34–39, 2022.
[19] R. Fu, J. Huang, M. Wang, Y. Nobuyuki, B. Yu, T.-Y. Ho, and O. Chen, “BOMIG: A
     majority logic synthesis framework for AQFP logic,” in Proc. DATE, pp. 1–2, 2023.
[20] A. T. Calvino and G. De Micheli, “Algebraic and Boolean methods for SFQ super-
     conducting circuits,” in Proc. ASPDAC, pp. 588–593, 2024.
[21] R. Bairamkulov, S.-Y. Lee, A. T. Calvino, D. S. Marakkalage, M. Yu, and
     G. De Micheli, “Technology-aware logic synthesis for superconducting electronics,”
     in Proc. DATE, pp. 1–6, 2024.
[22] N. Kito, K. Takagi, and N. Takagi, “A fast wire-routing method and an automatic
     layout tool for RSFQ digital circuits considering wire-length matching,” IEEE Trans-
     actions on Applied Superconductivity, vol. 28, no. 4, pp. 1–5, 2018.
[23] C.-C. Wang and W.-K. Mak, “A novel clock tree aware placement methodology for
     single flux quantum logic circuits with maximum path length consideration,” IEEE
     Transactions on Applied Superconductivity, vol. 32, no. 5, pp. 1–12, 2022.
[24] S. Chen, R. Fu, J. Huang, Z. Zhang, X. Ye, T.-Y. Ho, and D. Fan, “JPlace: A
     clock-aware length-matching placement for rapid single-flux-quantum circuits,” in
     Proc. DATE, pp. 1–6, 2024.
[25] R. Bairamkulov, M. Yu, and G. De Micheli, “Unleashing the power of T1-cells in
     SFQ arithmetic circuits,” in Proc. DAC, 2024.
[26] X. Li, M. Pan, T. Liu, and P. A. Beerel, “Multi-phase clocking for multi-threaded
     gate-level-pipelined superconductive logic,” in Proc. ISVLSI, pp. 62–67, 2022.
[27] Y.-C. Chang, H. Li, O. Chen, Y. Wang, N. Yoshikawa, and T.-Y. Ho, “ASAP: An
     analytical strategy for AQFP placement,” in Proc. ICCAD, pp. 1–7, 2020.
[28] H. Li, M. Sun, T. Zhang, O. Chen, N. Yoshikawa, B. Yu, Y. Wang, and Y. Lin,
     “Towards AQFP-capable physical design automation,” in Proc. DATE, pp. 954–959,
     2021.
[29] P. Dong, Y. Xie, H. Li, M. Sun, O. Chen, N. Yoshikawa, and Y. Wang, “TAAS:
     A timing-aware analytical strategy for AQFP-capable placement automation,” in
     Proc. DAC, pp. 1321–1326, 2022.
[30] R. Fu, O. Chen, B. Yu, N. Yoshikawa, and T.-Y. Ho, “DLPlace: A delay-line clocking-
     based placement framework for AQFP circuits,” in Proc. ICCAD, pp. 1–8, 2023.




                                            7
1. Participate in the Chinese Academy of Sciences Strategic Priority Research Program
   (Class A) from 2019 to 2021, mainly engaged in EDA research and system integration
   and testing of superconducting logic circuits.
2. Assist my supervisor in successfully applying for the 2022 General Research Fund
   (GRF) from the Research Grants Council (RGC) of Hong Kong, China.
3. Assist my supervisor in successfully applying for the second batch of five major
   challenges of industrial software: "Generation of Component Library Based on Pre-
   trained AI Model" released by Huawei, https://www.disa.org.cn/Content-203.html.
4. Collaborate with XXX Group at XXX University to complete the Adiabatic Quantum-
   Flux-Parametron logic circuit design from 2022.
5. Collaborate with XXX Group at XXX University to complete the Reversible
   Quantum-Flux-Parametron logic circuit design from 2023 to 2024.
6. Collaborate with XXX Group at XXX University to complete the "Richness Design of
   Industrial Standard Cells" from 2024.
7. Lead the project “ABCOv100: 3D Architecture Floorplan with Multi-Objective
   Optimization” from Huawei, 2025.
8. Lead the project “LeafCell Layout Automation” from Huawei, 2025.
1. Class Monitor at XXX University from 2014 to 2018.
2. Lead the project “ABCOv100: 3D Architecture Floorplan with Multi-Objective
   Optimization” from Huawei, 2025.
3. Lead the project “LeafCell Layout Automation” from Huawei, 2025.
4. Guide three master students on the research and publish 1 paper at DATE’24, 1
   paper at GLSVLSI’24, 1 paper at DC’25, and 2 journals under review.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
l    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
l    The nominee should complete Part A of the referee’s report.
l    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
l    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
l    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) FU
                        (Other name) Rongliang
Host Faculty / School / Department / Department of Computer Science and Engineering, The Chinese
Division / Unit ^ :                  University of Hong Kong

^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
l   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
l   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
l   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
l   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
l   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
l   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
l   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known Rongliang Fu since 2021 in my capacity as an associate professor at the Chinese University of
Hong Kong, and now I am a PhD academic committee member of Rongliang Fu. Noting his work on EDA for
superconducting logic circuits, I recommended him to his PhD supervisor and subsequently provided guidance
on his EDA research, particularly in logic synthesis. Together, we developed a technology mapping–aware,
area-efficient standard-cell library extension framework, published in IEEE TCAD.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                               Excellent      Very Good        Good                Average          No basis
                               (top 5%)       (top 6-20%) (top 21-50%)            or below            for
                                                                                 (lower than       judgment
                                                                                    50%)
Intellectual capacity              R               ☐                 ☐                 ☐               ☐
Ability for conducting
scholastic research
                                   R               ☐                 ☐                 ☐               ☐
Knowledge of proposed
research study
                                   R               ☐                 ☐                 ☐               ☐
Analytical power and
reasoning
                                   R               ☐                 ☐                 ☐               ☐
Judgment                           ☐               R                 ☐                 ☐               ☐
Imagination and
originality
                                   R               ☐                 ☐                 ☐               ☐
Motivation and
perseverance
                                   R               ☐                 ☐                 ☐               ☐
Skills of writing and
argumentation
                                   R               ☐                 ☐                 ☐               ☐
Capacity for independent
work
                                   R               ☐                 ☐                 ☐               ☐
Reliability and sense of
responsibility
                                   R               ☐                 ☐                 ☐               ☐
Leadership experience              ☐               R                ☐                 ☐                ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
Rongliang Fu exhibits strong research ability and clear potential for independent, impactful work in electronic
design automation (EDA), particularly for superconducting logic circuits and logic synthesis.
First, he has achieved research excellence and productivity. During his PhD, he independently designed and
implemented an end-to-end flow for superconducting logic circuits, covering logic synthesis, placement, and
routing, demonstrating both engineering capability. He has an outstanding publication record as first author:
DAC (2), ICCAD (3), DATE (1), ASPDAC (1), IEEE TC (1), and IEEE TCAD (3), among a total of 24
publications.
Second, He possesses technical strength and learning agility. From our first meeting, he showed a clear grasp of
EDA hotspots and open problems, and quickly translated literature insights into concrete research directions. He
rapidly mastered practical challenges in logic synthesis and moved from understanding to contribution in a short
time. He has a solid foundation in computer science and core EDA methodologies, enabling him to connect
algorithms to deployable tool flows.
Given his track record, curiosity, and ability to drive advances at the intersection of superconducting logic
circuits and EDA, I strongly support his JRFS application and believe he will thrive in Hong Kong’s research
environment.
                                                       3
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
    4. What is your overall recommendation for this nominee?

                  R      Recommend very strongly
                  ☐      Recommend strongly
                  ☐      Recommend with reservation
                  ☐      Do not recommend

     Name of Referee: BEI YU                                          Title: Prof
                             (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*
     University / Organisation: The Chinese University of Hong Kong
     Position: Professor
     E-mail Address: <EMAIL>
     Telephone Number#: +852-39438435
     (# Please provide country code and area code, e.g. +86-10-1234567)
l     * Please delete as appropriate


    Declaration by the Referee:
    I hereby declare that
    R I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
    R I have no conflict(s) of interest in the capacity of academic referee in this nomination.


     Signature:                                                  Date: Aug. 27, 2025




    This document is strictly CONFIDENTIAL. Please send it directly to the supporting
    university (under confidential cover) whose details are given on the cover page.




                                                          4
                                                                   JRFS-3 Academic Referee’s Report Form (Aug 2025)
