                                                      KE XU (徐克)
                                    Research Associate - Machine Learning Molecular Simulation



                                    EDUCATION
                                    Ph. D. - Biophysics & Soft Condensed Matter               2019 - 2023
                                    Xiamen University - Xiamen, Fujian (China)
                                    Title of PhD thesis: Mechanical Properties of Clathrate Hydrates at the
CONTACT                             Micro- and Nano-Scale

    <EMAIL>               M. Tech. - Hydraulic Engineering                          2016 - 2019
                                    Northwest A&F University - Xi’an, Shanxi (China)
   Kick-H
                                    Passed with 81.5%. Thermal transport of 2D materials
D   Ke Xu

³   Ke XU (XMU)
                                    B. Tech. - Hydraulics and Hydropower Engineering          2012 - 2016
    Department of Electronics En-   Northwest A&F University - Xi’an, Shanxi (China)
    gineering, The Chinese Uni-
+   versity of Hong Kong, Hong      Passed with 3.13 GPA.
    Kong, China

                                    EXPERIENCES
                                    Research Associate                                        2023 - 2025
                                    Electronic Engineering Department, The Chinese
                                    University of Hong Kong - Hong Kong SAR (China)

                                    Collaborators of Professor <PERSON><PERSON><PERSON>’s research group


                                    Two exchanges/studies, each lasting 15 days.              2017 - 2018
                                    XiaMen University Malaysia,       Kuala Lumpur
                                    (Malaysia)

                                    Exchange student                                        May - Aug 2021
SKILLS                              Huazhong University of Science and Technology,
                                    Wuhan (China)
Thermal Transport        9+ yrs
                                    REPRESENTATIVE WORKS
Mechanics Properties     8+ yrs
                                    NEP-MB-pol: a unified machine-learned framework               2025
                                    for fast and accurate prediction of water’s thermo-
Molecular Dynamics       10+ yrs    dynamic and transport properties
                                    npj Computational Materials 11, 279

Machine Learning         7+ yrs
                                    Critical Size Transitions in Silicon Nanowires: Amor-         2025
                                    phization, Phonon Hydrodynamics, and Thermal
Ab-initio calculation    5+ yrs     Conductivity
                                    The Journal of Physical Chemistry Letters 16 (33),
                                    8580-8587
Python                   10+ yrs
                                    GPUMD 4.0: A high-performance molecular dynam-                2025
                                    ics package for versatile materials simulations with
Linux                    10+ yrs
                                    machine-learned potentials
                                    Materials Genome Engineering Advances e70028
ACHIEVEMENTS                        Accurate prediction of heat conductivity of water by              2023
                                    a neuroevolution potential
                                    the Journal of Chemical Physics 158, 204114
Scholar
                                    Gradient nano-grained graphene as 2D thermal rec-                 2022
Cite: 1325; h-index: 20             tifier: A molecular dynamics based machine learn-
No. of publiactions: 45 (1st: 14)   ing study
                                    Applied Physics Letters 121 (13), 133501

Achievement in B. Tech.             PROJECT INVOLVED
Advanced Individual of Social
Work in 2013                        Molecular Dynamics Simulation Platform Based on                   2024
                                    Artificial Intelligence Technology
                                    National Science and Technology Innovation 2030
Achievement in M. Tech.
                                    Major Project
Outstanding Graduates (2019)
                                    Funding received: RMB 500,000. Responsible for the AIP of MLP.
National Scholarship (2018)

                                    Phonon Hydrodynamics in One-dimensional Crys-                     2023
Achievement in Ph. D.               tals Towards High Thermal Conduction
Outstanding Graduates (2023)        RGC General Research Fund

National Scholarship (2022)         Simulating the diameter-dependent thermal conductivity

Outstanding      Merit    Student
(2022)                              Multi-field carrier coupling and heat transport regu-             2023
                                    lation mechanism in nanomaterials
                                    Experimental & simulation calculations
Achievement in CUHK
Research Fellowship Scheme          The multi-field carrier coupling mechanism of nano interface materials
2023-24 (Nomination)
                                    Study on Mechanical Properties of Methane-CO2                     2022
                                    Heterogeneous Hydrate and Its Sediments
Skills Certificate
                                    National Natural Science Foundation
Second- and Third-level Certifi-
cate for National Computer          PATENT
                                    A method for controlling directional transport of                 2021
Personal Hobby
                                    fluid in nanochannels
Badminton, Swimming, Frisbee        China, CN202110175003.9

                                    WORKSHOPS & CONFERENCES
                                    The First GPUMD Developer Conference                        June 2025
                                    Member of the Academic Committee

                                    Ningbo Institute of Materials Technology & Engineering - Ningbo, China


                                    The 1st GPUMD&NEP Academic Seminar                              July 2024
                                    Invited Speech

                                    Bohai University - Jinzhou, China


                                    Forum for Outstanding Young Scholars                        June 2023
                                    Invited Speech

                                    Zhejiang University Quzhou Research Institute - Quzhou, China


                                    Multiscale Computing Youth Academic Forum                   June 2023
                                    Invited Speech

                                    University of Science and Technology Beijing - Beijing, China
1      Ke XU Publication list
International refereed journals (First Author)
 [1]   K. Xu, T. Liang, N. Xu, P. Ying, S. Chen, N. Wei, J. Xu, and Z. Fan. “NEP-MB-Pol: A Unified
       Machine-Learned Framework for Fast and Accurate Prediction of Water’s Thermodynamic and
       Transport Properties”. In: npj Computational Materials 11.1 (2025), 279, IF=12.3(Q1).
 [2]   K. Xu, Y. Li, D. Ding, T. Liang, J. Wu, and J. Xu. “Critical Size Transitions in Silicon Nanowires:
       Amorphization, Phonon Hydrodynamics, and Thermal Conductivity”. In: The Journal of Physical
       Chemistry Letters 16.33 (2025), 8580–8587, IF=4.8(Q1).
 [3]   K. Xu, Y. Hao, T. Liang, P. Ying, J. Xu, J. Wu, and Z. Fan. “Accurate prediction of heat conduc-
       tivity of water by a neuroevolution potential”. In: The Journal of Chemical Physics 158.20 (2023),
       204114, IF=4.4(Q1).
 [4]   K. Xu, T. Liang, Y. Fu, Z. Wang, Z. Fan, N. Wei, J. Xu, Z. Zhang, and J. Wu. “Gradient nano-
       grained graphene as 2D thermal rectifier: A molecular dynamics based machine learning study”.
       In: Applied Physics Letters 121.13 (2022), 133501, IF=4.0(Q2).
 [5]   T. Li, K. Xu, L. Shi, J. Wu, J. He, and Z. Zhang. “Dual-ionic hydrogels with ultralong anti-
       dehydration lifespan and superior anti-icing performance”. In: Applied Materials Today 26 (2022),
       101367, IF=8.3(Q1), Co–first author.
 [6]   K. Xu, Y. Lin, Q. Shi, T. Li, Z. Zhang, and J. Wu. “Role of mechanical deformation in the thermal
       transport of sI-type methane hydrate”. In: Physical Chemistry Chemical Physics 24.9 (2022), 5479–
       5488, IF=3.3(Q2).
 [7]   K. Xu, S. Deng, T. Liang, X. Cao, M. Han, X. Zeng, Z. Zhang, N. Yang, and J. Wu. “Efficient
       mechanical modulation of the phonon thermal conductivity of Mo6 S6 nanowires”. In: Nanoscale
       14.8 (2022), 3078–3086, IF=6.7(Q1), Co–first author.
 [8]   K. Xu, T. Liang, Z. Zhang, X. Cao, M. Han, N. Wei, and J. Wu. “Grain boundary and misorientation
       angle-dependent thermal transport in single-layer MoS2 ”. In: Nanoscale 14.4 (2022), 1241–1249,
       IF=6.7(Q1).
 [9]   K. Xu, Y. Lin, T. Li, Y. Fu, Z. Zhang, and J. Wu. “Structural and mechanical stability of clathrate
       hydrates encapsulating monoatomic guest species”. In: Journal of Molecular Liquids 347 (2022),
       118391, IF=6(Q1).
[10]   K. Xu, L. Yang, J. Liu, Z. Zhang, and J. Wu. “Mechanical Properties of CH4 –CO2 Heteroclathrate
       Hydrates”. In: Energy & Fuels 34.11 (2020), 14368–14378, IF=5.3(Q1).
[11]   K. Xu, A. J. Gabourie, A. Hashemi, Z. Fan, N. Wei, A. B. Farimani, H.-P. Komsa, A. V. Krashenin-
       nikov, E. Pop, and T. Ala-Nissila. “Thermal transport in MoS2 from molecular dynamics using
       different empirical potentials”. In: Physical Review B 99.5 (2019), 054303, IF=3.7(Q2).
[12]   K. Xu, J. Zhang, X. Hao, N. Wei, X. Cao, Y. Kang, and K. Cai. “Interfacial thermal conductance
       of buckling carbon nanotubes”. In: AIP Advances 8.6 (2018), 065116, IF=1.6(Q4).
[13]   K. Xu, J. Zhang, X. Hao, C. Zhang, N. Wei, and C. Zhang. “Wetting properties of defective
       graphene oxide: a molecular simulation study”. In: Molecules 23.6 (2018), 1439, IF=4.6(Q2).
[14]   K. Xu, Z. Fan, J. Zhang, N. Wei, and T. Ala-Nissila. “Thermal transport properties of single-layer
       black phosphorous from extensive molecular dynamics simulations”. In: Modelling and Simulation
       in Materials Science and Engineering 26 (2018), 085001, IF=2.4(Q3).


Chinese journals (First Author)
[15]   K. Xu, H. Bu, S. Pan, E. Lindgren, Y. Wu, Y. Wang, J. Liu, K. Song, B. Xu, Y. Li, T. Hainer,
       L. Svensson, J. Wiktor, R. Zhao, H. Huang, C. Qian, S. Zhang, Z. Zeng, B. Zhang, B. Tang, Y.
       Xiao, Z. Yan, J. Shi, Z. Liang, J. Wang, T. Liang, S. Cao, Y. Wang, P. Ying, N. Xu, C. Chen, Y.
       Zhang, Z. Chen, X. Wu, W. Jiang, E. Berger, Y. Li, S. Chen, A. J. Gabourie, H. Dong, S. Xiong,
       N. Wei, Y. Chen, J. Xu, F. Ding, Z. Sun, T. Ala-Nissila, A. Harju, J. Zheng, P. Guan, P. Erhart,
       J. Sun, W. Ouyang, Y. Su, and Z. Fan. “GPUMD 4.0: A high-performance molecular dynamics
       package for versatile materials simulations with machine-learned potentials”. In: Materials Genome
       Engineering Advances n/a.n/a (), e70028.


                                                    1
[16]   K. Xu, Y. Lin, Q. Shi, Y. Fu, Y. Yang, Z. Zhang, and J. Wu. “Mechanical enhancement and
       weakening in Mo6 S6 nanowire by twisting”. In: Chinese Physics B 32.4 (2023), 046204, Language.
       English, IF=1.7(Q3).


International refereed journals (Co-author)
[17]   P. Ying, W. Zhou, L. Svensson, E. Berger, E. Fransson, F. Eriksson, K. Xu, T. Liang, J. Xu, B.
       Song, S. Chen, P. Erhart, and Z. Fan. “Highly efficient path-integral molecular dynamics simulations
       with GPUMD using neuroevolution potentials: Case studies on thermal properties of materials”.
       In: The Journal of Chemical Physics 162.6 (Feb. 2025), p. 064109.
[18]   C. Lu, Z. Li, X. Sang, Z. Fan, X. Xu, Y. Zhang, K. Xu, Y. Cheng, J. Zhao, J.-C. Zheng, and
       N. Wei. “Stress-Driven Grain Boundary Structural Transition in Diamond by Machine Learning
       Potential”. In: Small 21.16 (2025), p. 2409092.
[19]   Y. Xiao, Y. Liu, Z. Tan, B. Zhang, K. Xu, Z. Fan, S. Chen, S. Xiong, and H. Dong. In: Phys. Rev.
       Mater. 9 (8 Aug. 2025), p. 084603.
[20]   T. Liang, W. Jiang, K. Xu, H. Bu, Z. Fan, W. Ouyang, and J. Xu. “PYSED: A tool for extracting
       kinetic-energy-weighted phonon dispersion and lifetime from molecular dynamics simulations”. In:
       Journal of Applied Physics 138.7 (Aug. 2025), p. 075101.
[21]   H. Dong, Y. Shi, P. Ying, K. Xu, T. Liang, Y. Wang, Z. Zeng, X. Wu, W. Zhou, S. Xiong, S. Chen,
       and Z. Fan. “Molecular dynamics simulations of heat transport using machine-learned potentials:
       A mini-review and tutorial on GPUMD with neuroevolution potentials”. In: Journal of Applied
       Physics 135.16 (2024), p. 161101.
[22]   K. Song, R. Zhao, J. Liu, Y. Wang, E. Lindgren, Y. Wang, S. Chen, K. Xu, T. Liang, P. Ying,
       N. Xu, Z. Zhao, J. Shi, J. Wang, S. Lyu, Z. Zeng, S. Liang, H. Dong, L. Sun, Y. Chen, Z. Zhang,
       W. Guo, P. Qian, J. Sun, P. Erhart, T. Ala-Nissila, Y. Su, and Z. Fan. “General-purpose machine-
       learned potential for 16 elemental metals and their alloys”. In: Nature Communications 15.1 (Nov.
       2024), p. 10208.
[23]   G. Li, J. Tang, J. Zheng, Q. Wang, Z. Cui, K. Xu, J. Xu, T.-H. Liu, G. Zhu, R. Guo, and B. Li.
       “Convergent thermal conductivity in strained monolayer graphene”. In: Phys. Rev. B 109 (3 Jan.
       2024), p. 035420.
[24]   T. Liang, P. Ying, K. Xu, Z. Ye, C. Ling, Z. Fan, and J. Xu. “Mechanisms of temperature-dependent
       thermal transport in amorphous silica from machine-learning molecular dynamics”. In: Phys. Rev.
       B 108 (18 Nov. 2023), p. 184203.
[25]   P. Ying, T. Liang, K. Xu, J. Xu, Z. Fan, T. Ala-Nissila, and Z. Zhong. “Variable thermal transport
       in black, blue, and violet phosphorene from extensive atomistic simulations with a neuroevolution
       potential”. In: International Journal of Heat and Mass Transfer 202 (2023), p. 123681.
[26]   Y. Lin, T. Li, S. Liu, Q. Shi, K. Xu, Z. Zhang, and J. Wu. “Interfacial mechanical properties
       of tetrahydrofuran hydrate-solid surfaces: Implications for hydrate management”. In: Journal of
       Colloid and Interface Science 629 (2023), pp. 326–335.
[27]   P. Ying, T. Liang, K. Xu, J. Zhang, J. Xu, Z. Zhong, and Z. Fan. “Sub-Micrometer Phonon Mean
       Free Paths in Metal–Organic Frameworks Revealed by Machine Learning Molecular Dynamics
       Simulations”. In: ACS Applied Materials & Interfaces 15.30 (2023), pp. 36412–36422.
[28]   Y. Liu, K. Xu, Y. Xu, J. Liu, J. Wu, and Z. Zhang. “HTR: An ultra-high speed algorithm for cage
       recognition of clathrate hydrates”. In: Nanotechnology Reviews 11.1 (2022), pp. 699–711.
[29]   Z. Zhang, K. Xu, Y. Lin, X. Cao, and J. Wu. “Simultaneous stiffening and strengthening of nan-
       odiamond by fivefold twins”. In: MRS Bulletin 47.3 (2022), pp. 219–230.
[30]   R. Fu, Y. Xu, Y. Liu, Y. Lin, K. Xu, Y. Chang, Y. Fu, Z. Zhang, and J. Wu. “Thermally induced
       hex-graphene transitions in 2D carbon crystals”. In: Nanotechnology Reviews 11.1 (2022), pp. 1101–
       1114.
[31]   Y. Xu, Q. Shi, Z. Zhou, K. Xu, Y. Lin, Y. Li, Z. Zhang, and J. Wu. “Machine learning assisted
       insights into the mechanical strength of nanocrystalline graphene oxide”. In: 2D Materials 9.3
       (2022), p. 035002.



                                                     2
[32]   X. Guo, S. Cheng, K. Xu, B. Yan, Y. Li, W. Cai, J. Cai, B. Xu, Y. Zhou, Y. Zhang, and X.-a.
       Zhang. “Controlling anisotropic thermal properties of graphene aerogel by compressive strain”. In:
       Journal of Colloid and Interface Science 619 (2022), pp. 369–376.
[33]   P. Wang, J. Wang, K. Xu, Y. Lin, Q. Shi, T. Li, Y. Fu, Z. Zhang, and J. Wu. “Mechanical stability
       of fluorinated-methane clathrate hydrates”. In: Journal of Molecular Liquids 360 (2022), p. 119553.
[34]   Z. Zhou, K. Xu, Z. Song, Z. Wang, Y. Lin, Q. Shi, Y. Hao, Y. Fu, Z. Zhang, and J. Wu. “Isotope
       doping-induced crossover shift in the thermal conductivity of thin silicon nanowires”. In: Journal
       of Physics: Condensed Matter 35.8 (2022), p. 085702.
[35]   Z. Fan, Y. Wang, P. Ying, K. Song, J. Wang, Y. Wang, Z. Zeng, K. Xu, E. Lindgren, J. M.
       Rahm, A. J. Gabourie, J. Liu, H. Dong, J. Wu, Y. Chen, Z. Zhong, J. Sun, P. Erhart, Y. Su,
       and T. Ala-Nissila. “GPUMD: A package for constructing accurate machine-learned potentials and
       performing highly efficient atomistic simulations”. In: The Journal of Chemical Physics 157.11
       (2022), p. 114801.
[36]   T. Liang, K. Xu, M. Han, Y. Yao, Z. Zhang, X. Zeng, J. Xu, and J. Wu. “Abnormally high ther-
       mal conductivity in fivefold twinned diamond nanowires”. In: Materials Today Physics 25 (2022),
       p. 100705.
[37]   Y. Lin, Y. Liu, K. Xu, T. Li, Z. Zhang, and J. Wu. “Strengthening and weakening of methane
       hydrate by water vacancies”. In: Advances in Geo-Energy Research 6.1 (2022), pp. 23–37.
[38]   H. Yu, K. Xu, Z. Zhang, X. Cao, J. Weng, and J. Wu. “Oxygen functionalization-induced crossover
       in the tensile properties of the thinnest 2D Ti 2 C MXene”. In: Journal of Materials Chemistry C
       9.7 (2021), pp. 2416–2425.
[39]   J. Liu, K. Xu, L. Yang, Y. Lin, T. Li, X. Gao, Z. Zhang, and J. Wu. “Fracture mechanics of methane
       clathrate hydrates”. In: Acta Mechanica Sinica 37.9 (2021), pp. 1387–1394.
[40]   Y. Xin, Q. Shi, K. Xu, Z.-S. Zhang, and J.-Y. Wu. “Tensile properties of structural I clathrate
       hydrates: Role of guest—host hydrogen bonding ability”. In: Frontiers of Physics 16 (2021), pp. 1–
       10.
[41]   L. Yang, J. Liu, Y. Lin, K. Xu, X. Cao, Z. Zhang, and J. Wu. “Strengthening and weakening by
       dislocations in monolayer MoS2”. In: Chemistry of Materials 33.22 (2021), pp. 8758–8767.
[42]   L. Yang, J. Liu, Y. Lin, K. Xu, X. Cao, Z. Zhang, and J. Wu. “Mechanical ductile detwinning in CH
       3 NH 3 PbI 3 perovskite”. In: Physical Chemistry Chemical Physics 23.38 (2021), pp. 21863–21873.
[43]   J. Zhu, K. Xu, Z. Zhang, X. Cao, S. Huang, and J. Wu. “Carbon clathrates as strong lightweight
       structures”. In: International Journal of Mechanical Sciences 202 (2021), p. 106509.
[44]   Z. Fan, Y. Wang, S. Zheng, K. Xu, J. Wu, S. Chen, J. Liang, A. Shi, and Z. Wang. “A submicron
       Si@ C core-shell intertwined with carbon nanowires and graphene nanosheet as a high-performance
       anode material for lithium ion battery”. In: Energy Storage Materials 39 (2021), pp. 1–10.
[45]   A. Zhou, Q. Yang, K. Xu, Q. Zhou, J. Wu, and H. Bai. “Manipulating the elasticity of chemically
       modified graphene aerogel through water surface plasticization”. In: Carbon 184 (2021), pp. 43–52.
[46]   Y. Lin, K. Xu, X. Cao, Z. Zhang, and J. Wu. “Role of nanotube chirality on the mechanical
       characteristics of pillared graphene”. In: Mechanics of Materials 162 (2021), p. 104035.
[47]   Y. Fu, K. Xu, J. Wu, Z. Zhang, and J. He. “The effects of morphology and temperature on the
       tensile characteristics of carbon nitride nanothreads”. In: Nanoscale 12.23 (2020), pp. 12462–12475.
[48]   T. Li, P. F. Ibáñez-Ibáñez, V. Håkonsen, J. Wu, K. Xu, Y. Zhuo, S. Luo, J. He, and Z. Zhang. “Self-
       deicing electrolyte hydrogel surfaces with Pa-level ice adhesion and durable antifreezing/antifrost
       performance”. In: ACS applied materials & interfaces 12.31 (2020), pp. 35572–35578.
[49]   H. Gong, J. Liu, K. Xu, J. Wu, and Y. Li. “Surface-topology-controlled mechanical characteristics
       of triply periodic carbon Schwarzite foams”. In: Soft matter 16.17 (2020), pp. 4324–4338.
[50]   N. Wei, Y. Chen, Y. Zhang, C. Zhou, X. Hao, K. Xu, K. Cai, and J. Chen. “Efficient selection
       methods for black phosphorene nanoribbons”. In: Nanoscale 10.9 (2018), pp. 4385–4390.




                                                     3
Abstract of Research

     Transition metal dichalcogenides (TMDs), such as MoS2, WS2, and their alloys, are
emerging as key materials for next-generation technologies, including electronics, quantum
computing, and energy conversion. These materials offer unique properties such as a direct
bandgap, strong spin-orbit coupling, and atomic-scale thickness, which make them ideal
candidates for a wide range of applications. However, the large-scale production of high-
quality TMDs faces significant challenges. These include difficulties in controlling phase
purity, maintaining alloy homogeneity, optimizing edge structures, suppressing defects, and
achieving large-area growth with consistent quality.
     To overcome these challenges, we propose developing a TMD-specific machine learning
potential (MLP) framework to drive molecular dynamics (MD) simulations of TMD growth.
This framework will be benchmarked against density functional theory (DFT) to ensure high
accuracy in simulations. By integrating the MLP with MD simulations (MLP+MD), we can
perform microsecond-scale simulations that capture critical growth processes such as
nucleation, edge propagation, domain coalescence, and defect dynamics. The simulations will
cover various chemistries and polymorphs of TMDs (Mo/W and S/Se/Te), including
interactions with substrates and gas/surface reactions that are relevant to the growth process.
     The TMD-specific MLP framework will enable large-scale, long-time simulations that
can accurately model the evolution of defects, phase transitions, and other key processes
involved in TMD synthesis. This will provide valuable insights into material growth
mechanisms and help guide the precise tuning of TMD structures and electronic properties.
Expected outcomes include the creation of high-accuracy MLPs for TMDs and dynamic
models of TMD growth that will accelerate the integration of these materials into practical
applications. By providing a scalable computational platform for TMD design, this approach
will support the efficient and defect-free synthesis of TMDs, paving the way for their
widespread use in electronics, energy devices, and other cutting-edge technologies.
Proposed Objectives and Potential Impact of Research Project / Activities

Objective:
1. Prepare Complete Training-set: Develop a comprehensive, growth-aware training corpus
that captures the thermodynamics and kinetics of transition-metal dichalcogenides (TMDs)
across multiple phases, surfaces, edges, defects, heterointerfaces, and reactive environments.
The dataset will be (i) diverse (covering a wide range of structures, chemistries, and
temperatures), (ii) reliable (using DFT-quality labels), and (iii) consistent (ensuring identical
target definitions and dispersion treatment). This will enable the development of a single,
robust machine learning potential (MLP) without requiring task-specific adjustments or
compromises.
2. Develop a Near-DFT-Level Accuracy Universal (MLP) for TMDs: Build an interatomic
potential covering Mo/W cations and S/Se/Te anions across polymorphs, compositions, edges,
vacancies, adatoms, grain boundaries, and common substrates. Target energy MAE ≤ 20
meV/atom, force MAE ≤ 200 meV Å-1, and stress MAE ≤ 2 GPa on held-out test sets
covering both equilibrium and reactive configurations. Validate phase stability (free-energy
differences), defect formation energies, and edge energies against DFT and short AIMD
baselines with quantified uncertainties.
3. Property Inference from Atomistics: Molecular dynamics simulations will encompass
predictions of growth-related equations of state (ES) curves, elastic response, phonons, defect
energetics, edge reconstruction, and adsorbate reactions. Based on the MLP+MD trajectories,
structure-related properties—such as defect/edge density, grain size distribution, alloy
clustering indices, and phonon spectra/thermal transport indices—will be calculated to link
predicted growth conditions to device-relevant performance.
4. Growth Simulation and Mechanism Analysis: Use the trained MLP to simulate TMD
growth mechanisms in large-scale MD simulations, focusing on nucleation, edge propagation,
domain coalescence, and defects. The study will examine how growth conditions (e.g.,
temperature, precursor fluxes) and substrate interactions influence morphology, phase purity,
and defect formation. These insights will be critical for process optimization.
Potential Impact and Benefits:
1. Authoritative TMD MLP Library: The creation of a publicly available, benchmarked
suite of MLPs for Mo/W–S/Se/Te systems, including bulk, monolayer, edges, defects, alloys,
and interfaces, will be a significant resource for the research community. This MLP library,
ready for deployment in computational platforms such as LAMMPS or GPUMD, will allow
for efficient and accurate large-scale simulations of TMD systems, contributing to the
advancement of TMD research globally.
2. Predictive growth phase maps: The development of condition-aware maps linking growth
parameters (temperature, chemical potentials, flux ratios) to phase purity, alloy homogeneity,
and defect densities will provide mechanistic insights for process optimization. These maps
will allow researchers and industry practitioners to predict growth outcomes under different
conditions without the need for extensive experimental work. This capability will
significantly accelerate the development of optimized TMD growth protocols.
3. Mechanistic design rules: The research will generate quantified insights into the roles of
edge structures, step flow, and defects in controlling morphology and domain coalescence
during TMD growth. These insights will lead to the formulation of transferably applicable
design rules that can help suppress defects (e.g., antisites and vacancies) and stabilize desired
phases (such as the 2H versus 1T/1T') under realistic growth conditions. These rules will be
invaluable for improving the quality of TMD films in both academic research and industrial
applications.
4. Scalable digital twin for 2D growth: The development of a high-performance MLP+MD
engine will enable simulations of TMD growth on the scale of millions of atoms, operating on
timescales of microseconds. The inclusion of rare-event acceleration techniques will make it
possible to simulate key growth processes (such as nucleation, coalescence, and defect
evolution) that are typically impractical to study using traditional AIMD simulations. This
digital twin approach will provide a powerful tool for in silico process development, enabling
the optimization of growth protocols without the need for costly and time-consuming
experiments.
This research will significantly enhance the understanding of TMD growth mechanisms at the
atomic level, providing valuable insights into how various factors—such as precursor flux,
substrate interaction, and temperature—control the material's phase, structure, and defect
landscape. The development of an accurate, scalable MLP model and growth simulation
platform will open new pathways for optimizing TMD synthesis, reducing defects, and
improving phase control. By providing a tool for predictive process design, this research
could revolutionize the scalable production of high-quality TMD materials for a wide range of
applications, including flexible electronics, quantum computing, and energy devices.
Background of Research, Research Plan and Methodology
(i) Background of Research
Two-dimensional transition-metal dichalcogenides (TMDs) such as MoS2, WS2, and their
alloys possess remarkable properties, including direct bandgaps in the monolayer limit, strong
spin-orbit coupling3,4, polymorphism5, and exceptional mechanical and chemical stability.
These features make TMDs promising for applications in logic, memory, quantum, and
energy devices6,7. However, their scalable deployment faces several challenges, primarily
related to growth issues such as phase selection1,2, alloy homogeneity, edge reconstruction,
defect suppression, domain coalescence, and interactions with substrates.
A critical bottleneck in TMD growth is the ability to control and predict the relationship
between growth conditions (e.g., temperature, chemical potentials, precursor fluxes) and
resulting structural and material properties. There is a pressing need for a predictive,
atomistically accurate simulation framework that can bridge this gap, optimizing synthesis
processes and facilitating the transition from laboratory research to real-world applications.8
Why MLP+MD for TMD growth?
While ab initio molecular dynamics (AIMD) simulations can capture bond rearrangements
during nucleation and step flow, they are limited by computational constraints, typically
handling only ~ 102–103 atoms over timescales of ~ 10–100 ps. Modern equivariant/graph-
based MLPs, such as NEP (Neural Network Potentials), provide near-DFT accuracy with
orders-of-magnitude speedups, enabling simulations of 104–106 atoms over nanosecond to
microsecond timescales on commodity GPUs and high-performance computing clusters. This
opens the possibility of simulating mesoscale TMD growth, including tracking nuclei
statistics, edge kinetics, alloy ordering, and defect dynamics in complex, chemically rich
environments. The application of MLP+MD simulations in TMD growth can address several
key challenges:
1. Epitaxy & Orientation Control: Simulations can predict grain orientation, step formation,
and the energetics of step-terraces during growth on different substrates.
2. Chalcogen Supply, Defects, and Alloys: These simulations can quantify sticking
mechanisms, surface diffusion, substitution barriers, and defect annihilation rates, which are
crucial for predicting phase purity and alloy homogeneity.
3. Metastable Phase Stabilization: MLP+MD can explore the interfacial interactions that
stabilize metastable 1T' phases, providing pathways for transitions from 2H to 1T'.
4. Edge Kinetics: The models can simulate edge reconstruction and kinetics, which are
essential for controlling step growth and defect suppression.
5. Device-Driven Scale & Uniformity: Simulations can offer predictions on stoichiometry,
interface effects, and the transition from research-scale to device-scale growth.
(ii) Research Plan
(a) Reference Dataset Design (DFT/AIMD)
A comprehensive reference dataset will be assembled to cover various crystalline TMD
phases (2H, 1T, and 1T') with various metal (Mo/W) and chalcogen (S/Se/Te) compositions,
along with point defects (vacancies, antisites, adatoms) and edge configurations (ZZ/AC
terminations). Reactive environments will include chalcogen monomers/dimers, H radicals,
and interfaces such as Au(4H)/TMD for quasi-epitaxy studies. Active learning will drive
dataset expansion, focusing on high-curvature regions like step reconstructions, alloying, and
non-equilibrium pathways.
(b) MLP Architecture and Training.
The neural network architecture for the NEP model will be optimized to handle short-to-
medium-range interactions and to learn long-range electrostatics. The model will be trained
using data from DFT calculations, NEB barriers, and AIMD simulations, with a focus on
energy, forces, and stresses. A multi-fidelity training approach will be used to enhance
transferability and ensure the accuracy of the model, which will be validated using hold-out
tests and reactive event simulations.
(c) Condition-Aware Growth Simulations.
Grand-canonical/reactive MD simulations will emulate TMD growth by controlling chalcogen
monomer and metal precursor fluxes. The simulations will focus on epitaxy, nucleation, edge
kinetics, and alloy mixing. We will validate key adsorption and diffusion barriers against DFT
results and simulate island nucleation and coalescence on vicinal substrates. Edge-resolved
kinetics will provide insights into step growth and defect suppression under varying chemical
potentials.
(d) Surrogates, Design Maps, and Validation.
To facilitate fast predictions of macroscopic growth outcomes, we will develop surrogate
models, such as kinetic Monte Carlo (kMC) models and compact emulators, to predict growth
rates, domain sizes, phase purity, and defect densities. These models will allow the generation
of "growth dashboards" that link process variables (e.g., temperature, precursor flux) to
material properties. Multi-objective Bayesian optimization will be used to identify optimal
process conditions, balancing rapid growth with low defects and uniform phases. Validation
will involve cross-validation, DFT checks, and experimental comparisons.
(iii) Methodology
a. Prepare the Dataset for Machine Learning Potential
(1) Structure generation: The dataset will include TMD polymorphs (2H, 3R, 1T/1T') and
substrates (e.g., sapphire, SiO₂, Au, Cu), incorporating organic/organometallic fragments,
solvent species, and strained or deformed versions, such as the MPtrj dataset9, the ANI-1xnr
dataset for reactive organics10 and the OMat24 dataset11. We will perform ab initio MD
simulations at 600–1200 K under chalcogen- and metal-rich chemical potentials and compute
representative minimum-energy paths (NEB/dimer) for barrier topologies. Alloy systems,
defects, and interfaces will also be considered.
(2) MLP Training: The NEP model12-14 will be trained on the generated dataset, with site
energies expressed as functions of abstract descriptors. The potential energy for a given atom
will be modeled as a sum over hidden layers of the neural network, optimized to reproduce
energies, forces, and stresses.
(3) Model Check and Analysis: The model will be validated against unseen test sets, which
will include novel edges, substrates, chalcogen ratios, and larger grains. The model’s ability to
reproduce static properties (energies, forces) and dynamic properties (e.g., diffusion barriers,
edge energies) will be critically assessed.
b. Molecular Dynamics Simulations
(1) Static calculation: Key thermodynamic and defect properties will be calculated using the
trained MLP. This includes lattice parameters, vacancy formation energies, and chemical
potentials under different growth conditions.
(2) MD Simulation: A comprehensive set of dynamic properties relevant to TMD growth will
be computed using the trained MLP, including surface and edge diffusion,
adsorption/desorption rates, and edge kinetics. Rare-event sampling techniques (e.g., umbrella
sampling, meta-dynamics) will be used to accelerate nucleation and other slow processes.
c. Surrogates, Design Maps, and Validation
Surrogate models will be developed to approximate computationally expensive MLP
calculations. These will include rate-based models (Gaussian processes, gradient-boosted
models) for elementary process rates (e.g., diffusion, adsorption/desorption) and a kinetic
surrogate for kMC outcomes. These surrogates will be used to generate design maps linking
experimental parameters (temperature, precursor flux, etc.) to desired growth outcomes.
Multi-objective optimization will be employed to generate Pareto fronts that balance growth
rate, defect density, and phase purity.
d. Growth Mechanism Analysis: The growth of TMDs involves several complex
mechanisms, including nucleation, island formation, growth kinetics, phase transitions, and
defect dynamics. Nucleation is governed by factors such as precursor flux and chemical
potentials, while island growth and coalescence are influenced by surface diffusion, edge
kinetics, and substrate interactions. The phase transitions, particularly from the stable 2H
phase to the metastable 1T' phase, are crucial for tuning material properties. Additionally,
defects such as vacancies and edge reconstructions can significantly impact the quality of
TMDs, and controlling these defects is key to achieving high-quality films. By using
MLP+MD simulations, this research aims to provide atomistic insights into these processes,
enabling the optimization of growth conditions to control phase stability, minimize defects,
and achieve uniform, large-scale TMD films suitable for various applications.
Illustrations (optional)
Pathways to Impact Statement
This research project aims to develop a predictive and atomistically accurate simulation
framework for the growth of two-dimensional transition-metal dichalcogenides (TMDs),
enabling optimized synthesis processes and accelerated technological application. The
integration of machine learning potentials (MLPs) with molecular dynamics (MD)
simulations will facilitate large-scale, atomistic simulations of TMD growth, providing
insights into the key mechanisms of nucleation, island coalescence, phase transitions, and
defect suppression. By improving our understanding of TMD growth dynamics and enabling
the design of more efficient, scalable, and defect-free synthesis methods, this project has the
potential to make significant contributions across multiple sectors, including materials
science, quantum technologies, and energy devices. The pathways to impact for this project
are outlined below, highlighting the scientific, technological, economic, and societal impacts.
Scientific Impact
Advancing Knowledge of 2D Materials Growth: This research will make significant
contributions to the fundamental understanding of TMD growth mechanisms, particularly at
the mesoscale. By combining atomistic simulations with machine learning techniques, we will
develop novel methods for simulating large-scale TMD growth and exploring complex
phenomena such as phase transitions, edge dynamics, and alloy mixing. These advancements
will help unlock the potential for designing TMD materials with tailored properties, which is
essential for next-generation electronics, optoelectronics, and quantum devices.
Improving the Predictive Power of Simulation Frameworks: By developing a robust
machine learning-based simulation framework, this project will provide a new tool for
researchers to predict the behavior of TMDs under various growth conditions. The integration
of high-fidelity models for materials properties with process parameters will allow for more
accurate predictions of growth outcomes, reducing the need for extensive experimental trial
and error. These models will enable researchers to design optimized synthesis protocols and
predict material behaviors, thus pushing the boundaries of computational materials science.
Enhancing Atomistic Simulations in Materials Design: The application of MLPs to
simulate TMD growth will enhance the capability of atomistic simulations in materials
design. The ability to handle larger systems and longer timescales will open new avenues for
the study of mesoscale phenomena in material growth. The methodology developed here can
be extended to other 2D materials and complex materials systems, offering a general
framework for advancing the design of materials with targeted properties.
Technological Impact
Accelerating the Development of TMD-Based Devices: The insights gained from this
project will be directly applicable to the development of TMD-based devices. These materials
have immense potential in the fields of flexible electronics, high-performance photodetectors,
solar cells, transistors, and quantum computing. By improving the control over TMD growth,
this research can contribute to the creation of more efficient, scalable, and defect-free TMD
films. Such advancements are critical for enabling the mass production of TMD-based
devices, reducing costs, and improving their performance for practical applications.
Facilitating Industry-Scale TMD Synthesis: One of the key challenges in TMD-based
manufacturing is achieving uniformity, phase purity, and scalability in large-area growth. The
predictive models developed through this research will enable the identification of optimal
process conditions, allowing for more precise control over TMD growth on substrates at
industry-relevant scales. This can lead to more reliable and reproducible production of high-
quality TMD materials, which is a major step towards their integration into real-world
applications.
Potential for Cross-Domain Applications: The techniques developed in this project can be
applied beyond TMDs to other materials and devices. The machine learning-enhanced
molecular dynamics simulations and the predictive models for material growth can be
generalized to other 2D materials, complex thin-film systems, and nanostructures. This opens
up the possibility of improving synthesis processes across a wide range of materials used in
industries such as semiconductors, photonics, and energy storage.
Conclusion
The impact of this research extends across scientific, technological, economic, and societal
domains. By providing new insights into the growth mechanisms of TMDs and enabling more
efficient synthesis processes, this project will help accelerate the adoption of TMD-based
technologies in various industries. The advancements in simulation methodologies and the
improved control over TMD growth will not only benefit the materials science community but
will also have far-reaching effects on energy solutions, quantum computing, and the broader
technology sector. Ultimately, this project will contribute to the development of advanced
materials that can drive innovation and sustainability across multiple industries, creating
significant value for both industry and society.
References
1     Zhang, T., Wang, J., Wu, P., Lu, A.-Y. & Kong, J. Vapour-phase deposition of two-
      dimensional layered chalcogenides. Nature Reviews Materials 8, 799-821,
      doi:10.1038/s41578-023-00609-2 (2023).
2     Sheng, C. et al. Two-Dimensional Semiconductors: From Device Processing to Circuit
      Integration. Advanced Functional Materials 33, 2304778,
      doi:https://doi.org/10.1002/adfm.202304778 (2023).
3     Mak, K. F., Lee, C., Hone, J., Shan, J. & Heinz, T. F. Atomically Thin MoS2: A New
      Direct-Gap Semiconductor. Physical Review Letters 105, 136805,
      doi:10.1103/PhysRevLett.105.136805 (2010).
4     Novoselov, K. S., Mishchenko, A., Carvalho, A. & Castro Neto, A. H. 2D materials
      and van der Waals heterostructures. Science 353, aac9439,
      doi:10.1126/science.aac9439 (2016).
5     Sung, J. H. et al. Coplanar semiconductor–metal circuitry defined on few-layer MoTe2
      via polymorphic heteroepitaxy. Nature Nanotechnology 12, 1064-1070,
      doi:10.1038/nnano.2017.161 (2017).
6     Han, G. H., Duong, D. L., Keum, D. H., Yun, S. J. & Lee, Y. H. van der Waals
      Metallic Transition Metal Dichalcogenides. Chemical Reviews 118, 6297-6336,
      doi:10.1021/acs.chemrev.7b00618 (2018).
7     Wang, P. et al. Controlled Syntheses and Multifunctional Applications of Two-
      Dimensional Metallic Transition Metal Dichalcogenides. Accounts of Materials
      Research 2, 751-763, doi:10.1021/accountsmr.1c00092 (2021).
8     Manzeli, S., Ovchinnikov, D., Pasquier, D., Yazyev, O. V. & Kis, A. 2D transition
      metal dichalcogenides. Nature Reviews Materials 2, 17033,
      doi:10.1038/natrevmats.2017.33 (2017).
9     Deng, B. et al. CHGNet as a pretrained universal neural network potential for charge-
      informed atomistic modelling. Nature Machine Intelligence 5, 1031-1041,
      doi:10.1038/s42256-023-00716-3 (2023).
10    Zhang, S. et al. Exploring the frontiers of condensed-phase chemistry with a general
      reactive machine learning potential. Nature Chemistry 16, 727-734,
      doi:10.1038/s41557-023-01427-3 (2024).
11    Barroso-Luque, L. et al. Open materials 2024 (omat24) inorganic materials dataset
      and models. arXiv preprint arXiv:2410.12771 (2024).
12    Fan, Z. et al. Neuroevolution machine learning potentials: Combining high accuracy
      and low cost in atomistic simulations and application to heat transport. Physical
      Review B 104, 104309, doi:10.1103/PhysRevB.104.104309 (2021).
13    Fan, Z. Improving the accuracy of the neuroevolution machine learning potential for
      multi-component systems. Journal of Physics: Condensed Matter 34, 125902,
      doi:10.1088/1361-648X/ac462b (2022).
14    Fan, Z. et al. GPUMD: A package for constructing accurate machine-learned
      potentials and performing highly efficient atomistic simulations. The Journal of
      Chemical Physics 157, 114801, doi:10.1063/5.0106617 (2022).
15    Xu, K. et al. GPUMD 4.0: A high-performance molecular dynamics package for
      versatile materials simulations with machine-learned potentials. Materials Genome
     Engineering Advances n/a, e70028, doi:https://doi.org/10.1002/mgea.70028 (2025).
16   Joksas, D. et al. Committee machines—a universal method to deal with non-idealities
     in memristor-based neural networks. Nature Communications 11, 4273,
     doi:10.1038/s41467-020-18098-0 (2020).
RELATED EXPERIENCE
My research focuses on areas such as thermal transport, molecular dynamics (MD)
simulations, and machine learning (ML) potentials, which are highly relevant to the
proposed project.
Molecular Dynamics and Machine Learning in Materials Science: My work
extensively employs MD simulations to study thermal properties and mechanical
behavior in nanomaterials. My involvement with machine-learned potentials (MLPs)
aligns directly with the proposed approach of utilizing MLPs to model TMD growth. I
have applied MLPs in simulations of materials such as graphene, MoS2, and black
phosphorene, enhancing the accuracy and efficiency of simulations.
Focus on 2D Materials: I have worked on 2D materials like MoS2 and phosphorene,
investigating their thermal transport properties, which is crucial for understanding
TMDs. This research in this area could be beneficial in addressing the challenges of
TMD growth, such as defect dynamics and phase transitions.
High-Performance Simulations: My research on efficient simulation methods using
advanced MLPs, such as GPUMD (a package for machine-learned potential
simulations), demonstrates my ability to conduct large-scale, long-time simulations,
much like the ones proposed for modeling TMD growth.
Collaborations and Publications: I have published numerous works in high-impact
journals, including Nature Communications, NPJ Computational Materials, The
Journal of Chemical Physics, and Physical Review B, often in collaboration with
leading experts in materials science.
My comprehensive experience in computational materials science, especially in MD
simulations and machine learning potentials, would contribute significantly to
advancing the TMD-specific MLP framework and achieving the objectives outlined in
your project.
The First GPUMD Developer Conference                   June 2025
Member of the Academic Committee
Ningbo Institute of Materials Technology & Engineering - Ningbo, China
The "Silk Road" Frontier Interdisciplinary Studies Forum has attracted
widespread attention.                               June 2024
Invited Speech
The Xinjiang Technical Institute of Physics & Chemistry. CAS - 40-1 Beijing Road
Urumqi, Xinjiang 830011 P. R. China
The 1st GPUMD&NEP Academic Seminar                     July 2024
Invited Speech
Bohai University - Jinzhou, China
Forum for Outstanding Young Scholars                   June 2023
Invited Speech
Zhejiang University Quzhou Research Institute - Quzhou, China
Multiscale Computing Youth Academic Forum              June 2023
Invited Speech
University of Science and Technology Beijing - Beijing, China
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Xu
                        (Other name) Ke
Host Faculty / School / Department / Department of Electronic Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

I have known the nominee since 2017 through our collaborative work on the development of the molecular
dynamics code I maintain, GPUMD (https://gpumd.org/). He has contributed several critical features to the
project and is now among the top contributors. Our collaboration has also resulted in several co-authored
publications, and we have maintained our professional dialogue through meetings at academic conferences on
topics including AI for materials.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent      Very Good        Good                 Average         No basis
                                (top 5%)       (top 6-20%) (top 21-50%)             or below           for
                                                                                   (lower than      judgment
                                                                                      50%)
Intellectual capacity                              ☐                 ☐                 ☐               ☐
                                                     ☐                ☐                 ☐               ☐
Ability for conducting
scholastic research
                                    
                                                     ☐                ☐                 ☐               ☐
Knowledge of proposed
research study
                                    
                                                     ☐                ☐                 ☐               ☐
Analytical power and
reasoning
                                    
Judgment                                            ☐                ☐                 ☐               ☐
                                                     ☐                ☐                 ☐               ☐
Imagination and
originality
                                    
                                                     ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                    
                                                                      ☐                 ☐               ☐
Skills of writing and
argumentation
                                    ☐                
                                                     ☐                ☐                 ☐               ☐
Capacity for independent
work
                                    
                                                     ☐                ☐                 ☐               ☐
Reliability and sense of
responsibility
                                    
Leadership experience               ☐                                ☐                 ☐               ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

The nominee has been deeply engaged in the field of molecular dynamics for many years. Since 2021, he has
collaborated with me on the development of machine learning force fields. His work has been instrumental in
creating robust models for a diverse range of materials, including carbon, silicon, various 2D materials, and
water. His most impressive contribution, in my assessment, is his work on water. In this particularly challenging
area, he achieved exceptional results that had previously remained elusive to the research community. These
experiences will perfectly align with the development and refinement of machine learning force fields required
for this project. In terms of material growth, the nominee has a solid understanding of the nucleation process in
methane hydrates, which will be highly beneficial for enhancing his analysis of the growth mechanisms of 2D
materials. The nominee possesses strong scientific insight and judgment, a passion for research, and a sense of
responsibility and creativity toward project tasks. These qualities will provide significant support and contribute
positively to this project.




                                                         3
                                                                  JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: FAN ZHEYONG                                     Title: Prof
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Bohai University
 Position: Jinzhou, Liaoning Province, China
 E-mail Address: <EMAIL>
 Telephone Number#: +86-18851809264
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date: 2025/8/30




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Xu
                        (Other name) Ke
Host Faculty / School / Department / Department of Electronic Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?


I met the nominee in 2017 when we first collaborated on molecular dynamics simulations of MoS2. In 2019, I
became his doctoral advisor. Throughout his Ph.D. studies (from September 2019 to June 2023), the nominee has
consistently demonstrated outstanding research skills, particularly in machine learning force fields. Over the
course of our collaboration, we have co-authored more than 20 research papers and worked together on more than
three major projects.




                                                      2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                Excellent      Very Good        Good                Average         No basis
                                (top 5%)       (top 6-20%) (top 21-50%)            or below           for
                                                                                  (lower than      judgment
                                                                                     50%)
Intellectual capacity                              ☐                ☐                 ☐               ☐
                                                    ☐                ☐                                 ☐
Ability for conducting
scholastic research
                                                                                      ☐
                                    ☐                                ☐                 ☐               ☐
Knowledge of proposed
research study
                                                    

                                                                     ☐                 ☐               ☐
Analytical power and
reasoning
                                                   ☐
Judgment                                           ☐                ☐                 ☐               ☐
                                                    ☐                ☐                 ☐               ☐
Imagination and
originality
                                    

                                                    ☐                ☐                 ☐               ☐
Motivation and
perseverance
                                    

                                    ☐                                ☐                 ☐               ☐
Skills of writing and
argumentation
                                                    

                                                    ☐                ☐                 ☐               ☐
Capacity for independent
work
                                    

                                                    ☐                ☐                 ☐               ☐
Reliability and sense of
responsibility
                                    
Leadership experience               ☐                               ☐                 ☐               ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)

The nominee has been using molecular dynamics to study the heat transport behavior of molybdenum disulfide
since 2016. Beginning in 2019, during his doctoral studies, he independently taught himself machine learning
methods and has extensive research experience in machine learning potential. He has studied the various physical
properties of various two-dimensional materials, including graphene, molybdenum disulfide, and boron nitride.
At least, he is a member of the GPUMD core team, focusing on the development of atomic deposition, which
provides comprehensive technical support for this project. In terms of growth, he has studied water freezing and
hydrate nucleation, which he believes will be of great assistance in his mechanistic analysis of this project. The
nominee is unique in his ability to integrate multiple methods, including molecular dynamics, first-principles
calculations, and machine learning, to provide a comprehensive, multi-dimensional approach to problem solving.
His collaborative and communicative skills have contributed to the success of interdisciplinary projects and
highlight his ability to work harmoniously with the research team.




                                                        3
                                                                 JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: Wu Jianyang                                     Title: Prof. Dr.
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: Xiamen University
 Position: Xiamen City, Fujian Province, China
 E-mail Address: <EMAIL>
 Telephone Number#: +86-15711569902
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
 I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature:                                                  Date:     2025/8/30




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
