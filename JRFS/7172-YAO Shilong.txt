                                             <PERSON><PERSON> YAO
             P1618, Yeung Kin Man Academic Building, City University of Hong Kong, Kowloon, Hong Kong SAR, China
                           <EMAIL> ● GoogleScholar ● linkedin.com/in/shilongyao


EDUCATION         City University of Hong Kong, (CityU), Kowloon, Hong Kong SAR, China
                   ■ Ph.D. in Electrical Engineering                                 2021.09 – 2025.10 (Expected)
                      ●   Advisors: Prof<PERSON> (IEEE Life Fellow), Prof. <PERSON><PERSON><PERSON> (IEEE Fellow, FCAE)
                      ●   Research focusing on continuum robotic system modeling, control and task-autonomous applications.

                  Southern University of Science and Technology, (SUSTech), Shenzhen, China
                   ■ B.Eng in Mechanical Engineering                                                                     2017.09 – 2021.06
                      ● Advisor: Prof. <PERSON>
                      ● Graduated with College First Honor. Cumulative GPA: 3.46 / 4.00


INTERNSHIP        The Chinese University of Hong Kong, (CUHK), New Territories, Hong Kong SAR, China
                   ■ Honorary Research Assistant, Department of Electronic Engineering         2023.09 – 2024.08
                      ●   Advisor: Prof. <PERSON> (SMIEEE)
                      ●   Research focusing on soft swab robot micro-force sensing and perception.

                  North Carolina State University, Raleigh, NC, USA
                   ■ Summer Research Intern, Intelligent Structure and Systems Research Lab                              2020.07 – 2020.08
                      ●   Research focusing on the modeling and analysis of pneumatic artificial muscles (PAM).

                  Milebot Robotics Company, Shenzhen, China
                  ■ Internship, Mechanical Design Engineer                                                               2020.05 – 2020.11
                      ●   Completed an invention patent on the design of passive exoskeletons for lower limbs as the first inventor.

                  Tohoku University, Sendai, Japan
                   ■ Summer Research Intern, Nano-Precision Mechanical Fabrication Lab                                   2019.07 – 2019.08
                      ●   Research focusing on body-implanted metallic glass processing method design.

                  Southern University of Science and Technology, (SUSTech), Shenzhen, China
                   ■ Undergraduate Research Assistant, Advanced Actuator and Robotics Lab                                2019.03 – 2020.12
                      ● Advisor: Prof. Hongqiang Wang
                      ● Research focusing on the design and applications of soft swab robot systems.


PUBLICATIONS      ( †indicates equal contribution, *represents the corresponding authors.)
                  JOURNALS
                     [1] Adaptive Load-Dependent Sim2Real Framework for Path Tracking towards Tendon-Driven
                         Continuum Robots
                         Shilong Yao, Peiyu Luo, Yiyao Yue, Yuhan Chen* , Hong Yan and Max Q.-H. Meng*
                         IEEE/ASME Transactions on Mechatronics (T-Mech), (IF: 6.4), 2025
                     [2] Fast-Adaptive Permanent Magnetic Positioning-based Navigation Framework for Continuum
                         Robots in Colonoscopic Biopsy
                         Shilong Yao† , Peiyu Luo† , Li Liu* , Hong Yan and Max Q.-H. Meng*
                         IEEE/ASME Transactions on Mechatronics (T-Mech), 2025
                     [3] An RNN-LSTM Enhanced Compact and Affordable Micro Force Sensing System for
                         Interventional Continuum Robots With Interchangeable End-Effector Instruments
                         Shilong Yao† , Ruijie Tang† , Long Bai, Hong Yan, Hongliang Ren* , and Li Liu*
                         IEEE Transactions on Instrumentation and Measurement (T-IM), (IF: 5.6), 2023
                     [4] Chained Spatial Beam Constraint Model: A General Kinetostatic Model for Tendon-Driven
                         Continuum Robots
                         Yuhan Chen† , Shilong Yao† , Max Q.-H. Meng and Li Liu*
                         IEEE/ASME Transactions on Mechatronics (T-Mech), 2024. Presented at IEEE AIM 2024.
                     [5] Rotenna: Harnessing Seamless Integration of Continuum Robot for Dynamic Electromagnetic
                         Reconfiguration
                         Feiyang Deng, Shilong Yao* and Kwai Man Luk*
                         IEEE Robotics and Automation Letters (RAL) (IF: 5.2), 2023



                                                               Page 1 of 2
                 [6] Data-driven 3D Tactile Cues with Intermediate Soft Interfaces towards Training Needle
                     Insertions
                     Ruijie Tang† , Shilong Yao† , Long Bai, Hong Yan, Max Q.-H. Meng* and Hongliang Ren*
                     IEEE Sensors Journal (IF: 4.3), 2023
                 [7] An Explicit Nonlinear Model for Large Spatial Deflections of Symmetric Slender Beams
                     Yuhan Chen, Shilong Yao, Max Q.-H. Meng and Li Liu*
                     International Journal of Non-Linear Mechanics (IF: 2.8), 2024
                 [8] Joint Sparse Representations and Coupled Dictionary Learning in Multi-Source
                     Heterogeneous Image Pseudo-color Fusion
                     Long Bai, Shilong Yao, Kun Gao* ,... Max Q.-H. Meng and Hongliang Ren*
                     IEEE Sensors Journal, 2023
                 [9] V2 -SfMLearner: Learning Monocular Depth and Ego-motion for Multimodal Wireless
                     Capsule Endoscopy
                     Long Bai, Beilei Cui, Liangyu Wang, Yanheng Li, Shilong Yao, ... and Hongliang Ren*
                     IEEE Transactions on Automation Science and Engineering (T-ASE), 2025
               CONFERENCE
                 [1] Efficient RRT*-based Safety-Constrained Motion Planning for Continuum Robots in Dynamic
                     Environments
                     Peiyu Luo† ,Shilong Yao† , Yiyao Yue, Jiankun Wang* , Hong Yan and Max Q.-H. Meng*
                     2024 IEEE International Conference on Robotics and Automation (ICRA), 2024

AWARDS &       ■ National Undergraduate Training Program for Innovation and Entrepreneurship                          2020 – 2021
SCHOLARSHIPS     Project leader funded by the National Undergraduate Training Program, $ 7400(Top 3 out of 190 programs)
               ■ Scholarships for Outstanding Students Prize, SUSTech (Top 2% out of 4000, $ 860)                          2019,2020
               ■ Best Athlete Award of SUSTech (Soccer, Top 1% out of 4000, $220)                                          2018,2019

TEACHING       ■ Thesis /Final Year Project TA and Mentorship
EXPERIENCE       Graduates:
                 Peiyu Luo, M.Eng. EE (Ph.D at SJTU, 2025)
                 Yiyao Yue, B.Eng. EE (M.Sc. at JHU, 2024)
               ■ Teaching Assistant

                 Robotic Motion and Control (EE368)                                                            2023.09 – 2024.01
                 Electronic and Information Tech for Metaverse (EE101)                                         2023.02 – 2023.06

PROFESSIONAL   ■ Journal Reviews:
ACTIVITIES       IEEE Transactions on Automation Science and Engineering (T-ASE)
                 IEEE Transactions on Industrial Electronics (T-IE)
                 IEEE Transactions on Instrumentation and Measurement (T-IM)
                 IEEE Robotics and Automation Letters (RAL)
                 IEEE Transactions on Biomedical Engineering (T-BME)
                 IEEE Sensors Journal
               ■ Conference Reviews:

                 IEEE International Conference on Robotics and Automation (ICRA)
                 IEEE International Conference on Robotics and Biomimetics (ROBIO)


SKILLS         Python, MATLAB, Java, LATEX, ROS, Solidworks, Abaqus, Comsol

CAMPUS         Vice-President, Student Union, SHUDE College, SUSTech                           2018.06 – 2019.06
ACTIVITIES     College Football Team Captain, Championship, Shenzhen University Football League       2018,2020




                                                       Page 2 of 2
 Towards Autonomous and Safe Endoscopic Insertion: A Sensorized Continuum Robot
             for Contact-Free Navigation and In-Situ Lesion Mapping

Upper gastrointestinal endoscopy, or gastroscopy, is a fundamental procedure for diagnosing
and treating diseases of the esophagus, stomach, and duodenum, including life-threatening
conditions like gastric cancer. However, the procedure's quality and safety are highly dependent
on the operator's skill. Manual navigation of the endoscope often leads to repeated and
sometimes forceful contact with the sensitive mucosal lining, causing patient discomfort,
retching, and in rare cases, bleeding or perforation. Furthermore, inconsistent navigation and
mucosal scanning can lead to significant miss rates for early-stage lesions, which are often
subtle and easily overlooked.

This research project aims to revolutionize gastroscopy by developing a robotic system capable
of fully autonomous and contact-free navigation. We propose an intelligent, tendon-driven
continuum robot, designed with the form factor of a gastroscope, but equipped with a unique
combination of embedded visual and force-perceptive sensors.

The core innovation is a dual-pronged approach to robotic intelligence. First, we will develop
a Visual-Shape SLAM (Simultaneous Localization and Mapping) framework. Using only an
onboard camera and integrated Fiber Bragg Grating (FBG) shape sensors, the robot will
autonomously build a detailed 3D map of the gastric environment in real-time while precisely
tracking its own position and full-body shape. This creates a "GPS-like" system inside the
stomach. Second, and critically, we will implement a Contact-Free Navigation strategy. The
highly sensitive FBG sensors will detect minute lateral forces, indicating imminent or actual
contact with the tissue wall. This force-perceptive feedback will drive a reactive motion planner,
enabling the robot to "float" through the center of the gastric lumen, actively avoiding the
mucosal walls.

Upon completion, this project will deliver a robotic gastroscopy platform that can
autonomously perform a full examination of the upper GI tract. By mapping the anatomy and
automatically recording the precise 3D location of any detected lesion, it creates a permanent,
patient-specific "digital twin" for future follow-up and targeted therapy. This work will
transform gastroscopy into a standardized, gentle, and highly precise procedure, aiming to
improve patient safety, increase diagnostic accuracy, and lay the foundation for fully
autonomous medical interventions.
Objectives:
  1. To develop a multi-modal sensing continuum robot for comprehensive state
      estimation in the upper GI tract.
          o Objective 1.1: Design and fabricate a continuum robot prototype (diameter <
             10mm) with an integrated high-definition camera and a multi-channel Fiber
             Bragg Grating (FBG) array embedded along its backbone for full-body shape
             and lateral force sensing.
          o Objective 1.2: Develop and validate a real-time algorithm to process FBG sensor
             signals, reconstructing the robot's complete 3D shape with a mean tip error of <
             3mm and estimating distributed lateral contact forces with a resolution of <
             15mN.
  2. To create a Visual-Shape SLAM framework for real-time 3D mapping of the
      gastric environment.
          o Objective 2.1: Develop a novel sensor fusion algorithm that tightly couples
             monocular visual odometry with the FBG-derived continuous shape and motion
             data. This "Visual-Shape SLAM" will generate a dense, geometrically accurate
             3D map of the stomach.
          o Objective 2.2: Achieve robust SLAM performance capable of handling
             challenges specific to the gastric environment, such as dynamic peristaltic
             motions, fluid presence, and specular reflections. Target: Attain a mapping scale
             error of < 2% and a localization drift of < 1.5% over a complete simulated
             gastric examination in realistic phantom and ex-vivo porcine stomach models.
  3. To implement a Contact-Free Motion Planner for safe and autonomous navigation.
          o Objective 3.1: Design a lumen-centering global planner that generates an
             optimal, smooth trajectory through the center of the 3D map generated by the
             SLAM system.
          o Objective 3.2: Integrate the FBG-based force feedback into a reactive local
             planner. This "Force-Aware" controller will modulate the robot's trajectory in
             real-time to actively steer away from the gastric walls, maintaining a safe
             distance. Target: During autonomous navigation, the system will maintain
             lateral contact forces below a safety threshold of 0.5 N for >95% of the
             procedure time.
  4. To validate the integrated system for autonomous gastroscopy and in-situ lesion
      mapping.
          o Objective 4.1: Integrate all hardware and software components into a unified
             robotic platform and conduct end-to-end validation in a high-fidelity stomach
             phantom.
          o Objective 4.2: Perform a pre-clinical study in an ex-vivo porcine stomach model
             to demonstrate autonomous navigation and lesion mapping. The robot will be
             tasked to autonomously scan the entire gastric surface and, upon visual
             identification of simulated lesions (markers), record their precise 3D coordinates
             within the SLAM map. Target: Achieve >90% surface area coverage of the
             stomach and a lesion localization accuracy of < 5mm, enabling reliable "go-
             back-to" navigation.
Potential Impact:
   • Scientific Impact: This project will be a pioneering work in fully autonomous
       endoscopy. It will make fundamental contributions to medical robotics by delivering the
       first demonstration of contact-free robotic navigation in a highly deformable luminal
       organ. The novel Visual-Shape SLAM framework and force-reactive planning
       algorithms will establish a new state-of-the-art for intelligent navigation in confined,
       sensitive environments.
   • Clinical Impact:
            o Enhanced Patient Safety and Comfort: By minimizing contact with the mucosal
               walls, the autonomous system will dramatically reduce patient discomfort,
               retching, and the risk of complications, potentially allowing for procedures with
               less sedation.
            o Improved Diagnostic Accuracy: Autonomous, systematic scanning ensures 100%
               mucosal coverage, reducing the high miss rates of early-stage gastric cancer. The
               automatic in-situ lesion mapping creates a permanent, patient-specific 3D record,
               which is invaluable for tracking disease progression and planning follow-up
               procedures or targeted therapies.
            o Democratization of Expertise: The system automates the most technically
               challenging part of gastroscopy, reducing the steep learning curve for trainees
               and standardizing the quality of examinations across operators of all skill levels.
   • Economic and Societal Impact: By improving the safety and efficacy of gastroscopy,
       this technology can increase patient compliance with crucial cancer screening programs.
       The standardization and potential for reduced procedure times can improve the
       efficiency of endoscopy units, leading to significant healthcare cost savings. This
       research will solidify Hong Kong's position at the forefront of medical robotics and AI
       in healthcare.
3.1 Background of Research
3.1.1 The Clinical Challenge: Limitations of Modern Gastroscopy
Upper gastrointestinal endoscopy (gastroscopy) is an indispensable tool in modern medicine,
crucial for the diagnosis and management of diseases affecting the esophagus, stomach, and
duodenum. It is the frontline procedure for investigating symptoms like dyspepsia and
dysphagia, and most critically, for the early detection of gastric cancer, a disease with a 5-year
survival rate below 30% when diagnosed at late stages but exceeding 90% with early detection.
Despite its importance, gastroscopy remains a procedure fraught with challenges that limit its
efficacy, safety, and patient acceptance. The quality of an examination is profoundly dependent
on the operator's skill and experience. The manual navigation of a flexible endoscope through
the upper GI tract often results in repeated, sometimes forceful, contact with the sensitive
mucosal lining. This can cause significant patient discomfort, leading to gagging and retching,
which in turn can obscure the visual field and lead to incomplete examinations. In rare but
serious cases, this can cause mucosal bleeding or even perforation.
This operator dependency also introduces significant diagnostic variability. Authoritative
studies have revealed alarming miss rates for upper GI neoplasia, with some reports citing rates
as high as 25%. These diagnostic failures are often due to incomplete mucosal surface
examination, especially in "blind spots" like the gastric cardia, fundus, and antrum. The
cognitive and physical fatigue experienced by endoscopists during long procedures further
exacerbates this issue. There is a clear and pressing clinical need for a technology that can
standardize the procedure, ensure complete and gentle examination, and reduce the reliance on
operator skill.

3.1.2 The Technological Gap: From Teleoperation to True Autonomy in Endoscopy
The field of medical robotics has introduced systems to improve endoscopy, primarily through
teleoperation. These platforms enhance ergonomics and provide stable instrument control but
do not fundamentally alter the navigation paradigm; the clinician remains fully responsible for
manually steering the device. The next frontier is robotic autonomy, which promises to address
the core limitations of manual endoscopy.
However, achieving autonomy in a deformable, dynamic, and GPS-denied environment like the
stomach is a formidable challenge. A truly autonomous system requires two fundamental
capabilities that are currently lacking in the state-of-the-art:
    1. Real-time State Estimation: The robot must know its own position, orientation, and
        full-body shape (proprioception) and simultaneously perceive the 3D structure of the
        surrounding anatomy (exteroception). This must be achieved without external tracking
        systems (like optical or electromagnetic trackers), which are cumbersome, expensive,
        and often inaccurate in a clinical setting.
    2. Force-Aware, Gentle Navigation: To ensure patient safety and comfort, the robot must
        be able to "feel" its interaction with the delicate mucosal walls. It needs to detect even
        minute contact forces and use this haptic feedback to actively navigate away from tissue,
        effectively "floating" through the lumen.

3.1.3 Our Proposed Solution: A Sensor-Fused Continuum Robot for Intelligent Navigation
This project will bridge this autonomy gap by developing an intelligent continuum robot
platform for autonomous gastroscopy. We propose a tendon-driven continuum robot, whose
soft, flexible nature is inherently safer than conventional endoscopes. The core innovation lies
in the synergistic fusion of embedded sensors: a high-definition monocular camera at the tip
and a multi-channel Fiber Bragg Grating (FBG) network integrated along the robot's backbone.
My doctoral research, which focused on the high-fidelity mechanical modeling, control, and
force sensing of continuum robots, provides the essential expertise to realize this vision. This
project will build upon that foundation to create a system that achieves autonomy through two
interconnected technological pillars:
    • A novel Visual-Shape SLAM framework for real-time mapping.
    • A Contact-Free Navigation strategy enabled by FBG-based force perception.


3.2 Research Plan and Methodology
Our research plan is structured around four synergistic objectives, designed to systematically
develop and validate our autonomous gastroscopy system.

Objective 1: To develop a multi-modal sensing continuum robot for comprehensive state
estimation.
    • Methodology:
          o Robot Design & Fabrication: We will design and fabricate a multi-segment,
              tendon-driven continuum robot with a diameter of < 10mm. Its backbone will
              be a custom-extruded multi-lumen polymer tube, providing channels for tendons,
              tools, and, crucially, our integrated sensor suite.
          o FBG Sensor Integration: A multi-core optical fiber containing three spatially
              distributed FBG arrays will be precisely integrated along the robot's neutral
              bending axis. This strategic placement is key to decoupling strain measurements
              caused by bending (for shape reconstruction) from those caused by transverse
              loads (for force estimation). A biocompatible, low-durometer polymer coating
              will protect the fiber and provide a smooth interface.
          o Shape and Force Decoupling Model: We will develop a hybrid model to
              interpret the FBG signals. A real-time Finite Element Method (FEM) model,
              informed by my prior work on the CSBCM, will provide a physics-based prior
              for the robot's shape. This will be fused with a lightweight neural network (a
              Temporal Convolutional Network) that learns the complex, non-linear mapping
              from FBG wavelength shifts to a continuous 3D shape representation
              (parameterized by Bézier curves) and a distributed lateral force profile.
    • Key Metrics & Validation:
          o Shape Accuracy: The robot's 3D shape reconstruction will be validated against
              a Vicon motion capture system. Target: Achieve a mean tip position error of <
              3mm and a mean absolute error along the entire body of < 2mm.
          o Force Sensitivity: Force estimation will be calibrated against a commercial
              multi-axis force sensor (ATI Nano17). Target: Achieve a lateral force
              measurement resolution of < 15mN with a signal-to-noise ratio > 20 dB.

Objective 2: To create a Visual-Shape SLAM framework for real-time 3D gastric mapping.
   •   Methodology:
          o Sensor Fusion Architecture: We will develop a tightly-coupled, graph-based
            SLAM framework. The novelty lies in using the FBG data as a powerful "virtual
            Inertial Measurement Unit (IMU)". By differentiating the high-frequency FBG
            shape data over time, we obtain robust, low-drift estimates of the robot tip's
            linear and angular velocity, which serve as strong motion priors.
          o Visual Front-End: The monocular camera stream will be processed by a visual
            odometry front-end. We will employ learning-based feature detectors (e.g.,
            SuperPoint) and descriptors that are robust to the challenging, texture-poor, and
            often specular gastric environment.
          o Graph Optimization Back-End: We will use a factor graph optimization back-
            end (e.g., GTSAM) to jointly optimize the robot's trajectory, the 3D map points,
            and intrinsic sensor calibration parameters. The graph will contain factors from
            visual re-projection errors and FBG-derived motion constraints. This tight
            coupling ensures that the visual data corrects any low-frequency drift from the
            FBG, while the FBG data prevents scale drift and tracking failure in visually-
            degraded scenes.
   •   Key Metrics & Validation:
          o Accuracy: The SLAM system will be evaluated in a realistic stomach phantom
            with known ground-truth geometry. Target: Achieve a mapping accuracy with
            a mean reconstruction error of < 4mm and an Absolute Trajectory Error (ATE)
            of < 1.5% of the total path length.
          o Robustness: We will test the system's ability to maintain tracking during
            simulated peristalsis and fluid-induced visual occlusions in an ex-vivo porcine
            stomach. Target: Achieve successful, continuous mapping over a full
            examination (>5 minutes) without requiring manual re-initialization.

Objective 3: To implement a Contact-Free Motion Planner for safe autonomous
navigation.
   • Methodology:
          o Global Path Planning: A global planner operating on the SLAM-generated 3D
             map (represented as an occupancy grid or a mesh), will compute a geometrically
             optimal path through the center of the gastric lumen. We will use a fast search
             algorithm like Lazy PRM* to efficiently find a smooth, kinematically feasible
             trajectory.
          o Force-Reactive Local Control: The core of our "Contact-Free" strategy is a
             high-frequency reactive controller. This controller implements a virtual potential
             field. The global path acts as an attractive valley, while any lateral contact force
             detected by the FBG sensors generates a repulsive potential field emanating
             from the gastric wall. The robot's control actions are calculated to follow the
             gradient descent of this combined field, naturally keeping it centered in the
             lumen and away from tissue contact.
          o Systematic Scanning Strategy: The global planner will also incorporate a
             coverage planning algorithm to ensure the camera systematically scans the entire
            mucosal surface, guaranteeing a complete examination and minimizing missed
            areas.
   •   Key Metrics & Validation:
          o Safety: The primary metric will be the force exerted on the
            environment. Target: During autonomous navigation in an ex-vivo model, the
            system will maintain lateral contact forces below a safety threshold of 0.5
            N for >95% of the procedure time, with no force spikes exceeding 2.0 N.
          o Efficiency: The time to complete a full gastric examination will be
            measured. Target: Achieve a total examination time of < 8 minutes, which is
            competitive with expert clinicians.

Objective 4: To validate the integrated system for autonomous gastroscopy and in-situ
lesion mapping.
    • Methodology:
          o System Integration and GUI: All hardware (robot, sensors, controller) and
              software (SLAM, planning) modules will be integrated using the Robot
              Operating System (ROS 2). We will develop an intuitive graphical user interface
              (GUI) for the clinician. The GUI will display the live endoscopic video, the
              evolving 3D map, the robot's real-time shape, and color-coded force feedback.
              It will also allow the clinician to pause autonomy and take manual control at any
              time.
          o Pre-clinical Validation Protocol: We will conduct a comprehensive validation
              study using freshly excised ex-vivo porcine stomach models. The system will be
              tasked with performing a fully autonomous examination: navigating from the
              esophagus, systematically scanning the fundus, body, and antrum, and reaching
              the pylorus. We will place artificial lesions (e.g., small colored beads) on the
              mucosal surface.
          o Lesion Mapping and "Go-Back-To" Functionality: When the system's visual
              processing pipeline identifies a simulated lesion, it will automatically record the
              lesion's 3D coordinates in the SLAM map. After the initial scan, we will test the
              system's ability to autonomously navigate back to the precise location of a
              selected lesion, demonstrating the clinical utility for targeted biopsy or therapy.
    • Key Metrics & Validation:
          o Primary Endpoint: Success rate of completing a full autonomous examination
              (esophagus to pylorus passage and systematic scan). Target: Achieve a success
              rate of >90%.
          o Diagnostic Coverage: The percentage of mucosal surface area visualized
              during the autonomous scan will be calculated from the 3D
              map. Target: Achieve >95% surface coverage.
          o Lesion Localization Accuracy: The 3D error between the mapped lesion
              coordinates and their ground-truth positions will be measured. Target: Achieve
              a mean localization accuracy of < 2mm.
4. Pathways to Impact Statement
4.1 Introduction: Translating Robotic Innovation into Clinical Reality
The proposed research is not merely a technical exercise; it is driven by the clear clinical goal
of transforming gastroscopy into a safer, more effective, and more accessible procedure. To
ensure that the innovations developed in this project translate into tangible benefits, we have
devised a multi-faceted and proactive plan to maximize its academic, clinical, and
socioeconomic impact. This plan extends beyond the life of the grant and outlines a clear
roadmap from laboratory proof-of-concept to real-world application.

4.2 Pathway 1: Academic Impact and Leadership in Medical Robotics
Our primary pathway to impact is through the dissemination of high-quality scientific
knowledge, positioning our research group and institution at the forefront of intelligent medical
robotics.
    • High-Impact Publications: The novel contributions of this project—particularly the
       Visual-Shape SLAM framework and the contact-free navigation strategy—are of
       significant interest to both the robotics and medical imaging communities. We will
       target publications in premier, high-impact journals such as Science Robotics, IEEE
       Transactions on Robotics (T-RO), and IEEE Transactions on Medical Imaging (TMI),
       as well as top-tier conferences like Robotics: Science and Systems (RSS), the
       International Conference on Robotics and Automation (ICRA), and Medical Image
       Computing and Computer-Assisted Intervention (MICCAI). We aim to produce at
       least 5-6 seminal papers from this work.
    • Open Science and Reproducibility: To catalyze further research in this nascent field,
       we are committed to open science. Upon publication, we plan to release key
       contributions to the community. This will include an anonymized endoscopic video
       dataset with corresponding FBG sensor data—a first-of-its-kind resource for developing
       and benchmarking luminal SLAM algorithms. We will also release the source code for
       our core sensor fusion and force-reactive planning modules, enabling other researchers
       to build upon our work.
    • Community Building and Collaboration: I will actively engage with the international
       research community by presenting our findings at conferences and workshops. In the
       final year of the fellowship, I plan to organize a workshop on "The Future of
       Autonomous Endoscopy" at a major robotics conference, bringing together leading
       researchers, clinicians, and industry partners to foster collaboration and define future
       research directions.

4.3 Pathway 2: Clinical Translation and Improvement of Patient Care
The ultimate goal of this research is to improve patient outcomes. Our clinical translation
pathway is designed to bridge the gap between the engineering laboratory and the endoscopy
suite.
    • Early and Integrated Clinical Partnership: This project will be conducted in close
       collaboration with the Department of Gastroenterology. We will establish a formal
       advisory board, including senior endoscopists, to provide continuous feedback
       throughout the design, development, and validation phases. This ensures that our
       engineering solutions are clinically relevant, address real-world challenges, and are
       designed to integrate smoothly into existing clinical workflows.
   •   A Phased Approach to Clinical Trials: The successful completion of this project will
       yield a system validated at Technology Readiness Level 4 (TRL 4). This will provide
       the robust pre-clinical data necessary to secure follow-on funding (e.g., via RGC
       Theme-based Research Scheme or the Innovation and Technology Fund - ITF) for the
       next critical phase: in-vivo large animal studies. We will work with our clinical and
       veterinary partners to develop the protocol and secure ethical approval for these studies,
       which are the final step before first-in-human trials.
   •   Development of a Training Platform: The autonomous navigation system can also
       serve as an unparalleled training tool. We will leverage the developed software to create
       a high-fidelity virtual reality simulator. This simulator will allow trainee endoscopists
       to practice navigation in a safe, repeatable environment, receiving real-time feedback
       on their performance and contact forces, thereby potentially accelerating the learning
       curve and improving the quality of care.

4.4 Pathway 3: Economic and Societal Impact through Commercialization
We envision this technology as a platform with significant commercial potential, capable of
creating economic value and delivering widespread societal benefits.
    • Strategic Intellectual Property (IP) Management: The innovations in sensor fusion,
        SLAM, and force-aware control constitute valuable intellectual property. We will work
        closely with the university's Knowledge Transfer Office (KTO) from the project's
        inception. Our strategy is to file for at least two foundational patents: one covering the
        Visual-Shape SLAM architecture and another for the contact-free navigation algorithm
        using FBG force feedback.
    • Dual-Prong Commercialization Strategy: In the long term, we will pursue a dual-
        prong commercialization strategy. Pathway A involves licensing our patented
        technology to established global leaders in the endoscopy market (Olympus, Fujifilm,
        Boston Scientific), who have the manufacturing scale and market access to bring this
        technology to patients quickly. Pathway B, which is more ambitious, involves spinning
        off a university-backed startup company. This will allow us to retain control over the
        technology's development and build a Hong Kong-based company at the forefront of
        medical robotics. We will actively engage with the Hong Kong Science and Technology
        Parks (HKSTP) and other incubation programs.
    • Public Health and Societal Benefit: By making gastroscopy safer, less uncomfortable,
        and more reliable, our technology can significantly increase patient uptake of and
        adherence to gastric cancer screening programs. The system's autonomy can help
        standardize the quality of care, ensuring that patients receive a high-quality examination
        regardless of the operator's experience level. This can lead to earlier cancer detection,
        improved patient outcomes, and a reduction in the long-term economic burden of cancer
        on the healthcare system.
References
I. Clinical Context & Robotic Endoscopy
[1] F. Bray, et al., "Global cancer statistics 2018: GLOBOCAN estimates of incidence and
mortality worldwide for 36 cancers in 185 countries," CA: A Cancer Journal for Clinicians, vol.
68, no. 6, pp. 394–424, 2018.
[2] A. R. Pimenta-Melo, et al., "Missing rate for gastric cancer during upper gastrointestinal
endoscopy: a systematic review and meta-analysis," European Journal of Gastroenterology &
Hepatology, vol. 28, no. 9, pp. 1041–1049, 2016.
[3] J. Burgner-Kahrs, D. C. Rucker, and H. Choset, "Continuum Robots for Medical
Applications: A Survey," IEEE Transactions on Robotics, vol. 31, no. 6, pp. 1261-1280, 2015.
[4] P. Valdastri, et al., "A new paradigm for robotic colonoscopy: the Magnetic Flexible
Endoscope," in Proc. IEEE Int. Conf. Robot. Autom. (ICRA), 2012, pp. 1199-1206.

II. Core Technology: Continuum Robot Modeling & Control
[5] Y. Chen, S. Yao, M. Q.-H. Meng, and L. Liu, "Chained Spatial Beam Constraint Model: A
General Kinetostatic Model for Tendon-Driven Continuum Robots," IEEE/ASME Transactions
on Mechatronics, vol. 29, no. 5, pp. 3534-3545, 2024.
[6] S. Yao, P. Luo, Y. Yue, Y. Chen, H. Yan and M. Q.-H. Meng, "Adaptive Load-Dependent
Sim2Real Framework for Path Tracking Toward Tendon-Driven Continuum
Robots," IEEE/ASME           Transactions       on     Mechatronics,       2025,       doi:
10.1109/TMECH.2025.3566053.
[7] D. C. Rucker and R. J. Webster III, "Statics and Dynamics of Continuum Robots With
General Tendon Routing and External Loading," IEEE Transactions on Robotics, vol. 27, no.
6, pp. 1033-1044, 2011.
[8] S. Lilge and J. Burgner-Kahrs, "Kinetostatic Modeling of Tendon-Driven Parallel
Continuum Robots," IEEE Transactions on Robotics, vol. 39, no. 2, pp. 1563-1579, 2023.

III. Core Technology: Sensing for Navigation and Interaction
[9] S. Yao, R. Tang, L. Bai, H. Yan, H. Ren, and L. Liu, "An RNN-LSTM Enhanced Compact
and Affordable Micro Force Sensing System for Interventional Continuum Robots With
Interchangeable End-Effector Instruments," IEEE Transactions on Instrumentation and
Measurement, vol. 72, Art. no. 9003513, 2023.
[10] A. Gao, et al., "Body Contact Estimation of Continuum Robots With Tension-Profile
Sensing of Actuation Fibers," IEEE Transactions on Robotics, vol. 40, pp. 1492-1508, 2024.
[11] Y. L. Park, et al., "Real-time estimation of 3-D shape and contact forces of a surgical
instrument using an FBG-based sensing module," IEEE Transactions on Biomedical
Engineering, vol. 58, no. 1, pp. 110-120, 2010.
[12] S. Yao, P. Luo, L. Liu, H. Yan, and M. Q.-H. Meng, "Fast-Adaptive Permanent Magnetic
Positioning-based Navigation Framework for Continuum Robots in Colonoscopic
Biopsy," IEEE/ASME           Transactions       on        Mechatronics,      2025,         doi:
10.1109/TMECH.2025.3535940.
[13] A. M. Franz, et al., "Electromagnetic Tracking in Medicine—A Review of Technology,
Validation, and Applications," IEEE Transactions on Medical Imaging, vol. 33, no. 8, pp. 1702-
1725, 2014.
[14] M. Turan, et al., "Endo-SLAM: a dense reconstruction and localization framework for
colonoscopy," in Proc. Med. Image Comput. Comput. Assist. Interv. (MICCAI), 2018, pp. 319-
327.
[15] M. Ye, et al., "Self-supervised deep visual odometry with online adaptation for endoscopic
capsule robots," IEEE Robotics and Automation Letters, vol. 6, no. 2, pp. 2788-2795, 2021.

IV. Core Technology: Autonomous Motion Planning
[16] P. Luo, S. Yao, Y. Yue, J. Wang, H. Yan and M. Q.-H. Meng, "Efficient RRT*-based Safety-
Constrained Motion Planning for Continuum Robots in Dynamic Environments," in Proc. IEEE
Int. Conf. Robot. Autom. (ICRA), 2024, pp. 9328-9334.
[17] B. H. Meng, I. S. Godage, and I. Kanj, "RRT*-Based Path Planning for Continuum
Arms," IEEE Robotics and Automation Letters, vol. 7, no. 3, pp. 6830–6837, 2022.
[18] J. Li, et al., "Active collision avoidance for teleoperated multi-segment continuum robots
toward minimally invasive surgery," The International Journal of Robotics Research, vol. 43,
no. 7, pp. 918-941, 2024.
Past Research Experience
My research career to date has been dedicated to advancing the intelligence and capabilities of
medical robotics, with a specific focus on continuum robots for minimally invasive surgery. My
experience spans the full research lifecycle, from fundamental mechanical modeling and sensor
design to the development of advanced AI-driven control and navigation systems. My work is
unified by the goal of transforming complex medical procedures into safer, more precise, and
autonomous operations.

1. Ph.D. Research: Intelligent Continuum Robots for Autonomous Surgical Tasks (2021 –
Present)
My doctoral research at my Ph.D. institution, co-supervised by a leading roboticist from a
university in Shenzhen, has been the cornerstone of my academic journey. This work
systematically addressed the core challenges preventing the clinical deployment of autonomous
continuum robots.
     Key Contribution 1: High-Fidelity Kinetostatic Modeling. I co-developed
       the Chained Spatial Beam Constraint Model (CSBCM), a novel mechanics-based model
       for tendon-driven continuum robots. Traditional models suffered from a trade-off
       between speed and accuracy. Our CSBCM is 10 times faster than the gold-standard
       Cosserat-rod models while maintaining superior robustness and achieving a tip position
       accuracy of <1.5% of the robot’s length in experimental validations. This work,
       published in IEEE/ASME Transactions on Mechatronics, provides a robust "physics
       engine" for reliable simulation and control.
     Key Contribution 2: Adaptive Sim2Real Control. To bridge the gap between
       simulation and the unpredictable physics of the real world, I developed an adaptive,
       load-dependent control framework. This system uses an LSTM neural network to learn
       and compensate for real-world uncertainties like friction and load-dependent
       deformations. In hardware experiments, this framework reduced trajectory tracking
       errors by nearly 60%, enabling high-precision motion control. This research was
       published in IEEE/ASME Transactions on Mechatronics.
     Key Contribution 3: Autonomous Navigation in Dynamic Environments. I co-led
       the development of two novel navigation systems. The first was an efficient RRT-based
       safety-constrained motion planner* for dynamic obstacle avoidance, presented at ICRA
       2024. The second was a fast-adaptive permanent magnet-based navigation
       framework that enables real-time localization without line-of-sight. This system is 50
       times faster than conventional methods and achieves a positioning accuracy of < 2mm,
       providing a crucial "internal GPS" for autonomous navigation in confined spaces. This
       work is also published in IEEE/ASME Transactions on Mechatronics.
     Key Contribution 4: Embodied Force Perception. To give the robot a sense of touch,
       I co-developed a compact, RNN-LSTM-enhanced micro-force sensing system. This
       low-cost, 3D-printed sensor can be integrated into the robot's tip to measure interaction
       forces with high sensitivity. This work, published in IEEE Transactions on
       Instrumentation and Measurement, is critical for enabling safe and interactive surgical
       tasks.
2. Research Internship: Micro-Force Sensing and Systems Perception (The Chinese
University of Hong Kong, 2023 – 2024)
As a Honorary Research Assistant in the Department of Electronic Engineering, I focused on
the development of novel micro-force sensing technologies. My work involved designing and
fabricating soft sensors and developing machine learning models to decouple complex sensor
signals. This experience directly contributed to the force sensing research in my Ph.D. and
honed my skills in experimental design and sensor integration.

3. Research Internship: Pneumatic Artificial Muscles (North Carolina State University,
2020)
During my summer research internship at NC State University, I worked on the modeling and
analysis of pneumatic artificial muscles (PAMs). I conducted experiments to characterize the
force-length properties of PAMs and developed analytical models to predict their behavior. This
project provided me with a strong foundation in soft robotics and actuator modeling, which has
been invaluable in my subsequent work with compliant continuum robots.

4. Research Internship: Nano-Precision Mechanical Fabrication (Tohoku University,
Sendai, Japan, 2019)
My summer research internship at Tohoku University's Nano-Precision Mechanical Fabrication
Lab provided me with critical expertise in advanced manufacturing and material science. I
worked on a project focused on body-implanted metallic glass processing methods. My
responsibilities included operating high-precision fabrication equipment, characterizing the
mechanical properties of novel biocompatible materials, and analyzing their potential for in-
vivo medical devices. This hands-on experience in micro-fabrication and biomaterials is
directly relevant to my proposed work, particularly for the fabrication and integration of
miniaturized sensors for the robotic gastroscope.

5. Early Research Experience
     Internship, Mechanical Design (Milebot Robotics, 2020): I completed an internship
       where I contributed to the design of passive exoskeletons for lower limbs, gaining
       practical experience in mechanical design, CAD modeling, and system integration in an
       industrial R&D setting.
     Undergraduate Research, Soft Robotics (2019 – 2020): As an Undergraduate
       Research Assistant in the Advanced Actuator and Robotics Lab at a leading university
       in Shenzhen, I contributed to a project focused on the design and application of soft
       swab robots. This early research experience ignited my passion for medical robotics and
       provided me with hands-on skills in robot design and fabrication.
Throughout my academic career, I have actively sought opportunities to develop my leadership
and mentorship skills, both within and outside the research environment. I believe that
leadership is about enabling others to succeed, fostering a collaborative spirit, and taking
initiative to drive projects forward.
Academic Leadership and Mentorship
      Mentorship of Junior Researchers (2023 – 2024): During the final years of my Ph.D.
         program, I took on a significant mentorship role for several master’s and final-year
         undergraduate students working on projects related to my research. My responsibilities
         included:
             o Project Scoping and Guidance: I guided students in defining their research
                 topics, setting achievable milestones, and developing their experimental plans.
                 For example, I mentored a Master's student on a project focused on real-time
                 force feedback control, which led to a successful thesis project.
             o Technical Training: I provided hands-on training in key technical areas,
                 including continuum robot fabrication, experimental setup using ROS, and data
                 analysis using Python.
             o Fostering a Collaborative Environment: I initiated and led weekly sub-group
                 meetings where junior members could present their progress, discuss challenges,
                 and receive feedback from peers in a supportive setting. This initiative improved
                 the productivity and morale of the team.
      Project Leadership in National Innovation Program (2020 – 2021): I served as
         the Project Leader for our team in the National Undergraduate Training Program for
         Innovation and Entrepreneurship. This was a highly competitive, nationally funded
         program. My role involved:
             o Conceptualizing and Proposing the Project: I led the initial brainstorming,
                 literature review, and proposal writing process, which successfully
                 secured $7,400 in funding. Our project was ranked in the Top 3 out of 190
                 participating teams.
             o Team Management and Task Delegation: I was responsible for coordinating
                 a team of four students, delegating tasks based on individual strengths, and
                 ensuring that we met all project deadlines and deliverables.
             o Communication and Reporting: I acted as the primary liaison with the
                 program coordinators and our faculty advisor, preparing regular progress reports
                 and delivering the final project presentation.
Leadership Outside Academia
      Vice-President of the Student Union (2018 – 2019): I was elected as the Vice-
         President of the Student Union at my undergraduate institution's college. In this role, I
         was responsible for organizing large-scale campus events, managing a budget, and
         representing student interests to the faculty. This experience enhanced my
         organizational, communication, and public speaking skills.
      College Football Team Captain (2018, 2020): I was honored to be selected as the
         captain of my college football team. As captain, my role extended beyond on-field
         performance. I was responsible for motivating my teammates, organizing training
         sessions, and developing game strategies. Leading the team to win the Championship
        of the Shenzhen University Football League was a testament to our teamwork, discipline,
        and shared commitment—qualities that I believe are directly transferable to leading a
        successful research team.
These experiences have equipped me with the skills to not only conduct independent research
but also to mentor junior scientists, manage complex projects, and foster a collaborative and
high-achieving research environment. I am confident that I can bring this leadership potential
to my postdoctoral research and contribute positively to the academic community.
                                                 RGC Reference Number :              To be assigned by the University




                             RESEARCH GRANTS COUNCIL (RGC)
                     JUNIOR RESEARCH FELLOW SCHEME (JRFS)
                                  Academic Referee’s Report Form
                                       (CONFIDENTIAL)

General Notes:
    The supporting university should provide the nominee its contact information below, for
     each referee to return the completed referee’s report directly to the university by post/ fax
     (under confidential cover) or e-mail.
    The nominee should complete Part A of the referee’s report.
    The nominee should forward one copy of this referee’s report (including this cover page)
     to each of his/ her two academic referees* for completion of Part B.
    Part B of the referee’s report should be entirely written by the academic referee, who
     should return the completed report (including this cover page) directly to the supporting
     university for inclusion in the submission of nomination.
    The supporting university should fill in the RGC Reference Number at the top right
     corner of this page after receipt of the completed referee’s report.


Name of Supporting University:       The Chinese University of Hong Kong
                                     (Faculty of Engineering)
Address:     Faculty Office of Engineering
              Rm. 606, 6/F, Ho Sin Hang Engineering Building
              The Chinese University of Hong Kong
              Shatin, N.T., Hong Kong
Fax: (852) 2603 5701
E-mail: <EMAIL>

Submission Deadline: 5:00 p.m. 1 September 2025

 * - The referee’s report should be completed in English.
    - The two academic referees should be familiar with the nominee’s academic achievements and
      research ability / potential.
    - Proposed postdoctoral supervisor of the nominee during the fellowship period is not allowed
      to be the nominee’s academic referee to provide this report.
    - Persons from non-academic background are considered not appropriate to act as academic
      referees.

 Declaration by the Supporting University:
 The supporting university has verified that this report is submitted directly by the academic referee concerned.

                                                      1
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
Part A    Nominee’s Particulars           (to be completed by the nominee)
Name of Nominee :       (Surname) Shilong YAO
                        (Other name)
Host Faculty / School / Department /    Faculty of Engineering, Dept of Electronic Engineering
Division / Unit ^ :
^ Please do not mention the name of the supporting university


Part B    Academic Referee’s Report            (to be completed by the referee)
Notes to Referee:
   The nominee named above is being nominated for the RGC Junior Research Fellow
    Scheme (JRFS) by the supporting university named on the cover page.
   Proposed postdoctoral supervisor of the nominee during the fellowship period is not
    allowed to be the nominee’s academic referee to provide this report.
   Please complete Part B of this report in English and return the completed report
    (including the cover page) directly to the supporting university.
   Please refrain from mentioning the name of the supporting university to enable blind
    review to be conducted.
   All information given here will be treated as strictly confidential. It will be accessible
    only to the JRFS Selection Panels and those who are responsible for processing this
    nomination within the named university and the RGC.
   Please provide genuine and sufficient information in the report to facilitate the Selection
    Panel in evaluating the nominee’s research ability and potential.
   All information submitted shall be handled in accordance with the requirements
    stipulated by the Personal Data (Privacy) Ordinance in Hong Kong.

1. How long and in what capacity have you known the nominee?

4 years. He has been a PhD student in our research group.




                                                       2
                                                                JRFS-3 Academic Referee’s Report Form (Aug 2025)
2. How would you rate the following attributes of the nominee?
   (Please tick as appropriate)
                                           Excellent           Very Good        Good                             Average              No basis
                                           (top 5%)            (top 6-20%) (top 21-50%)                         or below                for
                                                                                                               (lower than           judgment
                                                                                                                  50%)
Intellectual capacity                                                ☐                       ☐                       ☐                    ☐
                                                 ☐                                            ☐                       ☐                    ☐
Ability for conducting
scholastic research
                                                                       
                                                                       ☐                      ☐                       ☐                    ☐
Knowledge of proposed
research study
                                                 

                                                                       ☐                      ☐                       ☐                    ☐
Analytical power and
reasoning
                                                 
Judgment                                                              ☐                      ☐                       ☐                    ☐
                                                 ☐                                            ☐                       ☐                    ☐
Imagination and
originality
                                                                       

                                                                       ☐                      ☐                       ☐                    ☐
Motivation and
perseverance
                                                 

                                                 ☐                                            ☐                       ☐                    ☐
Skills of writing and
argumentation
                                                                       

                                                 ☐                                            ☐                       ☐                    ☐
Capacity for independent
work
                                                                       

                                                                       ☐                      ☐                       ☐                    ☐
Reliability and sense of
responsibility
                                                 
Leadership experience                            ☐                                           ☐                      ☐                     ☐

3. Please describe the research ability and potential of the nominee, and explain any other
   aspects of the nominee’s experience and skills that are relevant to JRFS.
   (Please refrain from mentioning the name of the supporting university. Please attach
   separate sheet(s) if more space is required.)
It’s a pleasure for me to write in support of Shilong Yao’s application for a research position at your institution. I have known Shilong for the past
four years during his doctoral studies in our group at City University of Hong Kong.

From my observations, Shilong has consistently demonstrated a strong academic aptitude and a commendable work ethics. He approached his
coursework with diligence and a genuine desire to master the fundamental concepts. He is a meticulous and rigorous student, showing a high level of
seriousness and responsibility towards his academic work.

What has particularly stood out to me is Shilong's intellectual curiosity and his role as a collaborative member within our research group. While his
main project focused on continuum robotics, he frequently engaged in discussions with peers from different research directions, especially on topics
related to learning-based methods. I have often observed him offering thoughtful insights on how to apply data-driven techniques to solve complex
robotics problems. His ability to understand challenges outside his immediate domain and to communicate technical ideas clearly makes him a
valuable collaborator.

In summary, Shilong is a mature and reliable individual. He possesses the necessary technical skills, demonstrated through his project work, and just
as importantly, the intellectual curiosity and collaborative mindset required to thrive in a high-level research environment like yours.

I am confident that Shilong will be a productive and valuable member of your team, and I am pleased to give him my strong recommendation for this
position. Please feel free to contact me if you have any questions.



                                                                            3
                                                                                         JRFS-3 Academic Referee’s Report Form (Aug 2025)
4. What is your overall recommendation for this nominee?

                    Recommend very strongly
              ☐      Recommend strongly
              ☐      Recommend with reservation
              ☐      Do not recommend

 Name of Referee: Hong Yan                                        Title: Professor
                         (in BLOCK letters)                                (Prof / Dr / Mr / Ms / Mrs)*

 University / Organisation: City University of Hong Kong
 Position: Chair Professor of Computer Engineering
 E-mail Address: <EMAIL>
 Telephone Number#: +852-3442-7375
 (# Please provide country code and area code, e.g. +86-10-1234567)
  * Please delete as appropriate


Declaration by the Referee:
I hereby declare that
 I am not the proposed postdoctoral supervisor of the nominee during the fellowship period.
☐ I have no conflict(s) of interest in the capacity of academic referee in this nomination.


 Signature: Hong Yan                                         Date: 31/08/2025




This document is strictly CONFIDENTIAL. Please send it directly to the supporting
university (under confidential cover) whose details are given on the cover page.




                                                      4
                                                               JRFS-3 Academic Referee’s Report Form (Aug 2025)
